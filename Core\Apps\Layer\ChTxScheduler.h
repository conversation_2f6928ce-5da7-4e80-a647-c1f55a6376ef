#ifndef __CHTXSCHEDULER_H__
#define __CHTXSCHEDULER_H__

#include "DataType.h"
#include "AllConst.h"
#include "ChannelMgr.h"
#include "FrameMapMgr.h"

class CChTxScheduler
{
public:    
    enum consts
    {
        MAX_ITDMA_TXCNT = 10,
    };

    CChTxScheduler(CChannelMgr *pChannel);
    ~CChTxScheduler();

public:
    void    Initialize();

    void    SetChReportInterval(int nTotalNI, float fChReportIntervalSec);
    void    SetChNewReportIntervalSec(float fReportIntervalSec);
    void    SetStaticReportIntervalSec(DWORD dwStaticIntervalSec);
    void    SetLongRangeReportInterval(UINT16 uLongRangeIntervalSec);
    void    InitLongRangeReportSecCh(DWORD dwSec);
    void    SetNSS(INT16 uSlotID);
    void    InitStaticReportSecCh(DWORD dwSec);
    INT16   GetNSS();
    void    IncNS();
    int     CalcNS(const int nTxCnt);
    void    SetNextStartSI(int nSI);
    void    ResetNextStartSI();
    void    IncNextStartSI();
    void    DecNextStartSI();
    BOOL    CheckRRvalid();
    void    ShiftFrameIndex(INT16 nShiftSlot);

    void    SetPosMsgSlotColumnRA(int wNextTxSlotID, int nSlotOffset, int nPosMsgID, int nSlotTimeOut=TMO_UNKNOWN);
    int     SetPosSlotColumnCheckTmoSO(int wTxSlotID, int nPosMsgID, int nCurStartSI, int nSizeSI, BOOL bCheckTmoZero);

    BOOL    ProcessPeriodicStaticVoyageMsgTx(BOOL bTxUnconditionally);
    BOOL    ProcessLongRangeMsgTx();
    void    SetNextTempItdmaSI();
    BOOL    SetTempUseReportByITDMA_Ch(BOOL bSet);
    BOOL    ProcessTempUseReportByITDMA_Ch();

    BOOL    AllocUnscheduledMultiSlotMsg(UINT16 uMsgID, WORD wStartSI, BYTE uNumSlot, BOOL bCheckSoTdmaSlot, BYTE nBuffIdx, INT16 nSizeSI=RATDMA_SI, int *pnTxSlot=NULL, BOOL bFailNoExistingSO=FALSE);
    int     UrgencyAllocSO(int nMsgID, const int nStartSI, const int nSizeSI);

    BOOL    ProcessPhaseNetworkEntry(int nEntrySlot, int nNSS);
    void    ProcessPhaseFirstFrame();
    void    ProcessPhaseRoutine();
    void    ProcessPhaseRoutine_SO();
    void    ProcessPhaseRoutine_IT();
    void    TerminatePosReportSO();
    void    TerminatePosReportITDMA();
    BOOL    ProcessPhaseChgReportRateFirst();
    BOOL    ProcessPhaseChgReportRateFirst_MasterCH();
    BOOL    ProcessPhaseChgReportRateFirst_SlaveCH();
    void    ProcessPhaseChgReportRateSecond();
    BOOL    RunPhaseAssignedSlot(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD wIncrementTotal, WORD wIncrementCh, int nTimeOut);
    void    ReissueSlotAssignedMode(WORD wFrSlotIDStart, int nTimeOut);
    void    TerminateSlotAssignedMode(WORD wLastAssignedTxSlot);

    void    ResetLastScheduledTxSlotID();
    void    ProcessTransmit();
    BOOL    TransmitFrameMapSlot(WORD wSlotID);

    void    ProcOpPhase();
    void    ProcessSlotChangeTime_Scheduler();

    void    RunProcessTxScheduler();
    void    RunPeriodicallyTxScheduler();

    static  void    SetAssignedRRTimeOut();

public:
    CChannelMgr     *m_pChannel;

    float   m_fChReportIntervalSec;
    float   m_fChReportRate;
    float   m_fChReportRateHalf;

    int     m_nSizeSI;
    int     m_nSizeHalfSI;
    int     m_nNI;

    float   m_fNewReportIntervalSecSO;

    INT16   m_nNSS;                                    // 0 ~ MAX_SLOT_FRAMEMAP, ÀüÃ¼ ½½·Ô¸Ê »ó¿¡¼­ÀÇ slot ID
    INT16   m_nNS;                                    // 0 ~ MAX_SLOT_FRAMEMAP, ÀüÃ¼ ½½·Ô¸Ê »ó¿¡¼­ÀÇ slot ID
    INT16   m_nNextStartSI;                            // 0 ~ MAX_SLOT_FRAMEMAP, ÀüÃ¼ ½½·Ô¸Ê »ó¿¡¼­ÀÇ slot ID

    INT8    m_nAddCntTempReportITDMA;                // ÄÚ½ºº¯È­¿¡ µû¸¥ ITDMA Ãß°¡¼Û½ÅÇØ¾ßÇÒ È¸¼ö 0 ~ 2
    int     m_nNextItdmaTxCnt;
    int     m_nItdmaStartSI;
    int     m_nItdmaNI;
    int     m_nItdmaSizeSI;
    int     m_nNextItdmaTxSlotId;

    int     m_nFirstFrameTxCnt;
    INT16   m_nFirstFrameTxSlotId;                    // 0 ~ MAX_SLOT_FRAMEMAP, ÀüÃ¼ ½½·Ô¸Ê »ó¿¡¼­ÀÇ slot ID

    int     m_nChangeRRphaseTxCnt;
    INT16   m_nChangeRRphaseTxSlotId;                // 0 ~ MAX_SLOT_FRAMEMAP, ÀüÃ¼ ½½·Ô¸Ê »ó¿¡¼­ÀÇ slot ID

    DWORD   m_dwStaticReportIntervalSec;
    DWORD   m_dwStaticReportNextSec;

    DWORD   m_dwLongRangeReportIntervalSec;
    DWORD   m_dwLongRangeTxNextSec;

    INT16   m_nLastScheduledTxSlotID;

    DWORD   m_dwSendPosReportTick;

    DWORD   m_dwRoutineAllocLastCheckSec;
    WORD    m_wRoutineAllocLastCheckSlot;
    int     m_nRoutinePosMsgID;

    DWORD   m_dwChLastPosTxSec;

    int     m_nRoutineNextTxSlot;
    int     m_nUrgencyTxSlotID;

    static  int     m_nRRChgTimeOutMin;
    static  DWORD   m_dwRRTimeOutStartTick;
    static  DWORD   m_dwRRTimeOutMS;
    static  int     m_nRRChgSlaveNewSI;
};
#endif//__CHTXSCHEDULER_H__
