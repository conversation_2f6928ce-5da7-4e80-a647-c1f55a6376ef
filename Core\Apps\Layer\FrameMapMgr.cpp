#include <stdlib.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Timer.h"
#include "Ship.h"
#include "ReportRateMgr.h"
#include "VdlRxMgr.h"
#include "AisModem.h"
#include "UserDirMgr.h"
#include "ChannelMgr.h"
#include "LayerNetwork.h"
#include "SetupMgr.h"
#include "SlotMgr.h"
#include "SensorMgr.h"
#include "FrameMapMgr.h"

const int NUM_SEARCH_SLOTS = NUM_SLOT_PER_FRAME;
static FRAMEMAP_SLOTDATA *gpuMsgMapOneFrame = NULL;

CFrameMapMgr::CFrameMapMgr(CChannelMgr *pChannel)
{
    m_pChannel =  pChannel;
    m_pFrameMap = (FRAMEMAP_SLOTDATA*)SysAllocMemory(sizeof(FRAMEMAP_SLOTDATA) * NUM_SLOT_FRAMEMAP);

    if(!gpuMsgMapOneFrame)
        gpuMsgMapOneFrame = (FRAMEMAP_SLOTDATA*)SysAllocMemory(sizeof(FRAMEMAP_SLOTDATA) * NUM_SEARCH_SLOTS);

    m_nFrameMapStartSlotID = 0;

    ClearFrameMap();
}

CFrameMapMgr::~CFrameMapMgr()
{
}

void CFrameMapMgr::ClearFrameMap()
{
    memset(m_pFrameMap, 0, sizeof(FRAMEMAP_SLOTDATA) * NUM_SLOT_FRAMEMAP);
}

void CFrameMapMgr::ShiftFrameMap(INT16 nShiftOffset)
{
    int nOldStartSlotID = m_nFrameMapStartSlotID;

    m_nFrameMapStartSlotID -= nShiftOffset;
    if(abs(m_nFrameMapStartSlotID) >= NUM_SLOT_FRAMEMAP)
        m_nFrameMapStartSlotID %= NUM_SLOT_FRAMEMAP;

    DEBUG_LOG("ShiftFrameMap] shift : %d, offset : %d -> %d\r\n", nShiftOffset, nOldStartSlotID, m_nFrameMapStartSlotID);
}

WORD CFrameMapMgr::GetShiftedMapSlotID(WORD wMapSlotID)
{
    return FrameMapSlotIdAdd(wMapSlotID, m_nFrameMapStartSlotID);
}

WORD CFrameMapMgr::GetShiftedMapSlotID(WORD wFrameID, WORD wSlotID)
{
    WORD wMapSlotID = GetFrameMapSlotID(wFrameID, wSlotID);
    return GetShiftedMapSlotID(wMapSlotID);
}

WORD CFrameMapMgr::GetSlotIdFromFrameSlotID(WORD wMapSlotID)
{
    if(wMapSlotID == SLOTID_NONE)
        return SLOTID_NONE;

    return (wMapSlotID % NUM_SLOT_PER_FRAME);
}

FRAMEMAP_SLOTDATA* CFrameMapMgr::GetSlotDataPtr(WORD wFrSlotID)
{
    WORD wMapSlot = SLOTID_NONE;
    if(wFrSlotID != SLOTID_NONE)
    {
        wMapSlot = GetShiftedMapSlotID(wFrSlotID);

        if(CheckValidFrameMapSlotID(wMapSlot))
            return &m_pFrameMap[wMapSlot];
    }

    return NULL;
}

BOOL CFrameMapMgr::CheckSlotIntAlloc(FRAMEMAP_SLOTDATA *pIntSlot)
{
    return (cShip::getOwnShipInst()->xStaticData.dMMSI != AIS_AB_MMSI_NULL && pIntSlot->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI);
}

BOOL CFrameMapMgr::CheckSlotIntAlloc(WORD wFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(!pSlotPtr)
        return FALSE;

    return CheckSlotIntAlloc(pSlotPtr);
}

BOOL CFrameMapMgr::CheckSlotExtAlloc(FRAMEMAP_SLOTDATA *pSlotPtr)
{
    return (pSlotPtr->uMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI != cShip::getOwnShipInst()->xStaticData.dMMSI);
}

BOOL CFrameMapMgr::CheckSlotExtAlloc(WORD wFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(!pSlotPtr)
        return FALSE;

    return CheckSlotExtAlloc(pSlotPtr);
}

void CFrameMapMgr::PrepToChangeFrMMSI(UINT uNewMMSI)
{
    if(cShip::getOwnShipInst()->xStaticData.dMMSI != AIS_AB_MMSI_NULL)
    {
        for(int i = 0 ; i <= MAX_SLOT_FRAMEMAP ; i++)
        {
            if(m_pFrameMap[i].uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
                m_pFrameMap[i].uMMSI = uNewMMSI;
        }
    }
}

void CFrameMapMgr::SetFrameMapSlotData(BOOL bCheckProtect, WORD wFrSlotID, INT8 nNumSlots, UINT uMMSI, BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                        UINT16 uMsgID, BOOL bStartSlot, UINT uSlotOffset, BOOL bItdmaKeepFlag, BOOL bProtectAssignedModeSlot, BYTE nDataIdx, BOOL bFAtoSO, WORD wTxChNum)
{
    if(wFrSlotID == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(!pSlotPtr)
        return;

    WORD wSlotID = GetSlotIdFromFrameSlotID(wFrSlotID);

    if(wTxChNum == AIS_CH_NUM_NONE)
        wTxChNum = m_pChannel->m_uChNumTx;

    if(bCheckProtect)
    {
        if(uMMSI != AIS_AB_MMSI_NULL && uMMSI != pSlotPtr->uMMSI && cShip::getOwnShipInst()->xStaticData.dMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
        {
            if(uMsgID == AIS_MSG_NO_ID_20 && IsValidMMSI_BaseSt(uMMSI) && (CUserDirMgr::getInst()->IsBaseStationWithin120NM(uMMSI) || CSensorMgr::getInst()->IsGnssLost()))
            {
                WARNING_LOG("SetMap] Error! Own Slot is stolen by BS,Msg-20, S: %d(%d), %09d -> %09d, M : %d, TMO : %d -> %d, s:%d\r\n",
                    wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, pSlotPtr->nSlotTimeOut, nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
            }
            else
            {
                WARNING_LOG("SetMap] WARN! Own Slot is about to be stolen but ignored, S: %d(%d), %09d -> %09d, M : %d -> %d, TMO : %d, s:%d\r\n",
                    wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, uMsgID, pSlotPtr->nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
                return;
            }
        }

        if(uMMSI != AIS_AB_MMSI_NULL && uMMSI != pSlotPtr->uMMSI && IsValidMMSI_BaseSt(pSlotPtr->uMMSI) && pSlotPtr->bCommStateScheme == TDMA_FATDMA)
        {
            ERROR_LOG("SetMap] Error! FATDMA slot is about to overwitten by other BaseST but ignore, S: %d(%d), %09d -> %09d, M : %d -> %d, TMO : %d, s:%d\r\n",
                wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, uMsgID, pSlotPtr->nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
            return;
        }
    }

    if((pSlotPtr->uMsgID == AIS_MSG_NO_ID_20 || pSlotPtr->bFAtoSO) && uMMSI != AIS_AB_MMSI_NULL && uMMSI != pSlotPtr->uMMSI &&
        cShip::getOwnShipInst()->xStaticData.dMMSI != AIS_AB_MMSI_NULL && uMMSI != cShip::getOwnShipInst()->xStaticData.dMMSI && pSlotPtr->uMMSI != cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        ERROR_LOG("SetMap] xxxxxxxxxxxxxxxxxxxx Error! MSG-20 or FAtoSO slot is about to overwitten but ignore, S: %d(%d), %09d -> %09d, M : %d -> %d, FA: %d -> %d, TMO : %d -> %d, s:%d\r\n",
                wFrSlotID, wSlotID, pSlotPtr->uMMSI, uMMSI, pSlotPtr->uMsgID, uMsgID, pSlotPtr->bFAtoSO, bFAtoSO, pSlotPtr->nSlotTimeOut, nSlotTimeOut, cTimerSys::getInst()->GetCurTimerSec());
        return;
    }

    pSlotPtr->uMMSI             = uMMSI;
    pSlotPtr->nDataIdx          = nDataIdx;
    pSlotPtr->bSlotStat         = bStatus;
    pSlotPtr->nSlotTimeOut      = nSlotTimeOut;
    pSlotPtr->bAccessScheme     = bAccessScheme;
    pSlotPtr->bCommStateScheme  = bCommStateScheme;
    pSlotPtr->uMsgID            = uMsgID;
    pSlotPtr->uNumSlots         = nNumSlots;
    pSlotPtr->bStartSlot        = bStartSlot;
    pSlotPtr->uSlotOffset       = uSlotOffset;
    pSlotPtr->bItdmaKeepFlag    = bItdmaKeepFlag;
    pSlotPtr->bFAtoSO           = bFAtoSO;
    pSlotPtr->wTxChNum          = wTxChNum;
}

void CFrameMapMgr::ClearFrameMapSlotData(FRAMEMAP_SLOTDATA *pSlotPtr)
{
    if(!pSlotPtr)
        return;

    pSlotPtr->uMMSI             = AIS_AB_MMSI_NULL;
    pSlotPtr->bSlotStat         = SLOTSTAT_FREE;
    pSlotPtr->nSlotTimeOut      = 0;
    pSlotPtr->bAccessScheme     = POS_REPORT_UNSCHEDULED;
    pSlotPtr->bCommStateScheme  = TDMA_NONE;
    pSlotPtr->uMsgID            = AIS_MSG_NO_ID_UNDEFINED;
    pSlotPtr->uNumSlots         = 0;
    pSlotPtr->bStartSlot        = FALSE;
    pSlotPtr->uSlotOffset       = 0;
    pSlotPtr->bItdmaKeepFlag    = FALSE;
    //pSlotPtr->pData            = NULL;
    pSlotPtr->nDataIdx          = 0;
    pSlotPtr->bFAtoSO           = FALSE;
    pSlotPtr->wTxChNum          = AIS_CH_NUM_NONE;
}

void CFrameMapMgr::ClearFrameMapSlotData(int nFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotPtr = GetSlotDataPtr(nFrSlotID);
    ClearFrameMapSlotData(pSlotPtr);
}

void CFrameMapMgr::SetFrameMapOneMsg(BOOL bCheckProtect, WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                    UINT16 uMsgID, UINT16 uSlotOffset, BOOL bItdmaKeepFlag, BOOL bProtectAssignedModeSlot, BYTE nDataIdx, BOOL bFAtoSO)
{
    SetFrameMapSlotData(bCheckProtect, wFrSlotID, nNumSlot, uMMSI, bStatus, nSlotTimeOut, bAccessScheme, bCommStateScheme,
                        uMsgID, TRUE, uSlotOffset, bItdmaKeepFlag, bProtectAssignedModeSlot, nDataIdx, bFAtoSO);
    wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, 1);

    for(int i = 1 ; i < nNumSlot ; i++)
    {
        SetFrameMapSlotData(bCheckProtect, wFrSlotID, nNumSlot, uMMSI, bStatus, nSlotTimeOut, bAccessScheme, bCommStateScheme,
                            uMsgID, FALSE, uSlotOffset, bItdmaKeepFlag, bProtectAssignedModeSlot, nDataIdx, bFAtoSO);
        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, 1);
    }
}

void CFrameMapMgr::SetFrameMapOneMsgColumn(BOOL bCheckProtect, const WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, BYTE bSlotStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                            UINT16 uMsgID, UINT16 uSlotOffset, BOOL bItdmaKeepFlag, BOOL bProtectAssignedModeSlot, BYTE nDataIdx, BOOL bFAtoSO)
{
    if(wFrSlotID == SLOTID_NONE)
        return;

    if(nNumSlot <= 0)
        return;

    WORD wSlotID = wFrSlotID;
    while(nSlotTimeOut >= 0)
    {
        SetFrameMapOneMsg(bCheckProtect, wSlotID, nNumSlot, uMMSI, bSlotStatus, nSlotTimeOut, bAccessScheme, bCommStateScheme,
                            uMsgID, uSlotOffset, bItdmaKeepFlag, bProtectAssignedModeSlot, nDataIdx, bFAtoSO);

        wSlotID = FrameMapSlotIdAdd(wSlotID, NUM_SLOT_PER_FRAME);
        nSlotTimeOut--;
    }
}

void CFrameMapMgr::FreeFrameMapSlotRowByCnt(WORD wStartSlotID, int nNumSlot)
{
    FRAMEMAP_SLOTDATA *pSlotPtr;

    for(int i = 0 ; i < nNumSlot ; i++)
    {
        pSlotPtr = GetSlotDataPtr(wStartSlotID);
        ClearFrameMapSlotData(pSlotPtr);
        wStartSlotID = FrameMapSlotIdAdd(wStartSlotID, 1);
    }
}

void CFrameMapMgr::FreeFrameMapSlotRowByRange(WORD wStartSlotID, WORD wEndSlotID)
{
    int nCnt = FrameMapGetDiffSlotID(wStartSlotID, wEndSlotID);
    FreeFrameMapSlotRowByCnt(wStartSlotID, nCnt);
}

int CFrameMapMgr::FreeFrameMapOneMsg(WORD wFrSlotID)
{
    if(wFrSlotID == SLOTID_NONE)
        return 1;

    // 자선에 의해 할당한 슬롯이 아닌 경우 bStartSlot와 상관없이 1Slot씩 Clear하도록 수정함. 
    // 그렇지 않으면 실제 송신 후보에 포함되지 않아야 하는 슬롯이 송신 슬롯 후보로 포함되는 경우 발생함.
    FRAMEMAP_SLOTDATA *pStartSlotPtr = GetSlotDataPtr(wFrSlotID);
    if(pStartSlotPtr->bStartSlot && pStartSlotPtr->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        FreeFrameMapSlotRowByCnt(wFrSlotID, pStartSlotPtr->uNumSlots);
        return pStartSlotPtr->uNumSlots;
    }

    ClearFrameMapSlotData(pStartSlotPtr);
    return 1;
}

void CFrameMapMgr::FreeFrameMapMsgColRow(const WORD wFrSlotIDToFree, int nNumColToCheck, UINT uMMSI)
{
    const int NUM_CHECK_FRAME = NUM_FRAME_FRAMEMAP;

    FRAMEMAP_SLOTDATA *pSlotPtr;

    if(wFrSlotIDToFree == SLOTID_NONE)
        return;

    WORD wColSlotID = wFrSlotIDToFree;
    int nTmo;
    for(int i = 0 ; i < nNumColToCheck ; i++)
    {
        WORD wRowSlotID = wColSlotID;
        for(int j = 0 ; j < NUM_FRAME_FRAMEMAP ; j++)
        {
            pSlotPtr = GetSlotDataPtr(wRowSlotID);
            nTmo = pSlotPtr->nSlotTimeOut;

            if(uMMSI == AIS_AB_MMSI_NULL || pSlotPtr->uMMSI == uMMSI)  // 연속 프레임에서 모두 찾아 해제해야한다.
            {
                FreeFrameMapOneMsg(wRowSlotID);
            }
            if(nTmo <= 0)
                break;

            wRowSlotID = FrameMapSlotIdAdd(wRowSlotID, NUM_SLOT_PER_FRAME);
        }
        wColSlotID = FrameMapSlotIdAdd(wColSlotID, 1);
    }
}

void  CFrameMapMgr::UpdateFrameMapSOTDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotID, INT8 nNumSlot, UINT uMMSI, DWORD dwSyncState, DWORD dwTimeOut, DWORD dwSubMsg)
{
    WORD wFrSlotID = GetFrameMapSlotID(wFrameID, wRcvSlotID);

    if(uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        ERROR_LOG("FrMapSO-ERROR] ERROR! Rcved my msg %d, M : %d, S : %d\r\n", uMMSI, uMsgID, wRcvSlotID);
        return;
    }

    if(dwTimeOut == 0)
    {
        WORD uNewFrSlotID = FrameMapSlotIdAdd(wFrSlotID, dwSubMsg);                                                    // to offset to the slot in which transmission will occur during the next frame.
        SetFrameMapOneMsgColumn(TRUE, uNewFrSlotID, nNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC, TMO_MIN, POS_REPORT_SCHEDULED,
                                TDMA_SOTDMA, uMsgID, 0, FALSE, FALSE, 0, FALSE);                                    // 다음 프레임에서의 송신슬롯의 타임아웃을 아직 모르므로 TMO_MIN 로 설정한다!
    }
    else
    {
        dwTimeOut = MIN(dwTimeOut, TMO_MAX);
        SetFrameMapOneMsgColumn(TRUE, wFrSlotID, nNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC, dwTimeOut, POS_REPORT_SCHEDULED,
                                (uMsgID == AIS_MSG_NO_ID_04 ? TDMA_FATDMA : TDMA_SOTDMA), uMsgID, 0, FALSE, FALSE, 0, FALSE);    // 기지국의 송신에 사용된 슬롯은 FATDMA 슬롯으로 간주한다!
    }
}

void  CFrameMapMgr::UpdateFrameMapITDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotID, INT8 nNumSlot, UINT uMMSI, DWORD dwSyncState, DWORD dwSlotInc, DWORD dwNumSlot, DWORD dwKeepFlag, BYTE cSlotStatus)
{
    const UINT8 uSlotTimeOut = 0;        // one more frame;

    WORD wFrSlotID = GetFrameMapSlotID(wFrameID, wRcvSlotID);

    if(uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        ERROR_LOG("FrMapIT-ERROR] ERROR! Rcved my msg %d, M : %d, S : %d\r\n", uMMSI, uMsgID, wRcvSlotID);
        return;
    }

    if(dwKeepFlag)
        SetFrameMapOneMsgColumn(TRUE, wFrSlotID, nNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC, uSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_ITDMA, uMsgID, 0, FALSE, FALSE, 0, FALSE);

    // 스테이션의 다음 송신 슬롯 예약
    if(dwSlotInc > 0)
    {
        int nSlotOffset = dwSlotInc;
        UINT8 uNumNextTxSlot = 0;
        if(dwNumSlot <= 4)
            uNumNextTxSlot = dwNumSlot + 1;
        else if(dwNumSlot <= 7)
        {
            uNumNextTxSlot = dwNumSlot - 4;
            nSlotOffset += MAX_ITDMA_SLOT_OFFSET;
        }
        WORD wNextReservedSlot = FrameMapSlotIdAdd(wFrSlotID, nSlotOffset);
        SetFrameMapOneMsgColumn(TRUE, wNextReservedSlot, uNumNextTxSlot, uMMSI, SLOTSTAT_EXT_ALLOC, uSlotTimeOut, POS_REPORT_SCHEDULED, TDMA_ITDMA, uMsgID, 0, FALSE, FALSE, 0, FALSE);
    }
}

void  CFrameMapMgr::UpdateFrameMapFATDMA(BOOL bCheckBastStStat, UINT16 uMsgID, WORD wRcvFrameID, WORD wRcvSlotID, UINT uMMSI, DWORD dwOffset, DWORD dwNumSlot, DWORD dwTimeOut, int nIncrement, BOOL bFAtoSO)
{
    if(bCheckBastStStat)
    {
        if(!IsValidMMSI_BaseSt(uMMSI))
        {
            INFO_LOG("UpdateFrameMapFATDMA] ignored, not BS %09d\r\n", uMMSI);
            return;
        }

        if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(uMMSI) && !CSensorMgr::getInst()->IsGnssLost())
        {
            INFO_LOG("UpdateFrameMapFATDMA] ignored, 120NM : %d, OwnPosLost : %d\r\n", CUserDirMgr::getInst()->IsBaseStationWithin120NM(uMMSI), CSensorMgr::getInst()->IsGnssLost());
            return;
        }
    }

    int nTdmaScheme = TDMA_FATDMA;
    int nAccessScheme = POS_REPORT_SCHEDULED;
    if(bFAtoSO)
    {
        if(uMsgID == AIS_MSG_NO_ID_05)
        {
            nAccessScheme    = POS_REPORT_UNSCHEDULED;
            nTdmaScheme        = TDMA_RATDMA;
        }
        else
        {
            nAccessScheme    = POS_REPORT_SCHEDULED;
            nTdmaScheme        = TDMA_SOTDMA;
        }
    }

    WORD wRcvFrSlot = GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wNextReservedSlot = FrameMapSlotIdAdd(wRcvFrSlot, dwOffset);
    INT16 nResCnt = 0;

    if(nIncrement == 0)
        nResCnt = 1;
    else
    {
        nResCnt = NUM_SLOT_PER_FRAME / (int)nIncrement;
    }

    if(bFAtoSO)
    {
        for(int i = 0 ; i < nResCnt ; i++)
        {
            if(IsSlotAvailableForMMSI(wNextReservedSlot, dwNumSlot, uMMSI))
            {
                SetFrameMapOneMsgColumn(FALSE, wNextReservedSlot, dwNumSlot, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC,
                                        dwTimeOut, nAccessScheme, nTdmaScheme, uMsgID, 0, FALSE, FALSE, 0, bFAtoSO);
            }
            wNextReservedSlot = FrameMapSlotIdAdd(wNextReservedSlot, nIncrement);
        }
    }
    else
    {
        for(int i = 0 ; i < nResCnt ; i++)
        {
            SetFrameMapOneMsgColumn(TRUE, wNextReservedSlot, dwNumSlot, uMMSI, SLOTSTAT_EXT_ALLOC,
                                    dwTimeOut, POS_REPORT_SCHEDULED, TDMA_FATDMA, uMsgID, 0, TRUE, FALSE, 0, bFAtoSO);
            wNextReservedSlot = FrameMapSlotIdAdd(wNextReservedSlot, nIncrement);
        }
    }
}

BOOL CFrameMapMgr::UpdateAssignedSlotsFATDMAtoSOTDMA(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt)
{
    //-------------------------------------------------------------------------------------------------------------------------------------------------------
    // 슬롯할당 메시지 수신 시 호출됨
    // 할당명령에 의해 지정된 슬롯들을(이 슬롯들은  메시지 20 으로 기지국에 할당되어있는 FATDMA 슬롯일수도 있고 아닐수도 있다) 내부 SOTDMA 할당 슬롯으로 변경
    //-------------------------------------------------------------------------------------------------------------------------------------------------------

    int nFailCnt = 0;
    WORD wRcvFrSlotID = GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wFrSlotID = FrameMapSlotIdAdd(wRcvFrSlotID, wOffset);

    if(wFrSlotID == SLOTID_NONE)
        return FALSE;

    int nLoopCnt = NUM_SLOT_PER_FRAME / nIncrement;
    if(NUM_SLOT_PER_FRAME % nIncrement)
        nLoopCnt++;

    DEBUG_LOG("FAtoSO] mmsi : %09d, rcvSlot : %d,%d (%d), off : %d, inc : %d, startSlot : %d(%d), cnt : %d\r\n",
        uBaseStMMSI, wRcvFrameID, wRcvSlotID, wRcvFrSlotID, wOffset, nIncrement, wFrSlotID, GetSlotIdFromFrameSlotID(wFrSlotID), nLoopCnt);

    BOOL bFAtoSO;
    FRAMEMAP_SLOTDATA *pCurSlot;
    FRAMEMAP_SLOTDATA *pNextSlot;

    for(int i = 0 ; i < nLoopCnt-1 ; i++)
    {
        pCurSlot = GetSlotDataPtr(wFrSlotID);
        pNextSlot= GetSlotDataPtr(FrameMapSlotIdAdd(wFrSlotID, nIncrement));
        bFAtoSO = (pCurSlot->bSlotStat == SLOTSTAT_EXT_ALLOC && pCurSlot->uMMSI == uBaseStMMSI && pCurSlot->bCommStateScheme == TDMA_FATDMA);

        SetFrameMapOneMsgColumn(FALSE, wFrSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_02, 0, FALSE, FALSE, 0, bFAtoSO);

        if(!IsInternalAllocSlotSO(pNextSlot))
            SetFrameMapOneMsg(TRUE, wFrSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_03, nIncrement, TRUE, FALSE, 0, FALSE);

        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, nIncrement);
    }

    pCurSlot = GetSlotDataPtr(wFrSlotID);
    bFAtoSO = (pCurSlot->bSlotStat == SLOTSTAT_EXT_ALLOC && pCurSlot->uMMSI == uBaseStMMSI && pCurSlot->bCommStateScheme == TDMA_FATDMA);
    BOOL bLastExisting = IsInternalAllocSlotSO(pCurSlot);

    SetFrameMapOneMsgColumn(FALSE, wFrSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_02, 0, TRUE, FALSE, 0, bFAtoSO);
    if(!bLastExisting)
        SetFrameMapOneMsg(TRUE, wFrSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                            POS_REPORT_SCHEDULED, TDMA_ITDMA, AIS_MSG_NO_ID_03, 0, TRUE, FALSE, 0, FALSE);

    return (nFailCnt <= 0);
}

void CFrameMapMgr::RestoreAssginedSlotsSOTDMAtoFATDMA(UINT uBaseStMMSI, WORD wStartSlotID)
{
    //-----------------------------------------------------------------------------------------------
    // 슬롯할당모드에 의해 사용된 SOTDMA 슬롯들을 원래 FATDMA 할당한 기지국의 것으로 변경
    //-----------------------------------------------------------------------------------------------

    FRAMEMAP_SLOTDATA *pSlotPtr;
    WORD wFrSlotID = wStartSlotID;
    UINT uTmpSlotID;

    if(uBaseStMMSI == AIS_AB_MMSI_NULL)
        return;

    if(wStartSlotID == SLOTID_NONE)
        return;

    for(int i = 0 ; i < NUM_SLOT_PER_FRAME ; i++)
    {
        pSlotPtr = GetSlotDataPtr(wFrSlotID);

        if(pSlotPtr->bSlotStat == SLOTSTAT_INT_ALLOC && pSlotPtr->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI &&
            pSlotPtr->bAccessScheme == POS_REPORT_SCHEDULED && pSlotPtr->bCommStateScheme == TDMA_SOTDMA &&
            pSlotPtr->uMsgID == AIS_MSG_NO_ID_02)
        {
            if(pSlotPtr->bFAtoSO)
                SetFrameMapOneMsgColumn(FALSE, wFrSlotID, 1, uBaseStMMSI, SLOTSTAT_EXT_ALLOC, 0,
                                        POS_REPORT_SCHEDULED, TDMA_FATDMA, AIS_MSG_NO_ID_20, 0, FALSE, FALSE, 0, FALSE);
        }
        wFrSlotID  = FrameMapSlotIdAdd(wFrSlotID, 1);
    }
}

BOOL CFrameMapMgr::UpdateAssignedSlotsSOTDMA(WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt)
{
    //-----------------------------------------------------------------------------------------------
    // 슬롯할당 메시지 수신 시 호출됨
    //-----------------------------------------------------------------------------------------------

    int nFailCnt = 0;
    WORD wRcvFrSlotID = GetFrameMapSlotID(wRcvFrameID, wRcvSlotID);
    WORD wFrSlotID = FrameMapSlotIdAdd(wRcvFrSlotID, wOffset);

    if(wFrSlotID == SLOTID_NONE)
        return FALSE;

    int nLoopCnt = NUM_SLOT_PER_FRAME / nIncrement;
    if(NUM_SLOT_PER_FRAME % nIncrement)
    {
        nLoopCnt++;
    }

    DEBUG_LOG("AssignedSO] rcvSlot : %d,%d (%d), off : %d, inc : %d, startSlot : %d(%d), cnt : %d\r\n",
            wRcvFrameID, wRcvSlotID, wRcvFrSlotID, wOffset, nIncrement, wFrSlotID, nLoopCnt);

    for(int i = 0 ; i < nLoopCnt ; i++)
    {
        SetFrameMapOneMsgColumn(FALSE, wFrSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, nTimeOutCnt,
                                POS_REPORT_SCHEDULED, TDMA_SOTDMA, AIS_MSG_NO_ID_02, 0, FALSE, FALSE, 0, FALSE);

        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, nIncrement);
    }
    return (nFailCnt <= 0);
}

WORD CFrameMapMgr::GetInternalAllocatedSlot(WORD wStartSlot, INT16 nNumSlotToScan, BYTE bTdmaScheme, WORD *pwOutputSlot, int nMsgID, BOOL bExceptTmoZeroSlot)
{
    FRAMEMAP_SLOTDATA *pSlot;
    WORD wSlot = wStartSlot;

    if(wStartSlot == SLOTID_NONE)
        return SLOTID_NONE;

    if(cShip::getOwnShipInst()->xStaticData.dMMSI == AIS_AB_MMSI_NULL)
        return SLOTID_NONE;

    *pwOutputSlot = SLOTID_NONE;

    for(int i = 0 ; i < nNumSlotToScan ; i++)
    {
        if((pSlot = GetSlotDataPtr(wSlot)))
        {
            if( pSlot->bStartSlot && (nMsgID == AIS_MSG_NO_ID_UNDEFINED || pSlot->uMsgID == nMsgID) &&
                pSlot->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI && pSlot->bSlotStat == SLOTSTAT_INT_ALLOC &&
                (bTdmaScheme == TDMA_NONE || pSlot->bCommStateScheme == bTdmaScheme) &&
                (!bExceptTmoZeroSlot || pSlot->nSlotTimeOut > 0))
            {
                *pwOutputSlot = wSlot;
                break;
            }
        }
        wSlot = FrameMapSlotIdAdd(wSlot, 1);
    }

    return *pwOutputSlot;
}

WORD CFrameMapMgr::GetInternalAllocatedSlot_SO(WORD wStartSI, int nSizeSI, WORD *pwOutputSlot, int nMsgID, BOOL bExceptTmoZeroSlot, BOOL bFindMsg3SO)
{
    if(nSizeSI > MAX_SIZE_SI)
    {
        INFO_LOG("GetIntSlot_SO] too big sizeSI : %d\r\n", nSizeSI);
        nSizeSI = MAX_SIZE_SI;
    }

    GetInternalAllocatedSlot(wStartSI, nSizeSI, TDMA_SOTDMA, pwOutputSlot, nMsgID, bExceptTmoZeroSlot);

    if(*pwOutputSlot == SLOTID_NONE && bFindMsg3SO && (nMsgID == AIS_MSG_NO_ID_01 || nMsgID == AIS_MSG_NO_ID_02))
    {
        GetInternalAllocatedSlot(wStartSI, nSizeSI, TDMA_SOTDMA, pwOutputSlot, AIS_MSG_NO_ID_03, bExceptTmoZeroSlot);            // Get SOTDMA allocated MSG-3
    }
    return *pwOutputSlot;
}

WORD CFrameMapMgr::GetInternalAllocatedSlot_IT(WORD wStartSI, int nSizeSI, WORD *pwOutputSlot, int nMsgID, BOOL bExceptTmoZeroSlot)
{
    return GetInternalAllocatedSlot(wStartSI, nSizeSI, TDMA_ITDMA, pwOutputSlot, nMsgID, bExceptTmoZeroSlot);
}

WORD CFrameMapMgr::GetSoTdmaSlotForItdmaTx(WORD wStartSI, int nSizeSI, WORD *pwOutputSlot)
{
    if(wStartSI == SLOTID_NONE)
        return SLOTID_NONE;

    const INT8 nPosMsgID = GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
    GetInternalAllocatedSlot_SO(wStartSI, nSizeSI, pwOutputSlot, nPosMsgID, TRUE);
    return *pwOutputSlot;
}

BOOL CFrameMapMgr::IsIntTxReservedSlot(FRAMEMAP_SLOTDATA *pSlotData)
{
    if(pSlotData->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI && pSlotData->bStartSlot)
        return TRUE;
    return FALSE;
}

BOOL CFrameMapMgr::IsSlotAvailableForMMSI(WORD wFrSlotID, int nNumSlot, UINT uMMSI)
{
    FRAMEMAP_SLOTDATA *pSlot = GetSlotDataPtr(wFrSlotID);
    for(int i = 0 ; i < nNumSlot ; i++)
    {
        if(pSlot->uMMSI != AIS_AB_MMSI_NULL && pSlot->uMMSI != uMMSI)
        {
            INFO_LOG("Map-FA] IsSlotAvailableForMMSI, ignore, MMSI mismatch %09d -> %09d\r\n",
                    uMMSI, pSlot->uMMSI);
            return FALSE;
        }
    }
    return TRUE;
}

BOOL CFrameMapMgr::IsInternalAllocSlot(FRAMEMAP_SLOTDATA *pSlotPtr)
{
    return (pSlotPtr && pSlotPtr->uMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI);
}

BOOL CFrameMapMgr::IsInternalAllocSlotSO(FRAMEMAP_SLOTDATA *pSlotPtr, BOOL bCheckMsgID)
{
    return (pSlotPtr && pSlotPtr->uMMSI != AIS_AB_MMSI_NULL && pSlotPtr->uMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI &&
            (!bCheckMsgID || (pSlotPtr->uMsgID == AIS_MSG_NO_ID_01 || pSlotPtr->uMsgID == AIS_MSG_NO_ID_02)) &&
            pSlotPtr->bCommStateScheme == TDMA_SOTDMA && pSlotPtr->bAccessScheme == POS_REPORT_SCHEDULED);
}

void CFrameMapMgr::SetLastTxFrameAndFree(WORD wFrSlotIDStart, int nNumColToCheck, BOOL bCheckMsgID)
{
    //---------------------------------------------------------------------------------
    // SOTDMA 내부할당된 해당 메시지 송신 슬롯을 모두 찾아 time out 을 0 으로 만든다.
    //---------------------------------------------------------------------------------

    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;

    for(int i = 0 ; i < nNumColToCheck ; i++)
    {
        pSlotPtr = GetSlotDataPtr(uMapSlotID);
        if(IsInternalAllocSlot(pSlotPtr))
        {
            DEBUG_LOG("SetLast] chkMsg: %d, s:%d,%d, M:%d, tmo:%d, CH:%d, off:%d\r\n",
                    bCheckMsgID, uMapSlotID, GetSlotIdFromFrameSlotID(uMapSlotID), pSlotPtr->uMsgID, pSlotPtr->nSlotTimeOut, pSlotPtr->wTxChNum, pSlotPtr->uSlotOffset);

            uTmpSlotID = FrameMapSlotIdAdd(uMapSlotID, NUM_SLOT_PER_FRAME);
            if(pSlotPtr->uMsgID == AIS_MSG_NO_ID_03 && pSlotPtr->nSlotTimeOut > 0)
            {
                FRAMEMAP_SLOTDATA *pTmpSlot = GetSlotDataPtr(uTmpSlotID);
                if(pTmpSlot->nSlotTimeOut == pSlotPtr->nSlotTimeOut-1 && (pTmpSlot->uMsgID == AIS_MSG_NO_ID_01 || pTmpSlot->uMsgID == AIS_MSG_NO_ID_02))
                {
                    pSlotPtr->uMsgID = pTmpSlot->uMsgID;
                    pSlotPtr->uSlotOffset    = 0;
                }
            }

            FreeFrameMapMsgColRow(uTmpSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI);

            pSlotPtr->bItdmaKeepFlag= FALSE;
            pSlotPtr->nSlotTimeOut    = 0;                                                // 현재 슬롯으로 부터 한 프레임동안은 time-out 을 zero 로 하여 송신해야한다!
        }
        else
        {
        }
        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }
}

void CFrameMapMgr::SetLastTxSlotWithRange(int nStartSlot, int nEndSlot, BOOL bExceptBorder)
{
    int nClearStartSlot = nStartSlot;
    int nClearEndSlot    = nEndSlot;
    if(bExceptBorder)
    {
        nClearStartSlot    = FrameMapSlotIdAdd(nClearStartSlot, 1);
        nClearEndSlot    = FrameMapSlotIdAdd(nClearEndSlot, -1);
    }
    int nNumCols = FrameMapGetDiffSlotID(nClearStartSlot, nClearEndSlot);

    if(nStartSlot == SLOTID_NONE || nEndSlot == SLOTID_NONE)
        return;

    SetLastTxFrameAndFree(nClearStartSlot, nNumCols, TRUE);
}

void CFrameMapMgr::FrChangeFrMapPosMsgMode(WORD wFrSlotIDStart, int nOpMode)
{
    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    const int NUM_CHECK_FRAME = TMO_MAX;
    const int NUM_SEARCH_SLOTS = NUM_SLOT_PER_FRAME+NUM_SLOT_PER_FRAME;
    const int NEW_POSMSG = GetScheduledPosReportMsgID(nOpMode);

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;
    int nCntRow;

    for(int i = 0 ; i < NUM_SEARCH_SLOTS ; i++)
    {
        uTmpSlotID = FrameMapSlotIdAdd(uMapSlotID, i);
        for(int nCntRow = 0 ; nCntRow < NUM_CHECK_FRAME ; nCntRow++)
        {
            pSlotPtr = GetSlotDataPtr(uTmpSlotID);
            if(!IsInternalAllocSlotSO(pSlotPtr))
                break;

            pSlotPtr->uMsgID = NEW_POSMSG;
            if(pSlotPtr->nSlotTimeOut <= 0)
                break;

            uTmpSlotID = FrameMapSlotIdAdd(uTmpSlotID, NUM_SLOT_PER_FRAME);
            nCntRow++;
        }
    }
}

void CFrameMapMgr::FrChangePosMsgModeTimeOut(WORD wFrSlotIDStart, int nNumSlotsToCheck, int nTimeOut)
{
    //---------------------------------------------------------------------------------
    // SOTDMA 내부할당된 해당 메시지 송신 슬롯을 모두 찾아 mode, time out 을 바꾼다.
    //---------------------------------------------------------------------------------

    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;
    int nCntRow;

    for(int i = 0 ; i < nNumSlotsToCheck ; i++)
    {
        pSlotPtr = GetSlotDataPtr(uMapSlotID);
        if(IsInternalAllocSlotSO(pSlotPtr))
        {
            FreeFrameMapMsgColRow(uMapSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI);
            SetFrameMapOneMsgColumn(TRUE, uMapSlotID, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, nTimeOut, pSlotPtr->bAccessScheme, pSlotPtr->bCommStateScheme,
                                    pSlotPtr->uMsgID, 0, FALSE, FALSE, 0, pSlotPtr->bFAtoSO);
        }

        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }
}

void CFrameMapMgr::PrepToChgCh(WORD wFrSlotIDStart)
{
    //---------------------------------------------------------------------------------
    // SOTDMA 내부할당된 해당 메시지 송신 슬롯을 모두 찾아 time out 을 0 으로 만들고
    // 외부할당된 메시지들은 모두 비운다.
    //---------------------------------------------------------------------------------
    if(wFrSlotIDStart == SLOTID_NONE)
        return;

    FRAMEMAP_SLOTDATA *pSlotPtr = NULL;
    UINT16 uMapSlotID = wFrSlotIDStart;
    UINT16 uTmpSlotID;

    memset(gpuMsgMapOneFrame, 0, sizeof(FRAMEMAP_SLOTDATA) * NUM_SEARCH_SLOTS);

    for(int i = 0 ; i < NUM_SEARCH_SLOTS ; i++)
    {
        pSlotPtr = GetSlotDataPtr(uMapSlotID);
        if(IsInternalAllocSlot(pSlotPtr))
        {
            DEBUG_LOG("PrepToChgCh] s:%d,%d, M:%d, tmo:%d\r\n",
                uMapSlotID, GetSlotIdFromFrameSlotID(uMapSlotID), pSlotPtr->uMsgID, pSlotPtr->nSlotTimeOut);

            uTmpSlotID = FrameMapSlotIdAdd(uMapSlotID, NUM_SLOT_PER_FRAME);
            if(pSlotPtr->uMsgID == AIS_MSG_NO_ID_03 && pSlotPtr->nSlotTimeOut > 0)
            {
                FRAMEMAP_SLOTDATA *pTmpSlot = GetSlotDataPtr(uTmpSlotID);
                if(pTmpSlot->nSlotTimeOut == pSlotPtr->nSlotTimeOut-1 && (pTmpSlot->uMsgID == AIS_MSG_NO_ID_01 || pTmpSlot->uMsgID == AIS_MSG_NO_ID_02))
                {
                    pSlotPtr->uMsgID = pTmpSlot->uMsgID;
                    pSlotPtr->uSlotOffset    = 0;
                }
            }

            memcpy(&gpuMsgMapOneFrame[i], pSlotPtr, sizeof(FRAMEMAP_SLOTDATA));
        }

        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }

    ClearFrameMap();

    uMapSlotID = wFrSlotIDStart;
    for(int i = 0 ; i < NUM_SEARCH_SLOTS ; i++)
    {
        pSlotPtr = &gpuMsgMapOneFrame[i];
        if(IsInternalAllocSlot(pSlotPtr))
        {
            SetFrameMapOneMsg(TRUE, uMapSlotID, pSlotPtr->uNumSlots, cShip::getOwnShipInst()->xStaticData.dMMSI, pSlotPtr->bSlotStat, 0,
                                pSlotPtr->bAccessScheme, pSlotPtr->bCommStateScheme,
                                pSlotPtr->uMsgID, pSlotPtr->uSlotOffset, FALSE, FALSE, 0, pSlotPtr->bFAtoSO);
        }
        uMapSlotID = FrameMapSlotIdAdd(uMapSlotID, 1);
    }
}

void CFrameMapMgr::ProcessFrameChg_FrameMap()
{
}
