#include "LayerNetwork.h"
#include "LayerPhysical.h"
#include "Timer.h"
#include "Ship.h"
#include "TestModeMgr.h"
#include "SysLog.h"
#include "DSCMgr.h"

CDSCMgr::CDSCMgr(int nHwLocalRcvrID)
{
    m_nHwLocalRcvrID = nHwLocalRcvrID;
    m_uChNumRx = 0;

    m_pDscModem = new cDscModem(cFskModem::getInst());

    m_vRxBuffData = (xDscAllMsg*)SysAllocMemory(DSC_MAX_BUFF_SIZE * sizeof(xDscAllMsg));
    m_nRxBuffHead = 0;
    m_nRxBuffTail = 0;

    Initialize();
}

CDSCMgr::~CDSCMgr()
{
}

void CDSCMgr::Initialize()
{
}

BOOL CDSCMgr::SetRxChannelNumber(UINT16 uChNum)
{
    BOOL bRet = TRUE;

    if(uChNum != m_uChNumRx)
    {
        if(bRet = CLayerPhysical::getInst()->SetRxLocalFreq(m_nHwLocalRcvrID, uChNum))
        {
            m_uChNumRx = uChNum;
        }
    }

    return bRet;
}

void CDSCMgr::PutDscMsg(xDscAllMsg *pDscMsg)
{
    m_vRxBuffData[m_nRxBuffHead] = *pDscMsg;

    ++m_nRxBuffHead;
    if(m_nRxBuffHead >= DSC_MAX_BUFF_SIZE)
        m_nRxBuffHead  = 0;
}

int CDSCMgr::GetDscMsg(xDscAllMsg *pDscMsg)
{
    if(m_nRxBuffHead != m_nRxBuffTail)
    {
        *pDscMsg = m_vRxBuffData[m_nRxBuffTail];

        ++m_nRxBuffTail;
        if(m_nRxBuffTail >= DSC_MAX_BUFF_SIZE)
            m_nRxBuffTail  = 0;
        return 1;
    }
    return 0;
}

BOOL CDSCMgr::GetRosChTrxModeFromDscMsg(xROSDATA *psRosData, xDscAllMsg *psDscMsg)
{
    if(psDscMsg->uPrimaryChID == AIS_CH_NUM_NONE)
    {
        WARNING_LOG("DSC] invalid TRX mode, priCH: %d, secCH: %d, guardCH: %d, s:%d\r\n",
                psDscMsg->uPrimaryChID, psDscMsg->uSecondaryChID, psDscMsg->uGuardRegionChID, cTimerSys::getInst()->GetCurTimerSec());
        return FALSE;
    }

    if(psDscMsg->uSecondaryChID == AIS_CH_NUM_NONE)
    {
        if(psDscMsg->uGuardRegionChID == AIS_CH_NUM_NONE)
        {
            psDscMsg->uSecondaryChID= AIS_CH_NUM_NONE;
            psRosData->bTxRxMode    = TRXMODE_TXARXA_______;            // single channel mode
        }
        else
        {
            psDscMsg->uSecondaryChID = psDscMsg->uGuardRegionChID;
            psDscMsg->uSecondaryBandwidth = psDscMsg->uGuardRegionBandwidth;
            psRosData->bTxRxMode = TRXMODE_TXARXA____RXB;
        }
    }
    else
        psRosData->bTxRxMode = TRXMODE_TXARXA_TXBRXB;

    psRosData->wChannelNoA= psDscMsg->uPrimaryChID;
    psRosData->wChannelNoB= psDscMsg->uSecondaryChID;

    psRosData->bBandwidthA= psDscMsg->uPrimaryBandwidth;
    psRosData->bBandwidthB= psDscMsg->uSecondaryBandwidth;
    return TRUE;
}

BOOL CDSCMgr::ProcessDscMSG_AreaCall(xDscAllMsg *psDscMsg)
{
    POS_GRID geoNE;
    POS_GRID geoSW;

    geoNE.nLAT = psDscMsg->xGeoData.xNE_Pos.nGridLat;
    geoNE.nLON = psDscMsg->xGeoData.xNE_Pos.nGridLon;
    geoSW.nLAT = psDscMsg->xGeoData.xSW_Pos.nGridLat;
    geoSW.nLON = psDscMsg->xGeoData.xSW_Pos.nGridLon;

    POS_ALLH xPointNE;
    xPointNE.xPosG.nLAT = psDscMsg->sRosNE.nGridLat;
    xPointNE.xPosG.nLON = psDscMsg->sRosNE.nGridLon;
    CAisLib::CalcLowPosByGRID(&xPointNE);

    POS_ALLH xPointSW;
    xPointSW.xPosG.nLAT = psDscMsg->sRosSW.nGridLat;
    xPointSW.xPosG.nLON = psDscMsg->sRosSW.nGridLon;
    CAisLib::CalcLowPosByGRID(&xPointSW);

    DEBUG_LOG("DscMSG_AreaCall] before, fmt:%d, cat:%d, tgt:%09d, src:%09d, grNE:%d,%d,(%.3f,%.3f) grSW:%d,%d,(%.3f,%.3f) grCrs:%d, grShipT:%d, pwr:%d, CH:%d,%d,%d, band:%d,%d,%d, rosNE:%d,%d,(%.3f,%.3f), rosSW:%d,%d,(%.3f,%.3f)\r\n",
        psDscMsg->bFormat, psDscMsg->bCategory, psDscMsg->dTgtMMSI, psDscMsg->dSrcMMSI,
        psDscMsg->xGeoData.xNE_Pos.nGridLon, psDscMsg->xGeoData.xNE_Pos.nGridLat, xPointNE.xPosF.fLON, xPointNE.xPosF.fLAT,
        psDscMsg->xGeoData.xSW_Pos.nGridLon, psDscMsg->xGeoData.xSW_Pos.nGridLat, xPointSW.xPosF.fLON, xPointSW.xPosF.fLAT,
        psDscMsg->uGroupCourse, psDscMsg->uGroupShipType,
        psDscMsg->uTxPower, psDscMsg->uPrimaryChID, psDscMsg->uSecondaryChID, psDscMsg->uGuardRegionChID,
        psDscMsg->uPrimaryBandwidth, psDscMsg->uSecondaryBandwidth, psDscMsg->uGuardRegionBandwidth,
        psDscMsg->sRosNE.nGridLon, psDscMsg->sRosNE.nGridLat, xPointNE.xPosF.fLON, xPointNE.xPosF.fLAT,
        psDscMsg->sRosSW.nGridLon, psDscMsg->sRosSW.nGridLat, xPointSW.xPosF.fLON, xPointSW.xPosF.fLAT);

    if(!CROSMgr::getInst()->CheckInsideRectangleXX(&(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG), &geoNE, &geoSW))
    {
        WARNING_LOG("DscMSG_AreaCall] ignore, own ship is not in the VTS area, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        return FALSE;
    }

    if(psDscMsg->uGroupCourse != NMEA_HDG_NULL)
    {
        //--------------------------------------------------------------
        // ITU-R M.825-3 5.2.1 Ships on a certain course -> heading
        //--------------------------------------------------------------
        if(cShip::getOwnShipInst()->xDynamicData.nHDG == NMEA_HDG_NULL)
        {
            WARNING_LOG("DscMSG_AreaCall] ignore, heading unknown, grCourse:%d, s:%d\r\n",
                psDscMsg->uGroupCourse, cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        int nOwnHeading = cShip::getOwnShipInst()->xDynamicData.nHDG / NMEA_SCALE_HDT;
        if(CAisLib::GetDiffAbsYaw(nOwnHeading, psDscMsg->uGroupCourse) > 2)
        {
            WARNING_LOG("DscMSG_AreaCall] ignore, own heading mismatch, grHdg:%d, ownHdg:%d, s:%d\r\n",
                psDscMsg->uGroupCourse, cShip::getOwnShipInst()->xDynamicData.nHDG, cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        DEBUG_LOG("DscMSG_AreaCall] GroupBy Course(HDG), own HDG matched, grHdg:%d, ownHdg:%d, s:%d\r\n",
                psDscMsg->uGroupCourse, cShip::getOwnShipInst()->xDynamicData.nHDG, cTimerSys::getInst()->GetCurTimerSec());
    }

    if(psDscMsg->uGroupShipType != AIS_SHIPTYPE_NULL)
    {
        //--------------------------------------------------------------
        // ITU-R M.825-3 5.2.1 Ships of a certain type
        //--------------------------------------------------------------
        if(cShip::getOwnShipInst()->xNavData.uShipType == AIS_SHIPTYPE_NULL)
        {
            WARNING_LOG("DscMSG_AreaCall] ignore, own ship type unknown, grShipT:%d, s:%d\r\n",
                    psDscMsg->uGroupShipType, cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        int nGrFirstDigit = psDscMsg->uGroupShipType / 10;
        int nGrSecondeDigit = psDscMsg->uGroupShipType % 10;
        int nOwnFirstDigit = cShip::getOwnShipInst()->xNavData.uShipType / 10;
        int nOwnSecondeDigit = cShip::getOwnShipInst()->xNavData.uShipType % 10;
        if(nGrFirstDigit != nOwnFirstDigit || (nGrSecondeDigit != 0 && nGrSecondeDigit != nOwnSecondeDigit))
        {
            WARNING_LOG("DscMSG_AreaCall] ignore, ship type mismatch, grShipT:%d, ownShipT:%d, s:%d\r\n",
                psDscMsg->uGroupShipType, cShip::getOwnShipInst()->xNavData.uShipType,
                cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        DEBUG_LOG("DscMSG_AreaCall] GroupBy ShipType, own ShipType matched, grShipT:%d, ownShipT:%d, s:%d\r\n",
            psDscMsg->uGroupShipType, cShip::getOwnShipInst()->xNavData.uShipType,
            cTimerSys::getInst()->GetCurTimerSec());
    }

    xROSDATA xRosData;
    CROSMgr::getInst()->ClearOneRosData(&xRosData);

    xRosData.xPointNE.xPosG.nLAT = psDscMsg->sRosNE.nGridLat;
    xRosData.xPointNE.xPosG.nLON = psDscMsg->sRosNE.nGridLon;
    CAisLib::CalcLowPosByGRID(&(xRosData.xPointNE));

    xRosData.xPointSW.xPosG.nLAT = psDscMsg->sRosSW.nGridLat;
    xRosData.xPointSW.xPosG.nLON = psDscMsg->sRosSW.nGridLon;
    CAisLib::CalcLowPosByGRID(&(xRosData.xPointSW));

    xRosData.bRosSource = ROS_SRC_DSC;
    xRosData.dSrcMMSI   = psDscMsg->dSrcMMSI;
    xRosData.bTxPower    = psDscMsg->uTxPower;
    xRosData.bTrZoneSize= TRZONE_SIZE_DFLT;

    if(!GetRosChTrxModeFromDscMsg(&xRosData, psDscMsg))
        return FALSE;

    xRosData.bValidMode = MODE_VAL_ON;
    xRosData.xRcvTime   = cShip::getOwnShipInst()->xSysTime;
    xRosData.dwRcvSysSec= cTimerSys::getInst()->GetCurTimerSec();

    DEBUG_LOG("DscMSG_AreaCall] after %09d, NE:%.2f,%.2f SW:%.2f,%.2f, pos:%.2f,%.2f, ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d, %d-%d-%d,%02d:%02d:%02d, %d\r\n",
        xRosData.dSrcMMSI,
        xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT,
        xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT,
        cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
        xRosData.wChannelNoA, xRosData.wChannelNoB,
        xRosData.bTxRxMode, xRosData.bTxPower, xRosData.bTrZoneSize,
        xRosData.xRcvTime.xDate.nYear, xRosData.xRcvTime.xDate.nMon, xRosData.xRcvTime.xDate.nDay,
        xRosData.xRcvTime.xTime.nHour, xRosData.xRcvTime.xTime.nMin, xRosData.xRcvTime.xTime.nSec,
        cTimerSys::getInst()->GetCurTimerSec());

    if(!CAisLib::IsValidAisGridPOS(&(xRosData.xPointNE.xPosG)) || !CAisLib::IsValidAisGridPOS(&(xRosData.xPointSW.xPosG)))
    {
        //----------------------------------------------
        // in case ROS rect is not defined
        //----------------------------------------------
        CROSMgr::getInst()->UpdateAddressedRosData(&xRosData, TRUE);   // Do not check ROS area, just configure the other setups
    }
    else
    {
        CROSMgr::getInst()->UpdateRosData(&xRosData);
    }

    return TRUE;
}

BOOL CDSCMgr::ProcessDscMSG_Individual(xDscAllMsg *psDscMsg)
{
    xROSDATA xRosData;

    if(cShip::getOwnShipInst()->xStaticData.dMMSI == AIS_AB_MMSI_NULL || psDscMsg->dTgtMMSI != cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        WARNING_LOG("DscMSG_Individual] ignore, target MMSI is not own ship, %09d, own:%09d, s:%d\r\n",
            psDscMsg->dTgtMMSI, cShip::getOwnShipInst()->xStaticData.dMMSI,
            cTimerSys::getInst()->GetCurTimerSec());
        return FALSE;
    }

    if(CTestModeMgr::getInst()->IsReceiveTestRunning())
        CTestModeMgr::getInst()->TMIncDscRcvTestCnt();

#ifdef __ENABLE_DSC__
    CROSMgr::getInst()->SetDefaultRosData(&xRosData);
#endif

    xRosData.xPointNE.xPosG.nLAT = psDscMsg->sRosNE.nGridLat;
    xRosData.xPointNE.xPosG.nLON = psDscMsg->sRosNE.nGridLon;
    CAisLib::CalcLowPosByGRID(&(xRosData.xPointNE));

    xRosData.xPointSW.xPosG.nLAT = psDscMsg->sRosSW.nGridLat;
    xRosData.xPointSW.xPosG.nLON = psDscMsg->sRosSW.nGridLon;
    CAisLib::CalcLowPosByGRID(&(xRosData.xPointSW));

    xRosData.bRosSource = ROS_SRC_DSC;
    xRosData.dSrcMMSI   = psDscMsg->dSrcMMSI;
    xRosData.bTxPower    = psDscMsg->uTxPower;

    xRosData.bTrZoneSize= TRZONE_SIZE_DFLT;

    if(!GetRosChTrxModeFromDscMsg(&xRosData, psDscMsg))
        return FALSE;

    xRosData.bValidMode = MODE_VAL_ON;
    xRosData.xRcvTime   = cShip::getOwnShipInst()->xSysTime;
    xRosData.dwRcvSysSec= cTimerSys::getInst()->GetCurTimerSec();

    DEBUG_LOG("DscMSG_Individual] after, curROS:%d, src:%09d, NE:%.2f,%.2f SW:%.2f,%.2f, pos:%.2f,%.2f, ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d, %d-%d-%d,%02d:%02d:%02d, %d\r\n",
        CROSMgr::getInst()->m_sRosData.nRosIdx,
        xRosData.dSrcMMSI,
        xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT,
        xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT,
        cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
        xRosData.wChannelNoA, xRosData.wChannelNoB,
        xRosData.bTxRxMode, xRosData.bTxPower, xRosData.bTrZoneSize,
        xRosData.xRcvTime.xDate.nYear, xRosData.xRcvTime.xDate.nMon, xRosData.xRcvTime.xDate.nDay,
        xRosData.xRcvTime.xTime.nHour, xRosData.xRcvTime.xTime.nMin, xRosData.xRcvTime.xTime.nSec,
        cTimerSys::getInst()->GetCurTimerSec());

    CROSMgr::getInst()->UpdateAddressedRosData(&xRosData, FALSE);
    return TRUE;
}

void CDSCMgr::ProcessDscMsg(xDscAllMsg *psDscMsg)
{
    DEBUG_LOG("RcvDSC] format:%3d, cat:%3d, srcMMSI:%09d, targetMMSI:%09d\r\n",
            psDscMsg->bFormat, psDscMsg->bCategory, psDscMsg->dSrcMMSI, psDscMsg->dTgtMMSI);

    if(CAisLib::IsValidMMSI_BaseSt(psDscMsg->dSrcMMSI))
    {
        if(psDscMsg->bCategory == DSC_CATEGORY_VTS)
        {
            if(psDscMsg->bFormat == DSC_FRMT_INDIVIDUAL)
            {
                ProcessDscMSG_Individual(psDscMsg);
            }
            else if(psDscMsg->bFormat == DSC_FRMT_VTS_GEOGRAPHY)
            {
                ProcessDscMSG_AreaCall(psDscMsg);
            }
        }
        else
        {
            WARNING_LOG("RcvDSC] ignore, category is not VTS, format:%3d, cat:%3d, srcMMSI:%09d, targetMMSI:%09d\r\n",
                psDscMsg->bFormat, psDscMsg->bCategory, psDscMsg->dSrcMMSI, psDscMsg->dTgtMMSI);
        }
    }
    else
    {
        WARNING_LOG("RcvDSC] ignore, src is not a BS, format:%3d, cat:%3d, srcMMSI:%09d, targetMMSI:%09d\r\n",
            psDscMsg->bFormat, psDscMsg->bCategory, psDscMsg->dSrcMMSI, psDscMsg->dTgtMMSI);
    }
}

void CDSCMgr::ProcessRxMsg()
{
    xDscAllMsg sDscMsg;

	m_pDscModem->ProcessRcvData();

    while(GetDscMsg(&sDscMsg))
    {
        ProcessDscMsg(&sDscMsg);
    }
}
