#include "Timer.h"
#include "Ship.h"
#include "SysLog.h"
#include "SensorMgr.h"
#include "SyncMgr.h"
#include "LongRange.h"
#include "SysOpStatus.h"
#include "UserDirMgr.h"

CUserDirMgr::CUserDirMgr()
{
    m_vAisDirDataBS = (xDIRDATA*)SysAllocMemory(sizeof(xDIRDATA) * AIS_MAX_DIR_BS_NUM);
    m_vAisDirDataMS = (xDIRDATA*)SysAllocMemory(sizeof(xDIRDATA) * AIS_MAX_DIR_MS_NUM);
    m_puOwnShipNumRcvSt9min = (UINT16*)SysAllocMemory(sizeof(UINT16) * NUM_RCV_ST_SAVE_FRAME);

    m_nRxChID      = AIS_CHANNEL_AIS1;
    m_pRxAisMsg99  = NULL;

    m_nOtherStSemaMMSI = AIS_AB_MMSI_NULL;
    m_dwOtherStSemaCheckSec = 0;

    ClearDirDataBS();
    ClearDirDataMS();

    memset(m_puOwnShipNumRcvSt9min, 0, sizeof(INT16) * NUM_RCV_ST_SAVE_FRAME);
    m_nOwnShipNumRcvSt9min = 0;
    m_nOwnShipNumRcvStLast = 0;
}

CUserDirMgr::~CUserDirMgr(void)
{
}

/**
 * @brief Clear directory data for base station
 */
void CUserDirMgr::ClearDirDataBS(void)
{
    for(int i = 0; i < AIS_MAX_DIR_BS_NUM; i++)
        ClearDirDataOne(&m_vAisDirDataBS[i]);

    m_nNumValidBaseSt = 0;
}

/**
 * @brief Clear directory data for mobile station
 */
void CUserDirMgr::ClearDirDataMS(void)
{
    for(int i = 0; i < AIS_MAX_DIR_MS_NUM; i++)
        ClearDirDataOne(&m_vAisDirDataMS[i]);

    m_nNumValidMobileSt = 0;
}

/**
 * @brief Clear directory data
 * @param pAisDirData User directory data
 */
void CUserDirMgr::ClearDirDataOne(xDIRDATA *pAisDirData)
{
    memset(pAisDirData, 0, sizeof(xDIRDATA));

    pAisDirData->dMMSI          = AIS_AB_MMSI_NULL;

    pAisDirData->bCanBeSyncSrc  = FALSE;
    pAisDirData->nClassBType    = CLASS_B_UNIT_UNKNOWN;
    pAisDirData->nStType        = AIS_ST_TYPE_INVALID;

    pAisDirData->uNumRcvStLast  = 0;
    pAisDirData->uNumRcvSt9min  = 0;
    memset(pAisDirData->puNumRcvStForMin, 0, sizeof(pAisDirData->puNumRcvStForMin));

    pAisDirData->bRcvChID       = AIS_CHANNEL_NONE;
    pAisDirData->bRptIndicator  = 0;

    pAisDirData->fDistance  = AIS_DIST_NM_INVALID;

    pAisDirData->bNavStatus = AIS_NAV_STATUS_NONE_NONE;
    pAisDirData->bSyncState = AIS_SYNC_MODE_NONE;
    pAisDirData->bPosValid  = 0;
    pAisDirData->bTimeStamp = AIS_TIME_STAMP_DEFAULT;

    pAisDirData->nLRTxCtrlCode  = LR_TXCTRL_CODE_NA;
    pAisDirData->dwLRCtrlRcvSec = TIMER_SEC_NA;
    pAisDirData->bLRInRegion    = FALSE;
    pAisDirData->dwLRInRegionSec= TIMER_SEC_NA;

    pAisDirData->dwLastNonRepeatPosReportSec[IDX_POS_PREV] = (DWORD)-1;
    pAisDirData->dwLastNonRepeatPosReportSec[IDX_POS_LAST] = (DWORD)-1;

    pAisDirData->dwLastPosReportSec= (DWORD)-POS_VALID_SEC;
    pAisDirData->dwLastSlotReuseSec= 0;
}

/**
 * @brief Clear all parameters
 */
void CUserDirMgr::ClearAllParameter(void)
{
    m_nMsg18ClassBType= CLASS_B_UNIT_UNKNOWN;
    m_dwSrcMMSI  = AIS_AB_MMSI_NULL;
    m_nNavStatus = AIS_NAV_STATUS_NONE_NONE;
    m_nSyncState = AIS_SYNC_MODE_NONE;
    m_nPosValid  = 0;
    m_fDistance  = AIS_DIST_NM_INVALID;
    m_xPosF.fLAT = AIS_FLOAT_LAT_NULL_VAL;
    m_xPosF.fLON = AIS_FLOAT_LON_NULL_VAL;
    m_nRxStnNums = AIS_DIR_DATA_RX_STN_NUM_NULL;
    m_nTimeStamp = AIS_DIR_DATA_TIME_STAMP_NULL;
}

/**
 * @brief Set class B type
 */
void CUserDirMgr::SetClassBType(void)
{
    if (m_nMsgID == AIS_MSG_NO_ID_18)
    {
        m_nMsg18ClassBType = m_pRxAisMsg99->xMsg18.dUnitFlag;
    }
}

/**
 * @brief Set navigation status
 */
void CUserDirMgr::SetNavStatus(void)
{
    if(m_nMsgID ==  AIS_MSG_NO_ID_01 || m_nMsgID ==  AIS_MSG_NO_ID_02 || m_nMsgID == AIS_MSG_NO_ID_03)
        m_nNavStatus = m_pRxAisMsg99->xMsg01.dNavStatus;
}

/**
 * @brief Set synchronization state
 */
void CUserDirMgr::SetSyncState(void)
{
    switch (m_nMsgID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
    case AIS_MSG_NO_ID_03:
        m_nSyncState = m_pRxAisMsg99->xMsg01.dComStateSyncState;
        break;
    case AIS_MSG_NO_ID_04:
        m_nSyncState = m_pRxAisMsg99->xMsg04.dComStateSyncState;
        break;
    case AIS_MSG_NO_ID_11:
        m_nSyncState = m_pRxAisMsg99->xMsg04.dComStateSyncState;
        break;
    case AIS_MSG_NO_ID_09:
        m_nSyncState = m_pRxAisMsg99->xMsg09.dComStateSyncState;
        break;
    case AIS_MSG_NO_ID_18:
        m_nSyncState = m_pRxAisMsg99->xMsg18.dComStateSyncState;
        break;
    case AIS_MSG_NO_ID_26:
        m_nSyncState = m_pRxAisMsg99->xMsg26.dComStateSyncState;
        break;
    }
}

/**
 * @brief Set distance
 */
void CUserDirMgr::SetDistance(void)
{
    FLOAT fLAT = 0;
    FLOAT fLON = 0;

    m_nPosValid = 0;
    if((m_nMsgID ==  AIS_MSG_NO_ID_01 || m_nMsgID ==  AIS_MSG_NO_ID_02 || m_nMsgID ==  AIS_MSG_NO_ID_03)
    	&& CAisLib::IsValidAisHighPOS(&m_pRxAisMsg99->xMsg01.xFullPosX.xPosH))
    {
        m_nPosValid = 1;
        fLAT = m_pRxAisMsg99->xMsg01.xFullPosX.xPosF.fLAT;
        fLON = m_pRxAisMsg99->xMsg01.xFullPosX.xPosF.fLON;
    }
    else if((m_nMsgID == AIS_MSG_NO_ID_04 || m_nMsgID == AIS_MSG_NO_ID_11)
    		&& CAisLib::IsValidAisHighPOS(&m_pRxAisMsg99->xMsg04.xFullPosX.xPosH))
    {
        m_nPosValid = 1;
        fLAT = m_pRxAisMsg99->xMsg04.xFullPosX.xPosF.fLAT;
        fLON = m_pRxAisMsg99->xMsg04.xFullPosX.xPosF.fLON;
    }
    else if((m_nMsgID == AIS_MSG_NO_ID_09) && CAisLib::IsValidAisHighPOS(&m_pRxAisMsg99->xMsg09.xFullPosX.xPosH))
    {
        m_nPosValid = 1;
        fLAT = m_pRxAisMsg99->xMsg09.xFullPosX.xPosF.fLAT;
        fLON = m_pRxAisMsg99->xMsg09.xFullPosX.xPosF.fLON;
    }
    else if((m_nMsgID == AIS_MSG_NO_ID_18) && CAisLib::IsValidAisHighPOS(&m_pRxAisMsg99->xMsg18.xFullPosX.xPosH))
    {
        m_nPosValid = 1;
        fLAT = m_pRxAisMsg99->xMsg18.xFullPosX.xPosF.fLAT;
        fLON = m_pRxAisMsg99->xMsg18.xFullPosX.xPosF.fLON;
    }
    else if((m_nMsgID == AIS_MSG_NO_ID_19) && CAisLib::IsValidAisHighPOS(&m_pRxAisMsg99->xMsg19.xFullPosX.xPosH))
    {
        m_nPosValid = 1;
        fLAT = m_pRxAisMsg99->xMsg19.xFullPosX.xPosF.fLAT;
        fLON = m_pRxAisMsg99->xMsg19.xFullPosX.xPosF.fLON;
    }
    else if((m_nMsgID == AIS_MSG_NO_ID_21) && CAisLib::IsValidAisHighPOS(&m_pRxAisMsg99->xMsg21.xFullPosX.xPosH))
    {
        m_nPosValid = 1;
        fLAT = m_pRxAisMsg99->xMsg21.xFullPosX.xPosF.fLAT;
        fLON = m_pRxAisMsg99->xMsg21.xFullPosX.xPosF.fLON;
    }

    if(CSensorMgr::getInst()->IsGnssFixed()
    	&& m_nPosValid
		&& CAisLib::IsValidAisGridPOS(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG))
    {
        m_xPosF.fLAT = fLAT;
        m_xPosF.fLON = fLON;
        m_fDistance = CGps::GetDistanceByFLOAT(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, fLAT, fLON, DIST_UNIT_NM);
    }
    else
    {
        m_nPosValid = 0;
        m_xPosF.fLAT = AIS_FLOAT_LAT_NULL_VAL;
        m_xPosF.fLON = AIS_FLOAT_LON_NULL_VAL;
        m_fDistance  = AIS_DIST_NM_INVALID;
    }
}

/**
 * @brief Set received station numbers
 */
void CUserDirMgr::SetRxStnNums(void)
{
    //-----------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 ******* Synchronized to base station (direct or indirect)
    // When the parameter slot time-out of the SOTDMA communication state has one of the values
    // three (3), five (5), or seven (7), the number of received stations should be contained within the
    // SOTDMA communication state-submessage.
    //-----------------------------------------------------------------------------------------------------

    if((m_nMsgID == AIS_MSG_NO_ID_01 || m_nMsgID == AIS_MSG_NO_ID_02) &&
        (m_pRxAisMsg99->xMsg01.dComStateTimeOut == 3 || m_pRxAisMsg99->xMsg01.dComStateTimeOut == 5 || m_pRxAisMsg99->xMsg01.dComStateTimeOut == 7))
        m_nRxStnNums = m_pRxAisMsg99->xMsg01.dComStateSubMsg;
    else if((m_nMsgID == AIS_MSG_NO_ID_04 || m_nMsgID == AIS_MSG_NO_ID_11) &&
        (m_pRxAisMsg99->xMsg04.dComStateTimeOut == 3 || m_pRxAisMsg99->xMsg04.dComStateTimeOut == 5 || m_pRxAisMsg99->xMsg04.dComStateTimeOut == 7))
        m_nRxStnNums = m_pRxAisMsg99->xMsg04.dComStateSubMsg;
    else if((m_nMsgID == AIS_MSG_NO_ID_09) && (m_pRxAisMsg99->xMsg09.dComStateSelector == COMSTAT_SELECTOR_SOTDMA) &&
        (m_pRxAisMsg99->xMsg09.dComStateTimeOut == 3 || m_pRxAisMsg99->xMsg09.dComStateTimeOut == 5 || m_pRxAisMsg99->xMsg09.dComStateTimeOut == 7))
        m_nRxStnNums = m_pRxAisMsg99->xMsg09.dComStateSubMsg;
    else if((m_nMsgID == AIS_MSG_NO_ID_18) && (m_pRxAisMsg99->xMsg18.dComStateSelector == COMSTAT_SELECTOR_SOTDMA) &&
        (m_pRxAisMsg99->xMsg18.dComStateTimeOut == 3 || m_pRxAisMsg99->xMsg18.dComStateTimeOut == 5 || m_pRxAisMsg99->xMsg18.dComStateTimeOut == 7))
        m_nRxStnNums = m_pRxAisMsg99->xMsg18.dComStateSubMsg;
    else if((m_nMsgID == AIS_MSG_NO_ID_26) && (m_pRxAisMsg99->xMsg26.dComStateSelector == COMSTAT_SELECTOR_SOTDMA) &&
        (m_pRxAisMsg99->xMsg26.dComStateTimeOut == 3 || m_pRxAisMsg99->xMsg26.dComStateTimeOut == 5 || m_pRxAisMsg99->xMsg26.dComStateTimeOut == 7))
        m_nRxStnNums = m_pRxAisMsg99->xMsg26.dComStateSubMsg;
}

/**
 * @brief Set time stamp
 */
void CUserDirMgr::SetTimeStamp(void)
{
    switch (m_nMsgID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
    case AIS_MSG_NO_ID_03:
        m_nTimeStamp = m_pRxAisMsg99->xMsg01.dTimeStamp; break;

    case AIS_MSG_NO_ID_04:
    case AIS_MSG_NO_ID_11:
        m_nTimeStamp = m_pRxAisMsg99->xMsg04.dSec;       break;

    case AIS_MSG_NO_ID_09:
        m_nTimeStamp = m_pRxAisMsg99->xMsg09.dTimeStamp; break;

    case AIS_MSG_NO_ID_18:
        m_nTimeStamp = m_pRxAisMsg99->xMsg18.dTimeStamp; break;

    case AIS_MSG_NO_ID_19:
        m_nTimeStamp = m_pRxAisMsg99->xMsg19.dTimeStamp; break;

    case AIS_MSG_NO_ID_21:
        m_nTimeStamp = m_pRxAisMsg99->xMsg21.dTimeStamp; break;
    }
}

/**
 * @brief Find directory data index from table
 * @param pAisDirData User directory data
 * @param nMaxNo Maximum number of user directory data
 * @param dMMSI MMSI
 * @return Directory data index
 */
int CUserDirMgr::FindDirDataIdxFromTbl(xDIRDATA *pAisDirData, int nMaxNo, DWORD dMMSI)
{
    int  i;

    // Find directory data index from table
    for(i = 0; i < nMaxNo; i++)
    {
        if(pAisDirData->dMMSI == dMMSI)
            return (i);

        ++pAisDirData;
    }

    return (AIS_DIR_DATA_NOT_FOUND);
}

/**
 * @brief Find directory data index for base station
 * @param dMMSI MMSI
 * @return Directory data index
 */
int CUserDirMgr::FindDirDataIdxBS(DWORD dMMSI)
{
    return FindDirDataIdxFromTbl(m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM, dMMSI);
}

/**
 * @brief Find directory data index for mobile station
 * @param dMMSI MMSI
 * @return Directory data index
 */
int CUserDirMgr::FindDirDataIdxMS(DWORD dMMSI)
{
    return FindDirDataIdxFromTbl(m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM, dMMSI);
}

/**
 * @brief Find directory data index
 * @param dMMSI MMSI
 * @return Directory data index
 */
int CUserDirMgr::FindDirDataIdx(DWORD dMMSI)
{
    if(CAisLib::IsValidMMSI_BaseSt(dMMSI))
        return FindDirDataIdxBS(dMMSI);
    if(CAisLib::IsValidMMSI_MobileSt(dMMSI))
        return FindDirDataIdxBS(dMMSI);
    return AIS_DIR_DATA_NOT_FOUND;
}

/**
 * @brief Find directory data pointer for base station
 * @param dMMSI MMSI
 * @return Directory data pointer
 */
xDIRDATA* CUserDirMgr::FindDirDataPtrBS(DWORD dMMSI)
{
    int nDirIdx = FindDirDataIdxBS(dMMSI);
    if(nDirIdx != AIS_DIR_DATA_NOT_FOUND)
        return &m_vAisDirDataBS[nDirIdx];
    return NULL;
}

/**
 * @brief Find directory data pointer for mobile station
 * @param dMMSI MMSI
 * @return Directory data pointer
 */
xDIRDATA* CUserDirMgr::FindDirDataPtrMS(DWORD dMMSI)
{
    int nDirIdx = FindDirDataIdxMS(dMMSI);
    if(nDirIdx != AIS_DIR_DATA_NOT_FOUND)
        return &m_vAisDirDataMS[nDirIdx];
    return NULL;
}

/**
 * @brief Find directory data pointer
 * @param dMMSI MMSI
 * @return Directory data pointer
 */
xDIRDATA* CUserDirMgr::FindDirDataPtr(DWORD dMMSI)
{
    if(dMMSI == AIS_AB_MMSI_NULL)
        return NULL;
    if(CAisLib::IsValidMMSI_BaseSt(dMMSI))
        return FindDirDataPtrBS(dMMSI);
    return FindDirDataPtrMS(dMMSI);
}

/**
 * @brief Find empty slot number
 * @param pAisDirData User directory data
 * @param nMaxNo Maximum number of user directory data
 * @return Empty slot number
 */
int CUserDirMgr::FindEmptySlotNo(xDIRDATA *pAisDirData, int nMaxNo)
{
    int  nDirIdx = AIS_DIR_DATA_NOT_FOUND;

    // Find empty slot
    for(int i = 0; i < nMaxNo; i++)
    {
        if(pAisDirData[i].dMMSI == AIS_AB_MMSI_NULL)
        {
            nDirIdx = i;
            return nDirIdx;
        }
    }

    // Find oldest position report
    if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
    {
        // When there's no empty slot found, find a slot of which position report time is oldest.
        int nMaxElapsedSec = 0;
        for(int i = 0; i < nMaxNo; i++)
        {
            if(cTimerSys::getInst()->GetTimeDiffSec(pAisDirData[i].dwLastPosReportSec) > nMaxElapsedSec)
            {
                nMaxElapsedSec = cTimerSys::getInst()->GetTimeDiffSec(pAisDirData[i].dwLastPosReportSec);
                nDirIdx = i;
            }
        }
    }

    return nDirIdx;
}

/**
 * @brief Find empty slot number for base station
 * @return Empty slot number
 */
int CUserDirMgr::FindEmptySlotBaseSt()
{
    return FindEmptySlotNo(m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM);
}

/**
 * @brief Find empty slot number for mobile station
 * @return Empty slot number
 */
int CUserDirMgr::FindEmptySlotMobileSt()
{
    return FindEmptySlotNo(m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM);
}

/**
 * @brief Get directory data for base station
 * @param nDirIdx Directory data index
 * @return Directory data
 */
xDIRDATA *CUserDirMgr::GetDirDataBS(int nDirIdx)
{
    if(nDirIdx >= 0 && nDirIdx < AIS_MAX_DIR_BS_NUM)
        return (&m_vAisDirDataBS[nDirIdx]);

    return (NULL);
}

/**
 * @brief Get directory data for mobile station
 * @param nDirIdx Directory data index
 * @return Directory data
 */
xDIRDATA *CUserDirMgr::GetDirDataMS(int nDirIdx)
{
    if(nDirIdx >= 0 && nDirIdx < AIS_MAX_DIR_MS_NUM)
        return (&m_vAisDirDataMS[nDirIdx]);

    return NULL;
}

/**
 * @brief Get directory data by MMSI
 * @param nMMSI MMSI
 * @return Directory data
 */
xDIRDATA* CUserDirMgr::GetDirDataByMMSI(int nMMSI)
{
    int   nDirIdx;
    xDIRDATA *pDirData = NULL;

    if(nMMSI != AIS_AB_MMSI_NULL)
    {
        if(CAisLib::IsValidMMSI_BaseSt(nMMSI))
        {
            nDirIdx = FindDirDataIdxBS(nMMSI);
            pDirData= GetDirDataBS(nDirIdx);
        }
        else
        {
            nDirIdx = FindDirDataIdxMS(nMMSI);
            pDirData = GetDirDataMS(nDirIdx);
        }
    }
    return pDirData;
}

/**
 * @brief Detect received station type
 * @param nMMSI MMSI
 * @param nRcvMsgID Received message ID
 * @param nMsg18ClassBType Message 18 class B type
 * @return Received station type
 */
int CUserDirMgr::DetectRcvStationType(int nMMSI, int nRcvMsgID, int nMsg18ClassBType)
{
    // If MMSI is base station, return base station type
    if(CAisLib::IsValidMMSI_BaseSt(nMMSI))
        return AIS_ST_TYPE_BASE_STATION;

    // If MMSI is SAR, return SAR type
    if(CAisLib::IsValidMMSI_SAR(nMMSI))
        return AIS_ST_TYPE_SAR_AIRBORNE;

    // If message 05 is received, return class B type
    if(nRcvMsgID == AIS_MSG_NO_ID_05)
    {
        return AIS_ST_TYPE_CLASS_A_ONLY;
    }

    // If message 18 is received, return class B type
    if(nRcvMsgID == AIS_MSG_NO_ID_18)
    {
        switch (nMsg18ClassBType)
        {
        case CLASS_B_UNIT_SO:
            return AIS_ST_TYPE_CLASS_B_SOTDMA_ONLY;
        case CLASS_B_UNIT_CS:
            return AIS_ST_TYPE_CLASS_B_CSTDMA_ONLY;
        }
        return AIS_ST_TYPE_CLASS_B_ALL;
    }

    // If message 09 is received, return SAR type
    if(nRcvMsgID == AIS_MSG_NO_ID_09)
        return AIS_ST_TYPE_SAR_AIRBORNE;

    // If message 21 is received, return AtoN type
    if(nRcvMsgID == AIS_MSG_NO_ID_21)
        return AIS_ST_TYPE_ATON;

    // If MMSI is mobile station, return mobile station type
    if(CAisLib::IsValidMMSI_MobileSt(nMMSI))
        return AIS_ST_TYPE_ALL_MOBILES;
}

/**
 * @brief Get interrogated station type
 * @param nMMSI MMSI
 * @return Interrogated station type
 */
int CUserDirMgr::GetInterrogatedStType(int nMMSI)
{
    if(nMMSI == AIS_AB_MMSI_NULL)
        return AIS_ST_TYPE_INVALID;

    // If station type is already detected, return it
    xDIRDATA *pData = GetDirDataByMMSI(nMMSI);
    if(pData && pData->nStType != AIS_ST_TYPE_INVALID)
        return pData->nStType;

    // If MMSI is base station, return base station type
    if(CAisLib::IsValidMMSI_BaseSt(nMMSI))
        return AIS_ST_TYPE_BASE_STATION;

    // If MMSI is SAR, return SAR type
    if(CAisLib::IsValidMMSI_SAR(nMMSI))
        return AIS_ST_TYPE_SAR_AIRBORNE;

    // If MMSI is AtoN, return AtoN type
    if(CAisLib::IsValidMMSI_AtoN(nMMSI))
        return AIS_ST_TYPE_ATON;

    // If MMSI is mobile station, return mobile station type
    if(CAisLib::IsValidMMSI_MobileSt(nMMSI))
        return AIS_ST_TYPE_ALL_MOBILES;

    return AIS_ST_TYPE_INVALID;
}

/**
 * @brief Check if station type is mobile
 * @param nMMSI MMSI
 * @return True if station type is mobile, false otherwise
 */
bool CUserDirMgr::IsStationTypeMobile(int nMMSI)
{
    BYTE bStType = GetInterrogatedStType(nMMSI);
    return (bStType == AIS_ST_TYPE_ALL_MOBILES || bStType == AIS_ST_TYPE_CLASS_A_ONLY ||
            bStType == AIS_ST_TYPE_CLASS_B_SOTDMA_ONLY || bStType == AIS_ST_TYPE_CLASS_B_CSTDMA_ONLY);
}

/**
 * @brief Get last received channel
 * @param uMMSI MMSI
 * @return Last received channel
 */
UINT8 CUserDirMgr::GetLastRcvCh(UINT uMMSI)
{
    xDIRDATA *pData = NULL;
    if((pData = GetDirDataByMMSI(uMMSI)))
        return pData->bRcvChID;
    return AIS_CHANNEL_NONE;
}

/**
 * @brief Append directory data for base station
 * @return Directory data index
 */
int CUserDirMgr::AppendDirDataBS(void)
{
    int  nDirIdx;

    // Find empty slot
    nDirIdx = FindEmptySlotBaseSt();

    // If no empty slot found, return error
    if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
        return AIS_DIR_DATA_NOT_FOUND;

    m_vAisDirDataBS[nDirIdx].dMMSI         = m_dwSrcMMSI;
    m_vAisDirDataBS[nDirIdx].bRptIndicator = m_nRepeatIndicator;
    m_vAisDirDataBS[nDirIdx].bRcvChID      = m_nRxChID;
    m_vAisDirDataBS[nDirIdx].nLRTxCtrlCode = LR_TXCTRL_CODE_NA;
    m_vAisDirDataBS[nDirIdx].bLRInRegion   = FALSE;
    m_vAisDirDataBS[nDirIdx].nClassBType   = CLASS_B_UNIT_UNKNOWN;
    m_vAisDirDataBS[nDirIdx].nStType       = AIS_ST_TYPE_BASE_STATION;

    switch (m_nMsgID)
    {
    // Base station report
    case AIS_MSG_NO_ID_04:
        m_vAisDirDataBS[nDirIdx].bCanBeSyncSrc= TRUE;
        m_vAisDirDataBS[nDirIdx].bTimeStamp = m_nTimeStamp;
        m_vAisDirDataBS[nDirIdx].bPosValid  = m_nPosValid;

        if(m_fDistance != AIS_DIST_NM_INVALID)
        {
            m_vAisDirDataBS[nDirIdx].fDistance = m_fDistance;
            m_vAisDirDataBS[nDirIdx].xPosF     = m_xPosF;
        }

        m_vAisDirDataBS[nDirIdx].bNavStatus = AIS_NAV_STATUS_NONE_NONE;
        m_vAisDirDataBS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataBS[nDirIdx].uNumRcvStLast = m_nRxStnNums;

        // Update position report receive time
        UpdatePosReportRcvTime(m_vAisDirDataBS, nDirIdx, m_nRepeatIndicator);
        break;

    // Multiple slot binary message from Base station
    case AIS_MSG_NO_ID_26:
        m_vAisDirDataBS[nDirIdx].bNavStatus = AIS_NAV_STATUS_NONE_NONE;
        m_vAisDirDataBS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataBS[nDirIdx].uNumRcvStLast = m_nRxStnNums;
        break;
    }

    // Update number of stations
    if(m_nNumValidBaseSt < AIS_MAX_DIR_BS_NUM-1)
    {
        m_nNumValidBaseSt++;
        m_nOwnShipNumRcvStLast++;
    }

    DEBUG_LOG("DIR-BS] app, [%d]= %09d, stType: %d, numBS:%d, numMS:%d\r\n",
            nDirIdx, m_dwSrcMMSI, m_vAisDirDataBS[nDirIdx].nStType, m_nNumValidBaseSt, m_nNumValidMobileSt);

    return (nDirIdx);
}

/**
 * @brief Append directory data for mobile station
 * @return Directory data index
 */
int CUserDirMgr::AppendDirDataMS(void)
{
    int  nDirIdx;

    // Find empty slot
    nDirIdx = FindEmptySlotMobileSt();
    if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
        return AIS_DIR_DATA_NOT_FOUND;

    m_vAisDirDataMS[nDirIdx].dMMSI         = m_dwSrcMMSI;
    m_vAisDirDataMS[nDirIdx].bRptIndicator = m_nRepeatIndicator;
    m_vAisDirDataMS[nDirIdx].bRcvChID      = m_nRxChID;
    m_vAisDirDataMS[nDirIdx].bCanBeSyncSrc = FALSE;
    m_vAisDirDataMS[nDirIdx].nLRTxCtrlCode = LR_TXCTRL_CODE_NA;
    m_vAisDirDataMS[nDirIdx].bLRInRegion   = FALSE;
    m_vAisDirDataMS[nDirIdx].nStType       = AIS_ST_TYPE_INVALID;
    m_vAisDirDataMS[nDirIdx].nClassBType   = CLASS_B_UNIT_UNKNOWN;

    switch (m_nMsgID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
    case AIS_MSG_NO_ID_03:
        m_vAisDirDataMS[nDirIdx].bCanBeSyncSrc = TRUE;
        m_vAisDirDataMS[nDirIdx].bNavStatus = m_nNavStatus;

        m_vAisDirDataMS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataMS[nDirIdx].uNumRcvStLast = m_nRxStnNums;

        m_vAisDirDataMS[nDirIdx].bPosValid = m_nPosValid;
        if(m_fDistance != AIS_DIST_NM_INVALID)
        {
            m_vAisDirDataMS[nDirIdx].fDistance = m_fDistance;
            m_vAisDirDataMS[nDirIdx].xPosF       = m_xPosF;
        }

        UpdatePosReportRcvTime(m_vAisDirDataMS, nDirIdx, m_nRepeatIndicator);
        break;

    case AIS_MSG_NO_ID_18:
        m_vAisDirDataMS[nDirIdx].nClassBType= m_nMsg18ClassBType;
        UpdatePosReportRcvTime(m_vAisDirDataMS, nDirIdx, m_nRepeatIndicator);
    case AIS_MSG_NO_ID_09:
    case AIS_MSG_NO_ID_11:
        m_vAisDirDataMS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataMS[nDirIdx].uNumRcvStLast = m_nRxStnNums;

        m_vAisDirDataMS[nDirIdx].bPosValid = m_nPosValid;
        if(m_fDistance != AIS_DIST_NM_INVALID)
        {
            m_vAisDirDataMS[nDirIdx].fDistance = m_fDistance;
            m_vAisDirDataMS[nDirIdx].xPosF        = m_xPosF;
        }
        break;

    case AIS_MSG_NO_ID_19:
        UpdatePosReportRcvTime(m_vAisDirDataMS, nDirIdx, m_nRepeatIndicator);
    case AIS_MSG_NO_ID_21:
        m_vAisDirDataMS[nDirIdx].bPosValid = m_nPosValid;

        if(m_fDistance != AIS_DIST_NM_INVALID)
        {
            m_vAisDirDataMS[nDirIdx].fDistance = m_fDistance;
            m_vAisDirDataMS[nDirIdx].xPosF        = m_xPosF;
        }
        break;

    case AIS_MSG_NO_ID_26:
        m_vAisDirDataMS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataMS[nDirIdx].uNumRcvStLast = m_nRxStnNums;
        break;
    }

    m_vAisDirDataMS[nDirIdx].nStType = DetectRcvStationType(m_dwSrcMMSI, m_nMsgID, m_nMsg18ClassBType);

    if(m_nNumValidMobileSt < AIS_MAX_DIR_MS_NUM-1)
    {
        m_nNumValidMobileSt++;

        if(m_nMsg18ClassBType != CLASS_B_UNIT_CS)
        {
            m_nOwnShipNumRcvStLast++;
        }
    }

    DEBUG_LOG("DIR-MS] app, [%d]= %09d, msg: %d, msg18ClsBtype:%d, stType:%d, numBS:%d, numMS:%d, s:%d\r\n",
            nDirIdx, m_dwSrcMMSI, m_nMsgID, m_nMsg18ClassBType, m_vAisDirDataMS[nDirIdx].nStType, m_nNumValidBaseSt, m_nNumValidMobileSt, cTimerSys::getInst()->GetCurTimerSec());

    return (nDirIdx);
}

/**
 * @brief Update directory data for base station
 * @param nDirIdx Directory data index
 */
void CUserDirMgr::UpdateDirDataBS(int nDirIdx)
{
    // Msg 04, 16, 20, 26
    m_vAisDirDataBS[nDirIdx].bRptIndicator = m_nRepeatIndicator;
    m_vAisDirDataBS[nDirIdx].bRcvChID      = m_nRxChID;
    m_vAisDirDataBS[nDirIdx].nClassBType   = CLASS_B_UNIT_UNKNOWN;
    m_vAisDirDataBS[nDirIdx].nStType       = AIS_ST_TYPE_BASE_STATION;

    switch (m_nMsgID)
    {
    // Base station report
    case AIS_MSG_NO_ID_04:
        m_vAisDirDataBS[nDirIdx].bCanBeSyncSrc = TRUE;
        m_vAisDirDataBS[nDirIdx].bTimeStamp = m_nTimeStamp;
        m_vAisDirDataBS[nDirIdx].bPosValid  = m_nPosValid;
        m_vAisDirDataBS[nDirIdx].fDistance  = m_fDistance;
        m_vAisDirDataBS[nDirIdx].xPosF      = m_xPosF;

        m_vAisDirDataBS[nDirIdx].bNavStatus = m_nNavStatus;
        m_vAisDirDataBS[nDirIdx].bSyncState = m_nSyncState;

        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataBS[nDirIdx].uNumRcvStLast= m_nRxStnNums;

        UpdatePosReportRcvTime(m_vAisDirDataBS, nDirIdx, m_nRepeatIndicator);
        break;

    // Multiple slot binary message from Base station
    case AIS_MSG_NO_ID_26:
        m_vAisDirDataBS[nDirIdx].bNavStatus = m_nNavStatus;
        m_vAisDirDataBS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataBS[nDirIdx].uNumRcvStLast= m_nRxStnNums;
        break;
    }
}

/**
 * @brief Update directory data for mobile station
 * @param nDirIdx Directory data index
 */
void CUserDirMgr::UpdateDirDataMS(int nDirIdx)
{
    m_vAisDirDataMS[nDirIdx].bRptIndicator      = m_nRepeatIndicator;
    m_vAisDirDataMS[nDirIdx].bRcvChID = m_nRxChID;
    m_vAisDirDataMS[nDirIdx].nLRTxCtrlCode    = LR_TXCTRL_CODE_NA;
    m_vAisDirDataMS[nDirIdx].bLRInRegion    = FALSE;

    if(m_nTimeStamp != AIS_DIR_DATA_TIME_STAMP_NULL)
        m_vAisDirDataMS[nDirIdx].bTimeStamp = m_nTimeStamp;

    switch (m_nMsgID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
    case AIS_MSG_NO_ID_03:
        m_vAisDirDataMS[nDirIdx].bCanBeSyncSrc = TRUE;
        m_vAisDirDataMS[nDirIdx].bNavStatus = m_nNavStatus;

        m_vAisDirDataMS[nDirIdx].nClassBType= CLASS_B_UNIT_UNKNOWN;
        m_vAisDirDataMS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataMS[nDirIdx].uNumRcvStLast= m_nRxStnNums;

        m_vAisDirDataMS[nDirIdx].bPosValid = m_nPosValid;
        m_vAisDirDataMS[nDirIdx].fDistance = m_fDistance;
        m_vAisDirDataMS[nDirIdx].xPosF        = m_xPosF;

        UpdatePosReportRcvTime(m_vAisDirDataMS, nDirIdx, m_nRepeatIndicator);
        break;

    case AIS_MSG_NO_ID_05:
        m_vAisDirDataMS[nDirIdx].nClassBType= CLASS_B_UNIT_UNKNOWN;
        break;

    case AIS_MSG_NO_ID_18:
        m_vAisDirDataMS[nDirIdx].nClassBType= m_nMsg18ClassBType;
    case AIS_MSG_NO_ID_09:
    case AIS_MSG_NO_ID_11:
        m_vAisDirDataMS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataMS[nDirIdx].uNumRcvStLast= m_nRxStnNums;

        m_vAisDirDataMS[nDirIdx].bPosValid = m_nPosValid;
        m_vAisDirDataMS[nDirIdx].fDistance = m_fDistance;
        m_vAisDirDataMS[nDirIdx].xPosF        = m_xPosF;

        if(m_nMsgID == AIS_MSG_NO_ID_18)
            UpdatePosReportRcvTime(m_vAisDirDataMS, nDirIdx, m_nRepeatIndicator);
        break;

    case AIS_MSG_NO_ID_19:
        UpdatePosReportRcvTime(m_vAisDirDataMS, nDirIdx, m_nRepeatIndicator);
    case AIS_MSG_NO_ID_21:
        m_vAisDirDataMS[nDirIdx].bPosValid = m_nPosValid;
        m_vAisDirDataMS[nDirIdx].fDistance = m_fDistance;
        m_vAisDirDataMS[nDirIdx].xPosF        = m_xPosF;
        break;

    case AIS_MSG_NO_ID_26:
        m_vAisDirDataMS[nDirIdx].bSyncState = m_nSyncState;
        if(m_nRxStnNums != AIS_DIR_DATA_RX_STN_NUM_NULL)
            m_vAisDirDataMS[nDirIdx].uNumRcvStLast= m_nRxStnNums;
        break;
    }

    int nMsg18ClassBType = m_nMsg18ClassBType;
    if(m_vAisDirDataMS[nDirIdx].nClassBType != CLASS_B_UNIT_UNKNOWN)
        nMsg18ClassBType = m_vAisDirDataMS[nDirIdx].nClassBType;

    // Update station type
    m_vAisDirDataMS[nDirIdx].nStType = DetectRcvStationType(m_dwSrcMMSI, m_nMsgID, nMsg18ClassBType);

    DEBUG_LOG("DIR-MS] upd, [%d]= %09d, msg:%d, msg18ClsBtype:%d, stType: %d, nBS:%d, numMS:%d, s: %d\r\n",
            nDirIdx, m_dwSrcMMSI, m_nMsgID, nMsg18ClassBType, m_vAisDirDataMS[nDirIdx].nStType, m_nNumValidBaseSt, m_nNumValidMobileSt, cTimerSys::getInst()->GetCurTimerSec());
}

/**
 * @brief Update directory data
 * @param pRxMgr Channel VDL receive data
 */
void CUserDirMgr::UpdateDirData(CVdlRxMgr *pRxMgr)
{
    int  nDirIdx;

    // Clear all parameters
    ClearAllParameter();

    m_nRxChID     = pRxMgr->m_pChannel->GetChOrdinal();
    m_pRxAisMsg99 = &pRxMgr->m_xRxAisMsg;

    // Set message ID and source MMSI
    m_nMsgID           = m_pRxAisMsg99->xMsg00.dMsgID;
    m_nRepeatIndicator = m_pRxAisMsg99->xMsg00.dRptInd;
    m_dwSrcMMSI        = m_pRxAisMsg99->xMsg00.dSrcMMSI;

    // Set parameters
    SetClassBType();
    SetNavStatus();
    SetSyncState();
    SetDistance();
    SetRxStnNums();
    SetTimeStamp();

    // If base station
    if(CAisLib::IsValidMMSI_BaseSt(m_dwSrcMMSI))
    {
        nDirIdx = FindDirDataIdxBS(m_dwSrcMMSI);
        if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
        {
            // Append new base station data
            AppendDirDataBS();
        }
        else
        {
            // Update existing base station data
            UpdateDirDataBS(nDirIdx);
        }
    }
    else
    {
        // find mobile station
        nDirIdx = FindDirDataIdxMS(m_dwSrcMMSI);
        if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
        {
            // Append new mobile station data
            AppendDirDataMS();
        }
        else
        {
            // Update existing mobile station data
            UpdateDirDataMS(nDirIdx);
        }
    }
}

/**
 * @brief Set last slot reuse time tick
 * @param uMMSI MMSI
 */ 
void CUserDirMgr::SetLastSlotReuseTimeTick(UINT uMMSI)
{
    xDIRDATA *pDirData = FindDirDataPtr(uMMSI);
    if(pDirData)
    {
        pDirData->dwLastSlotReuseSec = cTimerSys::getInst()->GetCurTimerSec();

        DEBUG_LOG("SetReuseSec] %09d, %d, s:%d\r\n",
                uMMSI, pDirData->dwLastSlotReuseSec, cTimerSys::getInst()->GetCurTimerSec());
    }
    else
    {
        DEBUG_LOG("SetReuseSec] Dir data NULL, %09d, s:%d\r\n",
                uMMSI, cTimerSys::getInst()->GetCurTimerSec());
    }
}

/**
 * @brief Set long range transmission control code
 * @param uBaseStMMSI Base station MMSI
 * @param nLRTxCtrlCode Long range transmission control code
 */
void CUserDirMgr::SetLongRangeTxCtrlCode(UINT uBaseStMMSI, INT8 nLRTxCtrlCode)
{
    xDIRDATA *pDirData = FindDirDataPtrBS(uBaseStMMSI);
    if(pDirData)
    {
        pDirData->nLRTxCtrlCode    = nLRTxCtrlCode;
        pDirData->dwLRCtrlRcvSec = cTimerSys::getInst()->GetCurTimerSec();

        DEBUG_LOG("[LR-VDL] TxCtrlCodeByMsg4, %09d, inRegion:%d,sec:%d, ctrlCode:%d,sec:%d, s:%d\r\n",
                uBaseStMMSI, pDirData->bLRInRegion, pDirData->dwLRInRegionSec,
                pDirData->nLRTxCtrlCode, pDirData->dwLRCtrlRcvSec, cTimerSys::getInst()->GetCurTimerSec());
    }
}

/**
 * @brief Set long range transmission in region
 * @param uBaseStMMSI Base station MMSI
 * @param bPosInRegion True if in region, false otherwise
 */
void CUserDirMgr::SetLongRangeTxInRegion(UINT uBaseStMMSI, bool bPosInRegion)
{
    xDIRDATA *pDirData = FindDirDataPtrBS(uBaseStMMSI);
    if(pDirData)
    {
        pDirData->bLRInRegion        = bPosInRegion;
        pDirData->dwLRInRegionSec    = cTimerSys::getInst()->GetCurTimerSec();

        DEBUG_LOG("[LR-VDL] InRegion, %09d, inRegion:%d,sec:%d, ctrlCode:%d,sec:%d, s:%d\r\n",
            uBaseStMMSI, pDirData->bLRInRegion, pDirData->dwLRInRegionSec,
            pDirData->nLRTxCtrlCode, pDirData->dwLRCtrlRcvSec, cTimerSys::getInst()->GetCurTimerSec());
    }
}

/**
 * @brief Get own ship number of received stations for 9 minutes
 * @return Own ship number of received stations for 9 minutes
 */
int CUserDirMgr::GetOwnShipNumOfRcvStFor9Min(void)
{
    //---------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ********
    // Class B "CS" are not included in the number of received stations
    //---------------------------------------------------------------------------------------------------------
    // When generating SOTDMA messages, for commstat sub messages 3, 5, 7:
    // -> When calculating the number of stations received by own ship, own ship and class B "CS" should not be included!
    //---------------------------------------------------------------------------------------------------------
    return m_nOwnShipNumRcvSt9min;
}

/**
 * @brief Get own ship number of received stations for 1 minute
 * @return Own ship number of received stations for 1 minute
 */
int CUserDirMgr::GetOwnShipNumOfRcvStFor1Min(void)
{
    //---------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ********
    // Class B "CS" are not included in the number of received stations
    //---------------------------------------------------------------------------------------------------------
    return m_nOwnShipNumRcvSt9min / 9;
}

/**
 * @brief Check obsolete user directory data
 * @param pAisDirData User directory data
 * @param nMaxNo Maximum number of user directory data
 * @param pNoOfItems Number of items
 * @return True if changed, false otherwise
 */
bool CUserDirMgr::CheckObsoleteUserDirData(xDIRDATA *pAisDirData, int nMaxNo, int *pNoOfItems)
{
    bool bChged = false;

    // If the number of items is less than or equal to zero, return false.
    if(*pNoOfItems <= 0)
        return false;

    // Check obsolete user directory data for base station.
    for(int idx = 0 ; idx < nMaxNo ; idx++)
    {
        if(pAisDirData->dMMSI != AIS_AB_MMSI_NULL)
        {
            // Delete user directory data if not received for 9 minutes.
            if(cTimerSys::getInst()->GetTimeDiffSec(pAisDirData->dwLastPosReportSec) > NUM_RCV_ST_SAVE_MIN)
            {
                DEBUG_LOG("DIR] del-begin, %09d, num:%d, posRcvElap:%d\r\n",
                        pAisDirData->dMMSI, *pNoOfItems, cTimerSys::getInst()->GetTimeDiffSec(pAisDirData->dwLastPosReportSec));

                if(*pNoOfItems > 0)
                {
                    (*pNoOfItems)--;

                    if(pAisDirData->nClassBType != CLASS_B_UNIT_CS)
                    {
                        if(m_nOwnShipNumRcvStLast > 0)
                            m_nOwnShipNumRcvStLast--;
                    }
                }

                ClearDirDataOne(pAisDirData);
                bChged = true;
            }
        }
        ++pAisDirData;
    }

    return bChged;
}

/**
 * @brief Update distance for all stations
 * @param pDirList Directory data list
 * @param nNumSt Number of directory data
 */
void CUserDirMgr::UpdateStationDistance(xDIRDATA *pDirList, int nNumSt)
{
    // If the position is not fixed, return.
    if(!CSensorMgr::getInst()->IsGnssFixed() || CAisLib::IsValidAisGridPOS(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG))
        return;

    // Update distance for all stations.
    for(int idx = 0 ; idx < nNumSt ; idx++)
    {
        pDirList[idx].fDistance = AIS_DIST_NM_INVALID;

        if(pDirList[idx].dMMSI != AIS_AB_MMSI_NULL)
        {
            if(pDirList[idx].bPosValid && CAisLib::IsValidAisFloatPOS(&pDirList[idx].xPosF))
            {
                pDirList[idx].fDistance = CGps::GetDistanceByFLOAT(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, 
                                                                    cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON,
                                                                    pDirList[idx].xPosF.fLAT, pDirList[idx].xPosF.fLON, DIST_UNIT_NM);
            }
        }
    }
}

/**
 * @brief Update distance for all stations
 */
void CUserDirMgr::UpdateStationDistance(void)
{
    static DWORD dwCheckSec = 0;
    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > 10)
    {
        UpdateStationDistance(m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM);
        UpdateStationDistance(m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM);
        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}

/**
 * @brief Check if the station position is valid
 * @param dMMSI MMSI
 * @return True if valid, false otherwise
 */
bool CUserDirMgr::IsStationPosAvail(DWORD dMMSI)
{
    //--------------------------------------------------------------------------------------------------------
    // ITU-R M.1371-5 ed.2014 Appndix2 ******* Candidate slots
    // The slots of another station, whose navigational status is not set to "at anchor" or "moored" and has
    // not been received for 3 min, should be used as candidate slots for intentional slot reuse.
    //--------------------------------------------------------------------------------------------------------

    xDIRDATA *pDirData;

    if(!(pDirData = CUserDirMgr::getInst()->FindDirDataPtr(dMMSI)))
        return false;

    return (pDirData->bPosValid && pDirData->fDistance != AIS_DIST_NM_INVALID);
}

/**
 * @brief Check if the base station position is valid
 * @param dMMSI MMSI
 * @return True if valid, false otherwise
 */
bool CUserDirMgr::IsBaseStationPosValid(DWORD dMMSI)
{
    int   nDirIdx;
    xDIRDATA *pDirData;

    if(!CAisLib::IsValidMMSI_BaseSt(dMMSI))
    {
        return false;
    }
    nDirIdx = FindDirDataIdxBS(dMMSI);
    if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
    {
        return false;
    }

    pDirData = GetDirDataBS(nDirIdx);
    if(pDirData == NULL)
    {
        return false;
    }

    BOOL bRet = (pDirData->bPosValid && pDirData->fDistance != AIS_DIST_NM_INVALID && cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastPosReportSec) <= POS_VALID_SEC);
    if(!bRet)
    {
        DEBUG_LOG("IsBaseSt120NM] ptr:%x, posValid:%d, dist:%.1f, rcvPosElapSec:%d\r\n",
                pDirData, pDirData->bPosValid, pDirData->fDistance, cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastPosReportSec));
    }

    return bRet;
}

/**
 * @brief Check if the base station is within 120 nautical miles
 * @param dMMSI MMSI
 * @return True if within 120 nautical miles, false otherwise
 */
bool CUserDirMgr::IsBaseStationWithin120NM(DWORD dMMSI)
{
    int   nDirIdx;
    xDIRDATA *pDirData;

    if(!CAisLib::IsValidMMSI_BaseSt(dMMSI))
    {
        WARNING_LOG("IsBaseSt120NM] ignore, not BS, %09d\r\n", dMMSI);
        return false;
    }

    nDirIdx = FindDirDataIdxBS(dMMSI);
    if(nDirIdx == AIS_DIR_DATA_NOT_FOUND)
    {
        WARNING_LOG("IsBaseSt120NM] not registered BS, %09d\r\n", dMMSI);
        return false;
    }

    pDirData = GetDirDataBS(nDirIdx);
    if(pDirData == NULL)
    {
        WARNING_LOG("IsBaseSt120NM] ptr NULL! %d\r\n", dMMSI);
        return false;
    }

    if(CSensorMgr::getInst()->IsGnssFixed())                                                            // 자선 위치 lost 시에는 자선의 마지막위치와 상관없이 해당 기지국이 120NM 내에 있는 것으로 간주한다!
    {
        if(pDirData->bPosValid == 0 || pDirData->fDistance == AIS_DIST_NM_INVALID)
        {
            DEBUG_LOG("IsBaseSt120NM] posValid:%d, dist:%.1f\r\n", pDirData->bPosValid, pDirData->fDistance);
            return false;
        }

        if(pDirData->fDistance > AIS_DIST_NM_VALID_MAX)
        {
            DEBUG_LOG("IsBaseSt120NM] ownPosValid:%d, ptr:%x, posValid:%d, dist:%.1f\r\n",
                    CSensorMgr::getInst()->IsGnssFixed(), pDirData, pDirData->bPosValid, pDirData->fDistance);
            return false;
        }

        if(cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastPosReportSec) > POS_VALID_SEC)
        {
            DEBUG_LOG("IsBaseSt120NM] pos data too old, elap:%d, %d\r\n",
                    cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastPosReportSec), pDirData->dwLastPosReportSec);
            return false;
        }
    }
    return true;
}

/**
 * @brief Update position report receive time
 * @param pDirList Directory data list
 * @param nDirIdx Directory data index
 * @param nRepeatIndicator Repeat indicator
 */
void CUserDirMgr::UpdatePosReportRcvTime(xDIRDATA *pDirList, int nDirIdx, int nRepeatIndicator)
{
    if(!nRepeatIndicator)
    {
        pDirList[nDirIdx].dwLastNonRepeatPosReportSec[IDX_POS_PREV] = pDirList[nDirIdx].dwLastNonRepeatPosReportSec[IDX_POS_LAST];
        pDirList[nDirIdx].dwLastNonRepeatPosReportSec[IDX_POS_LAST] = cTimerSys::getInst()->GetCurTimerSec();
    }

    pDirList[nDirIdx].dwLastPosReportSec = cTimerSys::getInst()->GetCurTimerSec();
}

/** 
 * @brief Check if two position reports have been received within the last 40 seconds
 * @param pDirData Directory data
 * @return True if two position reports have been received within the last 40 seconds, false otherwise
 */
bool CUserDirMgr::CheckTwoPosRcvWithin40sec(xDIRDATA *pDirData)
{
    //----------------------------------------------------------------------------------------------------------------------
    // refer to ITU-R-M 1371-5 3.1.1.4
    // A station, which is unable to attain UTC direct or UTC indirect synchronization and is also unable
    // to receive transmissions from a base station, should synchronize to the station indicating the highest
    // number of other stations received during the last nine frames, provided that two reports have been
    // received from that station in the last 40s.
    //----------------------------------------------------------------------------------------------------------------------
    if(!pDirData)
        return false;

    return (cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastNonRepeatPosReportSec[IDX_POS_PREV]) <= SEMAPHORE_CHECKTIME_SEC);
}

/**
 * @brief Check if two position reports have been received within the last 40 seconds
 * @param uMMSI MMSI
 * @return True if two position reports have been received within the last 40 seconds, false otherwise
 */
bool CUserDirMgr::CheckTwoPosRcvWithin40sec(DWORD uMMSI)
{
    return CheckTwoPosRcvWithin40sec(FindDirDataPtr(uMMSI));
}

/**
 * @brief Check long range transmission control code by base station
 * @return Long range transmission control code
 */
uint8_t CUserDirMgr::CheckLongRangeTxCtrlByBaseSt(void)
{
    //------------------------------
    // IEC-61993-2 20.2.2.2 c)
    //------------------------------

    xDIRDATA *pDirData = CUserDirMgr::getInst()->m_vAisDirDataBS;
    INT8 nCtrlCode = LR_TXCTRL_CODE_NA;

    int nInRegionCnt = 0;
    int nSameCtrlCodeCnt = 0;
    for(int i = 0 ; i < AIS_MAX_DIR_BS_NUM ; i++)
    {
        if (pDirData->dMMSI != AIS_AB_MMSI_NULL)
        {
            if(pDirData->bLRInRegion)
            {
                if(nCtrlCode == pDirData->nLRTxCtrlCode)
                    nSameCtrlCodeCnt++;
                else
                {
                    nSameCtrlCodeCnt = 1;
                    nCtrlCode = pDirData->nLRTxCtrlCode;
                }

                nInRegionCnt++;
                if(nInRegionCnt != nSameCtrlCodeCnt)
                {
                    nCtrlCode = LR_TXCTRL_CODE_NA;
                    break;
                }
            }
        }
        ++pDirData;
    }

    DEBUG_LOG("[LR-VDL] CheckTxCtrl] end, ctrl:%d s:%d\r\n", nCtrlCode, cTimerSys::getInst()->GetCurTimerSec());

    return (nCtrlCode);
}

/**
 * @brief Check long range region data invalid
 */
void CUserDirMgr::CheckLongRangeRegionDataInvalid(void)
{
    //-----------------------------------------------------------------------------------------
    // refer to 1371-5 Annex4. 3.3.3 Automatic identification system shore station qualifier
    //-----------------------------------------------------------------------------------------

    const int LR_CTRL_TIMEOUT_SEC = LR_REPORT_INTERVAL_SEC; // 3 min
    xDIRDATA *pDirData = m_vAisDirDataBS;

    for(int i = 0 ; i < AIS_MAX_DIR_BS_NUM ; i++)
    {
        if(pDirData->dMMSI != AIS_AB_MMSI_NULL)
        {
            if (pDirData->nLRTxCtrlCode != LR_TXCTRL_CODE_NA && cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLRCtrlRcvSec) >= LR_CTRL_TIMEOUT_SEC)
            {
                pDirData->nLRTxCtrlCode = LR_TXCTRL_CODE_NA;
                pDirData->dwLRCtrlRcvSec= TIMER_SEC_NA;
            }
            if (pDirData->bLRInRegion && cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLRInRegionSec) >= LR_CTRL_TIMEOUT_SEC)
            {
                pDirData->bLRInRegion     = FALSE;
                pDirData->dwLRInRegionSec= TIMER_SEC_NA;
            }

            DEBUG_LOG("CheckLongRangeRegionDataInvalid] after, %09d, ctrl:%d,elap:%d, inRegion:%d,elap:%d, s:%d\r\n",
                pDirData->dMMSI, pDirData->nLRTxCtrlCode, cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLRCtrlRcvSec),
                pDirData->bLRInRegion, cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLRInRegionSec),
                cTimerSys::getInst()->GetCurTimerSec());
        }
        ++pDirData;
    }
}

/**
 * @brief Get the highest received synchronization state value
 * @return Highest received synchronization state value
 */
int CUserDirMgr::GetHighPrioritySyncState(void)
{
    // Get the high priority received synchronization state vale
    int nMaxSyncStat = AIS_SYNC_MODE_UTC_DIRECT;
    
    for (int i = 0 ; i < AIS_MAX_DIR_BS_NUM ; i++)
    {
        if(m_vAisDirDataBS[i].bSyncState != AIS_SYNC_MODE_NONE)
            nMaxSyncStat = MAX(nMaxSyncStat, m_vAisDirDataBS[i].bSyncState);
    }

    for (int i = 0 ; i < AIS_MAX_DIR_MS_NUM ; i++)
    {
        if(m_vAisDirDataMS[i].bSyncState != AIS_SYNC_MODE_NONE)
            nMaxSyncStat = MAX(nMaxSyncStat, m_vAisDirDataMS[i].bSyncState);
    }

    return nMaxSyncStat;
}

/**
 * @brief Check if it's able to be UTC indirect synchronization source
 * @param pDirData Directory data
 */
bool CUserDirMgr::IsUtcIndirectSyncSrcCandi(xDIRDATA *pDirData)
{
    if(!pDirData)
        return false;

    // check if it's able to be UTC indirect synchronization source
    return (pDirData->dMMSI != AIS_AB_MMSI_NULL && pDirData->dMMSI != cShip::getOwnShipInst()->xStaticData.dMMSI && pDirData->bRptIndicator == 0 &&
            pDirData->bSyncState == AIS_SYNC_MODE_UTC_DIRECT && pDirData->bCanBeSyncSrc &&
            pDirData->bPosValid && (0 < pDirData->fDistance && pDirData->fDistance <= AIS_DIST_NM_VALID_MAX) &&
            cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastPosReportSec) < UTCINDIRECT_SYNC_HOLD_SEC);
}

/**
 * @brief Check if it's able to be UTC indirect synchronization source
 * @param uMMSI MMSI
 */
bool CUserDirMgr::IsUtcIndirectSyncSrcCandi(DWORD uMMSI)
{
    return IsUtcIndirectSyncSrcCandi(FindDirDataPtr(uMMSI));
}

/**
 * @brief Find UTC indirect synchronization candidate
 * @param pDirList Directory data list
 * @param nNumSt Number of directory data
 * @return UTC indirect synchronization candidate
 */
DWORD CUserDirMgr::FindUtcIndirectCandidate(xDIRDATA *pDirList, int nNumSt)
{
    //---------------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 3.1.1.2 Coordinated universal time indirect
    // A station, which is unable to get direct access to UTC, but can receive other stations that indicate
    // UTC direct, should synchronize to those stations. It should then change its synchronization state to
    // UTC indirect. Only one level of UTC indirect synchronization is allowed.
    //---------------------------------------------------------------------------------------------------------

    int nDirIdxMinPosRI = -1;
    int nMinPosRI = 30000;
    int nHitCnt = 0;

    int nDirRI;
    int nDirLastPosElap;
    DWORD dwDirLastPosSec;

    for(int i = 0 ; i < nNumSt ; i++)
    {
        if(pDirList[i].dMMSI != AIS_AB_MMSI_NULL && pDirList[i].bRptIndicator == 0)
        {
            nDirRI = -1;
            dwDirLastPosSec = pDirList[i].dwLastPosReportSec;

            nDirLastPosElap = cTimerSys::getInst()->GetTimeDiffSec(dwDirLastPosSec);

            // UTC indirect synchronization source candidate
            if(IsUtcIndirectSyncSrcCandi(&(pDirList[i])) && nDirLastPosElap < 60)
            {
                nDirRI = CAisLib::GetDiffDwordMax(pDirList[i].dwLastNonRepeatPosReportSec[IDX_POS_PREV], pDirList[i].dwLastNonRepeatPosReportSec[IDX_POS_LAST]);
                if(nDirRI < 60 && nDirRI < nMinPosRI)
                {
                    nDirIdxMinPosRI = i;
                    nMinPosRI = nDirRI;

                    // To boost performance
                    if(++nHitCnt > 100)
                        break;
                }
            }
        }
    }

    if (nDirIdxMinPosRI >= 0)
    {
        DEBUG_LOG("FindUtcIndirectCandidate] UtcIndi, end, %09d, RI:%d(%d,%d), s:%d\r\n",
                pDirList[nDirIdxMinPosRI].dMMSI, nMinPosRI, pDirList[nDirIdxMinPosRI].dwLastNonRepeatPosReportSec[IDX_POS_PREV], pDirList[nDirIdxMinPosRI].dwLastNonRepeatPosReportSec[IDX_POS_LAST],
                cTimerSys::getInst()->GetCurTimerSec());

        return pDirList[nDirIdxMinPosRI].dMMSI;
    }

    DEBUG_LOG("FindUtcIndirectCandidate] UtcIndi, %cS, end, Not found, %x, numSt:%d\r\n", pDirList == m_vAisDirDataBS ? 'B' : 'M', pDirList, nNumSt);
    return AIS_AB_MMSI_NULL;
}

/**
 * @brief Get UTC indirect synchronization source
 */
DWORD CUserDirMgr::GetSyncSrcForUtcIndirect(void)
{
    DWORD uMMSI = FindUtcIndirectCandidate(m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM);
    if(uMMSI == AIS_AB_MMSI_NULL)
    {
        uMMSI = FindUtcIndirectCandidate(m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM);
    }

    return uMMSI;
}

/**
 * @brief Check if it's able to be Base station Direct synchronization source
 * @param pDirData Directory data
 */
bool CUserDirMgr::IsBaseDirectSyncSrcCandi(xDIRDATA *pDirData)
{
    if(!pDirData)
        return false;

    // check if it's able to be Base station Direct synchronization source (base station semaphore)
    return ((pDirData->bSyncState == AIS_SYNC_MODE_UTC_INDIRECT || pDirData->bSyncState == AIS_SYNC_MODE_BASE_DIRECT ||
            pDirData->bSyncState == AIS_SYNC_MODE_BASE_INDIRECT) &&
            pDirData->bCanBeSyncSrc &&
            pDirData->bPosValid &&
            pDirData->bTimeStamp <= SEMAPHORE_VALID_TIMESTAMP_MAX &&
            CheckTwoPosRcvWithin40sec(pDirData));
}

/**
 * @brief Check if it's able to be Base station Direct synchronization source
 * @param uMMSI MMSI
 */
bool CUserDirMgr::IsBaseDirectSyncSrcCandi(DWORD uMMSI)
{
    return IsBaseDirectSyncSrcCandi(FindDirDataPtr(uMMSI));
}

/**
 * @brief Get Base station Direct synchronization source
 */
DWORD CUserDirMgr::GetSyncSrcForBaseDirect()
{
    //---------------------------------------------------------------------------------------------------------
    // refer to ITU-R-M 1371-5 *******
    // Mobile stations, which are unable to attain direct or indirect UTC synchronization, but are able to
    // receive transmissions from base stations, should synchronize to the base station which indicates the
    // highest number of received stations, provided that two reports have been received from that station
    // in the last 40s. Once base station synchronization has been established, this synchronization shall
    // be discontinued if fewer than two reports are received from the selected base station in the last 40s.
    // -> Find a basee station semaphore!
    //---------------------------------------------------------------------------------------------------------

    UINT16 uMaxRcvSt = 0;
    INT16 nDirIdxSema = AIS_DIR_DATA_NOT_FOUND;

    for (int i = 0 ; i < AIS_MAX_DIR_BS_NUM ; i++)
    {
        if(m_vAisDirDataBS[i].dMMSI != AIS_AB_MMSI_NULL)
        {
            if (IsBaseDirectSyncSrcCandi(&(m_vAisDirDataBS[i])) && m_vAisDirDataBS[i].uNumRcvSt9min > uMaxRcvSt)
            {
                uMaxRcvSt = m_vAisDirDataBS[i].uNumRcvSt9min;
                nDirIdxSema = i;
            }
        }
    }

    if(nDirIdxSema != AIS_DIR_DATA_NOT_FOUND)
    {
        DEBUG_LOG("GetSyncSrcForBaseDirect] end, %09d, rcvSt:%d, lastRcvElap:%d\r\n",
                m_vAisDirDataBS[nDirIdxSema].dMMSI, uMaxRcvSt, cTimerSys::getInst()->GetTimeDiffSec(m_vAisDirDataBS[nDirIdxSema].dwLastNonRepeatPosReportSec[IDX_POS_LAST]));

        return m_vAisDirDataBS[nDirIdxSema].dMMSI;
    }

    return AIS_AB_MMSI_NULL;
}

/**
 * @brief Check if it's able to be Base station Indirect synchronization source
 * @param pDirData Directory data
 */
bool CUserDirMgr::IsBaseIndirectSyncSrcCandi(xDIRDATA *pDirData)
{
    if(!pDirData)
        return false;

    // check if it's able to be Base station Indirect synchronization source
    return (pDirData->bSyncState == AIS_SYNC_MODE_BASE_DIRECT && pDirData->bCanBeSyncSrc && pDirData->bRptIndicator == 0 && 
            CheckTwoPosRcvWithin40sec(pDirData));
}

/**
 * @brief Check if it's able to be Base station Indirect synchronization source
 * @param uMMSI MMSI
 */
bool CUserDirMgr::IsBaseIndirectSyncSrcCandi(DWORD uMMSI)
{
    return IsBaseDirectSyncSrcCandi(FindDirDataPtrBS(uMMSI));
}

/**
 * @brief Get Base station Indirect synchronization candidate
 * @param pDirList Directory data list
 * @param nNumSt Number of directory data
 * @return Base station Indirect synchronization candidate
 */
DWORD CUserDirMgr::GetSyncSrcForBaseIndirectCandi(xDIRDATA *pDirList, int nNumSt)
{
    //-------------------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 ******* Synchronized to base station (direct or indirect)
    // A station that has Sync. state = 3 (see § 3.1.3.4.3) shall synchronize to a station that has Sync. state = 2
    // (see § 3.1.3.4.3) if no base station or station with UTC direct is available. Only one level of indirect
    // access to the base station is allowed.
    //-------------------------------------------------------------------------------------------------------------
    int nDirIdx = -1;

    for(int i = 0 ; i < nNumSt ; i++)
    {
        if(pDirList[i].dMMSI != AIS_AB_MMSI_NULL)
        {
            DEBUG_LOG("GetSyncSrcForBaseIndirectCandi] #%d, %09d, sy:%d, canBe:%d, lastRcvElap:%d\r\n",
                    i, pDirList[i].dMMSI, pDirList[i].bSyncState, pDirList[i].bCanBeSyncSrc,
                    cTimerSys::getInst()->GetTimeDiffSec(pDirList[i].dwLastNonRepeatPosReportSec[IDX_POS_LAST]));

            if(IsBaseIndirectSyncSrcCandi(&(pDirList[i])))
            {
                nDirIdx = i;
                break;
            }
        }
    }

    if(nDirIdx >= 0)
    {
        return pDirList[nDirIdx].dMMSI;
    }

    return AIS_AB_MMSI_NULL;
}

/**
 * @brief Get Base station Indirect synchronization source
 */
DWORD CUserDirMgr::GetSyncSrcForBaseIndirect()
{
    // Find a mobile station which is synchronized to a base station
    return GetSyncSrcForBaseIndirectCandi(m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM);
}

/**
 * @brief Check if it's able to be Mobile semaphore synchronization source
 * @param pDirData Directory data
 */
bool CUserDirMgr::IsMobileSemaSyncSrcCandi(xDIRDATA *pDirData)
{
    if(!pDirData)
        return false;

    // check if it's able to be Mobile semaphore synchronization source
    return ((pDirData->bSyncState == AIS_SYNC_MODE_UTC_INDIRECT ||
            pDirData->bSyncState == AIS_SYNC_MODE_BASE_INDIRECT || pDirData->bSyncState == AIS_SYNC_MODE_MOBILE_SEMAPHORE) &&
            pDirData->bCanBeSyncSrc &&
            pDirData->bPosValid &&
            pDirData->bTimeStamp <= SEMAPHORE_VALID_TIMESTAMP_MAX &&
            CheckTwoPosRcvWithin40sec(pDirData));
}

/**
 * @brief Check if it's able to be Mobile semaphore synchronization source
 * @param uMMSI MMSI
 */
bool CUserDirMgr::IsMobileSemaSyncSrcCandi(DWORD uMMSI)
{
    return IsMobileSemaSyncSrcCandi(FindDirDataPtrMS(uMMSI));
}

/**
 * @brief Get Mobile semaphore synchronization source
 */
DWORD CUserDirMgr::GetSyncSrcForMobileSemaphore()
{
    //----------------------------------------------------------------------------------------------------------------------
    // ITU-R-M 1371-5 3.1.1.4 Number of received stations
    // A station, which is unable to attain UTC direct or UTC indirect synchronization and is also unable
    // to receive transmissions from a base station, should synchronize to the station indicating the highest
    // number of other stations received during the last nine frames, provided that two reports have been
    // received from that station in the last 40s.
    // When a station is receiving several other stations, which indicate the same number of received stations,
    // synchronization should be based on the station with the lowest MMSI.
    //----------------------------------------------------------------------------------------------------------------------
    //-----------------------------------------------------------------------------------------------------
    // IEC-61993-2 7.3.3
    // Repeated messages and Class B messages shall not be used in the indirect synchronization process
    //-----------------------------------------------------------------------------------------------------

    if(cTimerSys::getInst()->GetTimeDiffSec(m_dwOtherStSemaCheckSec) < 10)
        return m_nOtherStSemaMMSI;

    m_dwOtherStSemaCheckSec = cTimerSys::getInst()->GetCurTimerSec();

    UINT16 uMaxRcvSt = 0;
    INT16 nDirIdxSema = AIS_DIR_DATA_NOT_FOUND;

    // Find a mobile station semaphore candidate
    for (int idx = 0 ; idx < AIS_MAX_DIR_MS_NUM ; idx++)
    {
        if(m_vAisDirDataMS[idx].dMMSI != AIS_AB_MMSI_NULL)
        {
            if (IsMobileSemaSyncSrcCandi(&(m_vAisDirDataMS[idx])))
            {
                // select the highest number of received stations
                if(m_vAisDirDataMS[idx].uNumRcvSt9min > uMaxRcvSt)
                {
                    uMaxRcvSt = m_vAisDirDataMS[idx].uNumRcvSt9min;
                    nDirIdxSema = idx;
                }
                else if(m_vAisDirDataMS[idx].uNumRcvSt9min == uMaxRcvSt)
                {
                    // if the number of received stations is the same, select the lowest MMSI
                    if(nDirIdxSema != AIS_DIR_DATA_NOT_FOUND && m_vAisDirDataMS[idx].dMMSI < m_vAisDirDataMS[nDirIdxSema].dMMSI)
                        nDirIdxSema = idx;
                }
            }
        }
    }

    if(nDirIdxSema != AIS_DIR_DATA_NOT_FOUND)
    {
        m_nOtherStSemaMMSI = m_vAisDirDataMS[nDirIdxSema].dMMSI;

        DEBUG_LOG("GetSyncSrcForMobileSemaphore] end, %09d, maxRcvSt:%d\r\n",
                m_vAisDirDataMS[nDirIdxSema].dMMSI, m_vAisDirDataMS[nDirIdxSema].uNumRcvSt9min);
    }
    else
    {
        m_nOtherStSemaMMSI = AIS_AB_MMSI_NULL;
    }

    return m_nOtherStSemaMMSI;
}

/**
 * @brief Count the number of received stations
 * @param nSaveIdx Save index
 * @param pDirList Directory data list
 * @param nNumSt Number of directory data
 */
void CUserDirMgr::CountNumberOfStations(int nSaveIdx, xDIRDATA *pDirList, int nNumSt)
{
    for(int idx = 0 ; idx < nNumSt ; idx++)
    {
        if(pDirList[idx].dMMSI != AIS_AB_MMSI_NULL)
        {
            pDirList[idx].puNumRcvStForMin[nSaveIdx] = pDirList[idx].uNumRcvStLast;

            int nSumStations = 0;
            for(int j = 0 ; j < NUM_RCV_ST_SAVE_FRAME ; j++)
                nSumStations += pDirList[idx].puNumRcvStForMin[j];
            pDirList[idx].uNumRcvSt9min = nSumStations;
        }
    }
}

/**
 * @brief Recalculate the number of received stations for own ship
 * @param nSaveIdx Save index
 */
void CUserDirMgr::CountNamberOfStationsOwnShip(int nSaveIdx)
{
    m_puOwnShipNumRcvSt9min[nSaveIdx] = m_nOwnShipNumRcvStLast;

    int nSumStations = 0;
    for(int j = 0 ; j < NUM_RCV_ST_SAVE_FRAME ; j++)
        nSumStations += m_puOwnShipNumRcvSt9min[j];
    m_nOwnShipNumRcvSt9min = nSumStations;

    DEBUG_LOG("CalcRcvSt-Own] [#%d], 9m:%d, last:%d, s:%d\r\n",
            nSaveIdx, m_nOwnShipNumRcvSt9min, m_nOwnShipNumRcvStLast, cTimerSys::getInst()->GetCurTimerSec());
}

/**
 * @brief Recalculate the number of received stations
 */
bool CUserDirMgr::UpdateNumberOfStations(void)
{
    static DWORD dwCheckSec = 0;
    static int nSaveIdx = 0;

    // This certainly should be called every 1 minutes
    if( OPSTATUS::bEnableCalcNumRcvSt && 
        cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 59 && 
        (2000 < OPSTATUS::nCurSlotID) && (OPSTATUS::nCurSlotID < 2250))
    {
        CountNumberOfStations(nSaveIdx, m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM);
        CountNumberOfStations(nSaveIdx, m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM);
        CountNamberOfStationsOwnShip(nSaveIdx);

        // For debugging
        //DebugOutDirBase();
        //DebugOutDirMobile();

        nSaveIdx++;
        if (nSaveIdx >= NUM_RCV_ST_SAVE_FRAME)
            nSaveIdx = 0;

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
        return true;
    }

    return false;
}

/**
 * @brief Run periodically
 */
void CUserDirMgr::RunPeriodically()
{
    static DWORD dwCheckSec = 0;

    // This certainly should be called every 1 minutes
    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 60)
    {
        // Check obsolete user directory data for base station
        if(CheckObsoleteUserDirData(m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM, &m_nNumValidBaseSt))
        {
            ;//DebugOutDirBase();
        }

        // Check obsolete user directory data for mobile station
        if(CheckObsoleteUserDirData(m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM, &m_nNumValidMobileSt))
        {
            ;//DebugOutDirMobile();
        }

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }

    // Only update the number of received stations
    UpdateNumberOfStations();
}

/**
 * @brief Debug output directory data
 * @param pTitle Title
 * @param pDirList Directory data list
 * @param nNumSt Number of directory data
 */
void CUserDirMgr::DebugOutDirList(char *pTitle, xDIRDATA *pDirList, int nNumSt)
{
    for(int i = 0 ; i < nNumSt ; i++)
    {
        if(pDirList[i].dMMSI != AIS_AB_MMSI_NULL)
        {
            DEBUG_LOG("#%d, %09d, sy:%d, canBeSrc:%d(%d), posVal:%d, stamp:%d, rxSt:%d(%d), lastRcvElap:%d,%d, posRcvElap:%d\r\n",
                i, pDirList[i].dMMSI, pDirList[i].bSyncState, pDirList[i].bCanBeSyncSrc, pDirList[i].nClassBType,
                pDirList[i].bPosValid, pDirList[i].bTimeStamp, pDirList[i].uNumRcvStLast, pDirList[i].uNumRcvSt9min,
                cTimerSys::getInst()->GetTimeDiffSec(pDirList[i].dwLastNonRepeatPosReportSec[IDX_POS_PREV]), cTimerSys::getInst()->GetTimeDiffSec(pDirList[i].dwLastNonRepeatPosReportSec[IDX_POS_LAST]),
                cTimerSys::getInst()->GetTimeDiffSec(pDirList[i].dwLastPosReportSec));
        }
    }
}

/**
 * @brief Debug output directory data for base station
 */
void CUserDirMgr::DebugOutDirBase()
{
    DebugOutDirList((char*)"DirList-BS] ", m_vAisDirDataBS, AIS_MAX_DIR_BS_NUM);
}

/**
 * @brief Debug output directory data for mobile station
 */
void CUserDirMgr::DebugOutDirMobile()
{
    DebugOutDirList((char*)"DirList-MS] ", m_vAisDirDataMS, AIS_MAX_DIR_MS_NUM);
}
