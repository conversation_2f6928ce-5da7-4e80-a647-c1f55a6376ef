/**
 * @file    Lri.cpp
 * @brief   Lri class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "RosMgr.h"
#include "Lri.h"

/******************************************************************************
*
* LRI - Long Range Interrogation
*
* $--LRI,x,a,xxxxxxxxx,xxxxxxxxx,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a*hh<CR><LF>
*        | | |         |         |       | |        | |       | |        |
*        1 2 3         4         5       6 7        8 9      10 11       12
*
* 1.     Sequence Number , 0 to 9
* 2.     Control Flag
* 3.     MMSI of "requester"
* 4.     MMSI of "destination"
* 5.6.   Latitude  - N/S (north-east co-ordinate)
* 7.8.   Longitude - E/W (north-east co-ordinate)
* 9.10.  Latitude  - N/S (south-west co-ordinate)
* 11.12. Longitude - E/W (south-west co-ordinate)
*
******************************************************************************/
// Define static member variables
int8_t CLri::m_nSequentialId = 0;

CLri::CLri() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CLri::Parse(const char *pszSentence)
{
    m_nSequentialId  = GetFieldInteger(pszSentence,1);
    //m_chControlFlag  = GetFieldChar(pszSentence, 2);
    //m_dwMMSIReq      = GetFieldInteger(pszSentence, 3);
    //m_dwMMSIDest     = GetFieldInteger(pszSentence, 4);
    //m_nLatNE         = GetFieldLat(pszSentence, 5);
    //m_nLonNE         = GetFieldLon(pszSentence, 7);
    //m_nLatSW         = GetFieldLat(pszSentence, 9);
    //m_nLonSW         = GetFieldLon(pszSentence, 11);

    return true;
}

/**
 * @brief Make the LRI sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CLri::MakeSentence(char *pszSentence)
{
    return strlen(pszSentence);
}




