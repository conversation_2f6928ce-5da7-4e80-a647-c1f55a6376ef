#ifndef __DSCMGR_H__
#define __DSCMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "RosMgr.h"
#include "DscModem.h"

//-----------------------------------------------------------------------------
// DSC Format Specifier
//-----------------------------------------------------------------------------
#define DSC_FRMT_VTS_GEOGRAPHY          103
#define DSC_FRMT_INDIVIDUAL             120
//#define DSC_FRMT_DISTRESS             112
//#define DSC_FRMT_ALL_SHIP             116
//#define DSC_FRMT_GEOGRAPHY            102
//#define DSC_FRMT_GROUP                114
//#define DSC_FRMT_AT_SAT               123
#define DSC_FRMT_NONE                   0
//-----------------------------------------------------------------------------
#define DSC_CATEGORY_VTS                103     // Category "information", a safety call related to VTS operation
#define DSC_CATEGORY_NONE               0
//-----------------------------------------------------------------------------
#define DSC_EOS_CHR_117                 117
#define DSC_EOS_CHR_122                 122
#define DSC_EOS_CHR_127                 127
//-----------------------------------------------------------------------------
#define DSC_MSGID_VTS_EXP               104     // Symbols for Message contents of VTS DSC calls
//-----------------------------------------------------------------------------
#define DSC_EXP_TX_POWER                1       // (followed by 1 symbol)
#define DSC_EXP_PRI_REGION_CH           9       // (followed by 3 symbols) - single-channel operation
#define DSC_EXP_SEC_REGION_CH           10      // (followed by 3 symbols) - Set Tx/Rx mode  (M.1371-3, p.58)
#define DSC_EXP_GUARD_REGION_CH         11      // (followed by 3 symbols) - Set only Rx Mode
#define DSC_EXP_NE_CORNER_REGION        12      // (followed by 6 symbols)
#define DSC_EXP_SW_CORNER_REGION        13      // (followed by 6 symbols)
//=============================================================================
#define DSC_CHAR_BIT_SIZE               10
#define DSC_PHASE_CHAR_SIZE             16
#define DSC_PHASE_BIT_SIZE              (DSC_PHASE_CHAR_SIZE * DSC_CHAR_BIT_SIZE)
//=============================================================================
#define DSC_DX_RX_SIZE                  256
//=============================================================================
#define DSC_RX_STATUS_PHASING           0
#define DSC_RX_STATUS_RUNNING           1
//=============================================================================
#define DSC_RX_EOS_FOUND_NONE           0
#define DSC_RX_EOS_FOUND_DX             1
#define DSC_RX_EOS_FOUND_RX             2
//=============================================================================
#define DSC_MAX_DSC_EXP_MSG             4
//=============================================================================
#define DSC_NEWS_NE                     0
#define DSC_NEWS_NW                     1
#define DSC_NEWS_SE                     2
#define DSC_NEWS_SW                     3
#define DSC_GROUP_BY_CRS                4       // Group call by course
//=============================================================================
#define DSC_CMD_VALID_MASK              0x80000000
//=============================================================================

//=============================================================================
#define DSC_MAX_BUFF_SIZE               4
//=============================================================================


class CDSCMgr
{
public:
	CDSCMgr(int nHwLocalRcvrID);
    ~CDSCMgr();

    static std::shared_ptr<CDSCMgr> getInst() {
        static std::shared_ptr<CDSCMgr> pInst = std::make_shared<CDSCMgr>(AIS_HW_RX_LOCAL_ID_RX3);
        return pInst;
    }

public:
    cDscModem  *m_pDscModem;

    INT8        m_nHwLocalRcvrID;
    int         m_uChNumRx;
    UINT8       m_uBandwidth;            // AIS_CH_BW_25_0_KHZ,...,AIS_CH_BW_12_5_KHZ

    xDscAllMsg *m_vRxBuffData;
    int         m_nRxBuffHead;
    int         m_nRxBuffTail;

public:
    void        Initialize();
    BOOL        SetRxChannelNumber(UINT16 uChNum);
    void        PutDscMsg(xDscAllMsg *pDscMsg);
    int         GetDscMsg(xDscAllMsg *pDscMsg);

    BOOL        GetRosChTrxModeFromDscMsg(xROSDATA *psRosData, xDscAllMsg *psDscMsg);
    BOOL        ProcessDscMSG_AreaCall(xDscAllMsg *psDscMsg);
    BOOL        ProcessDscMSG_Individual(xDscAllMsg *psDscMsg);
    void        ProcessDscMsg(xDscAllMsg *psDscMsg);
    void        ProcessRxMsg();
};

#endif//__DSCMGR_H__
