#ifndef __LAYERPHYSICAL_H__
#define __LAYERPHYSICAL_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"

class CLayerPhysical
{
public:
    enum _tagPrepTxStep
    {
        PREPTX_STEP_NONE = 0,
        PREPTX_STEP1,
        PREPTX_STEP2,
        PREPTX_STEP3,
        PREPTX_STEP4,
    };

public:
    CLayerPhysical();
    ~CLayerPhysical();

    static std::shared_ptr<CLayerPhysical> getInst() {
        static std::shared_ptr<CLayerPhysical> pInst = std::make_shared<CLayerPhysical>();
        return pInst;
    }

public:
    void    InitHW();
    BOOL    SetRxLocalFreq(int nHwLocalRcvrID, UINT uChNum);
    BOOL    SetTxPowerMode(UINT8 uTxPower);
    WORD    GetTxPowerLevel();
    void    RecalcRampUpPrepSet();

    void    InitToRampUp();
    void    RunToRampUp();

    void    SetTxRfOn(BOOL bOn);
    void    TurnTxOff(BOOL bUnconditionally);

    void    ReserveTxFreq(int nFrequency);
    BOOL    RunTransmitStep1();

    void    PrepToTransmitStep1(int nFrequency);
    void    PrepToTransmitStep2();
    void    PrepToTransmitStep3();

    void    SetVswrMonitoringByMKD(BOOL bEnable);
    BOOL    CheckVswrOnTx();
    BOOL    IsTxHwShutdownOccurred();
    BOOL    CheckReadyToTransmit();

    void    RunCriticalPhysical();
    void    RunPeriodicallyPhysical();

    inline  void    SetTxPowerRamp(int nLevel);

public:
    UINT8   m_uTxPowerMode;                // AIS_TX_POWER_HIGH(0) = high, AIS_TX_POWER_LOW(1) = low

    BOOL    m_bTxRunning;
    int     m_nPrepTxStep;

    int     m_nTxPwrLevelToSet;
    float   m_fRampUpStep;

    int     m_nRampUpCnt;
    float   m_fLevelSum;

    int     m_nCurPwrLevel;

    BOOL    m_bTxHwShutdownOccured;
    DWORD   m_dwTxHwPwrStartTick;

public:
    BOOL    m_bTxRfOn;
    DWORD   m_dwTxRfOnSec;

    BOOL    m_bVswrMonitoringByMKD;
    WORD    m_wFwdPowerAdValue;
    WORD    m_wRefPowerAdValue;

    BOOL    m_bEnableCheckVswr;
    DWORD   m_dwVswrCheckEnableTick;
    DWORD   m_dwVswrCheckRunTick;
    BOOL    m_bVswrErrorOccurred;

    BOOL    m_bEnableCheckRxMalFuncRX1;
    DWORD   m_dwRxLockStartTickSec1;

    BOOL    m_bEnableCheckRxMalFuncRX2;
    DWORD   m_dwRxLockStartTickSec2;

    int     m_nRegTxFreq;
    int     m_nSetModemTxFreq;
    DWORD   m_dwTxFreqTick;
};

#endif//__LAYERPHYSICAL_H__
