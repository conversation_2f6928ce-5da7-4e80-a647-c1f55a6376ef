/**
 * @file    LayerPhysical.h
 * @brief   LayerPhysical header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef __LAYERPHYSICAL_H__
#define __LAYERPHYSICAL_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"

class CLayerPhysical
{
public:
    enum _tagPrepTxStep
    {
        PREPTX_STEP_NONE = 0,
        PREPTX_STEP1,
        PREPTX_STEP2,
        PREPTX_STEP3,
        PREPTX_STEP4,
    };

public:
    CLayerPhysical();
    ~CLayerPhysical();

    static std::shared_ptr<CLayerPhysical> getInst() {
        static std::shared_ptr<CLayerPhysical> pInst = std::make_shared<CLayerPhysical>();
        return pInst;
    }

public:
    void    InitHW(void);

    UINT8   GetTxPowerMode(void)            { return m_uTxPowerMode; }
    bool    IsEnableCheckMalfuncRx1(void)   { return m_bEnableCheckRxMalFuncRX1; }
    bool    IsEnableCheckMalfuncRx2(void)   { return m_bEnableCheckRxMalFuncRX2; }
    bool    IsVswrErrorOccurred(void)       { return m_bVswrErrorOccurred; }
    WORD    GetFwdPowerAdValue(void)        { return m_wFwdPowerAdValue; }
    WORD    GetRefPowerAdValue(void)        { return m_wRefPowerAdValue; }

    bool    SetRxLocalFreq(int nHwLocalRcvrID, UINT uChNum);
    bool    SetTxPowerMode(UINT8 uTxPower);
    WORD    GetTxPowerLevel(void);

    void    InitToRampUp(void);
    void    RunToRampUp(void);
    void    RecalcRampUpPrepSet(void);

    void    SetTxRfOn(bool bOn);
    void    TurnTxOff(bool bUnconditionally);

    void    ReserveTxFreq(int nFrequency);
    void    RunTransmitStep1(void);

    void    PrepToTransmitStep1(int nFrequency);
    void    PrepToTransmitStep2(void);
    void    PrepToTransmitStep3(void);

    bool    CheckVswrOnTx(void);
    bool    IsTxHwShutdownOccurred(void);
    bool    CheckReadyToTransmit(void);

    void    RunCriticalPhysical(void);
    void    RunPeriodicallyPhysical(void);

    inline  void    SetTxPowerRamp(int nLevel);

private:
    UINT8   m_uTxPowerMode;

    bool    m_bTxRfOn;
    DWORD   m_dwTxRfOnSec;

    bool    m_bTxRunning;
    int     m_nPrepTxStep;

    int     m_nTxPwrLevelToSet;
    float   m_fRampUpStep;

    int     m_nRampUpCnt;
    float   m_fLevelSum;

    int     m_nCurPwrLevel;

    bool    m_bTxHwShutdownOccured;
    DWORD   m_dwTxHwPwrStartTick;

    WORD    m_wFwdPowerAdValue;
    WORD    m_wRefPowerAdValue;

    bool    m_bEnableCheckVswr;
    DWORD   m_dwVswrCheckEnableTick;
    DWORD   m_dwVswrCheckRunTick;
    bool    m_bVswrErrorOccurred;

    bool    m_bEnableCheckRxMalFuncRX1;
    DWORD   m_dwRxLockStartTickSec1;

    bool    m_bEnableCheckRxMalFuncRX2;
    DWORD   m_dwRxLockStartTickSec2;

    int     m_nRegTxFreq;
    int     m_nSetModemTxFreq;
    DWORD   m_dwTxFreqTick;
};

#endif//__LAYERPHYSICAL_H__
