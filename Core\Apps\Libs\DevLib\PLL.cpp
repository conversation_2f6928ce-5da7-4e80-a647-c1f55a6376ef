/**
 * @file    Timer.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "SysConst.h"
#include "SysLib.h"
#include "GPIOExt.h"
#include "SPI.h"
#include "PLL.h"

//=============================================================================
cPll::cPll(void)
{
    m_pSPI = new cSpiSYS(SPI1);
}
cPll::~cPll(void)
{
    if (m_pSPI)
    {
        delete m_pSPI;
    }
}
//=============================================================================
CPllMgr::CPllMgr(int nPllIdx, GPIO_TypeDef *pChipGPIO, DWORD dChipSelect, int nRxOutFreq, int nTxOutFreq, int nChOffFreq) 
: cPll()
{
    m_nPllIdx = nPllIdx;

    m_pChipGPIO = pChipGPIO;
    m_dChipSelP = dChipSelect;
    SysSetGPIOxBitData(m_pChipGPIO, m_dChipSelP, 1);

    m_nRFxOutFreq[PLL_RX] = nRxOutFreq;
    m_nRFxOutFreq[PLL_TX] = nTxOutFreq;
    m_nRFxOffFreq = nChOffFreq;

    m_nCurrSetRFFreq = 0;

    m_bRxEnable = FALSE;
    m_bTxEnable = FALSE;

    m_nRxPower = LMX2571_DEFT_RX_PWR;
    m_nTxPower = LMX2571_DEFT_TX_PWR;

    InitLMX2571();
    SetRxFrequency(nRxOutFreq);
}

CPllMgr::~CPllMgr(void)
{
}

/**
 * @brief Initialize LMX2571
 */
void CPllMgr::InitLMX2571(void)
{
    SetResetPLL();
    SetLockDetect();
    SetXtalEnable();
}

/**
 * @brief Set PLL reset
 */
void CPllMgr::SetResetPLL(void)
{
    tREG0 REG0 = {0};
    REG0.RESET = 1;
    WriteRegData(REG_R00, &REG0.Data, 1);
}

/**
 * @brief Set Xtal enable
 */
void CPllMgr::SetXtalEnable(void)
{
    tREG34 REG34 = {0};
    REG34.XTAL_EN = 1;
    REG34.XTAL_PWRCTRL = 0;
    WriteRegData(REG_R34, &REG34.Data, 1);
}

/**
 * @brief Set lock detect
 */
void CPllMgr::SetLockDetect(void)
{
    tREG39 REG39 = {0};
    REG39.LD_EN = 1;
    REG39.RSV_1 = 1;
    REG39.SDO_LD_SEL = 1;
    REG39.RSV_2 = 0x11;
    WriteRegData(REG_R39, &REG39.Data, 1);
}

/**
 * @brief Write register data
 * @param nAddr Register address
 * @param pTxData Pointer to transmit data
 * @param nLen Number of data to transmit
 */
void CPllMgr::WriteRegData(uint8_t nAddr, uint16_t *pTxData, uint16_t nLen)
{
    SysSetGPIOxBitData(m_pChipGPIO, m_dChipSelP, 0);

    m_pSPI->WriteSpiData(nAddr, pTxData, LMX2571_WORD_SIZE, nLen);

    SysSetGPIOxBitData(m_pChipGPIO, m_dChipSelP, 1);
}

/**
 * @brief Get RF output frequency
 * @return RF output frequency
 */
int CPllMgr::GetRFOutFrequency()
{
    return m_nCurrSetRFFreq;
}

/**
 * @brief Get Rx channel frequency
 * @return Rx channel frequency
 */
int CPllMgr::GetRxFrequency()
{
    return m_nRFxOutFreq[PLL_RX];
}

/**
 * @brief Get Tx channel frequency
 * @return Tx channel frequency
 */
int CPllMgr::GetTxFrequency()
{
    return m_nRFxOutFreq[PLL_TX];
}

/**
 * @brief Set channel frequency
 * @param nChNo Channel number
 * @param nFrequency Frequency to set
 */
void CPllMgr::SetChFrequency(int nChNo, int nFrequency)
{
    int   OUT_FREQ = (nFrequency + m_nRFxOffFreq) / 1000;
    DWORD OSC_IN_FREQ;
    const int PRESCALE = 2;
    int   idx = 0;

    tREG6 REG6 = {0};
    REG6.PFD_DELAY_F1 = 4;
    REG6.MULT_F1 = 1;

    if      (m_nPllIdx == PLL_NO_0) OSC_IN_FREQ = (PLL_OSC1_IN_FREQ / 1000);
    else if (m_nPllIdx == PLL_NO_1) OSC_IN_FREQ = (PLL_OSC2_IN_FREQ / 1000);
    else                            OSC_IN_FREQ = (PLL_OSC3_IN_FREQ / 1000);

    int IntDivFVCO = round(PLL_INT_VCO_DEFT / OUT_FREQ);
    for (idx = 1; idx < LMX2571_MAX_VCO_DIV; idx++)
    {
        if (xVcoDiv[idx].IntVcoDiv > IntDivFVCO)
        {
            int CurDiff = abs(IntDivFVCO - xVcoDiv[idx].IntVcoDiv);
            int PreDiff = abs(IntDivFVCO - xVcoDiv[idx-1].IntVcoDiv);

            if (CurDiff < PreDiff)
            {
                IntDivFVCO = xVcoDiv[idx].IntVcoDiv;
                REG6.CHDIV1_F1 = xVcoDiv[idx].SEG1;
                REG6.CHDIV2_F1 = xVcoDiv[idx].SEG2;
            }
            else
            {
                IntDivFVCO = xVcoDiv[idx-1].IntVcoDiv;
                REG6.CHDIV1_F1 = xVcoDiv[idx-1].SEG1;
                REG6.CHDIV2_F1 = xVcoDiv[idx-1].SEG2;
            }
            break;
        }
    }

    if (idx == LMX2571_MAX_VCO_DIV)
    {
        // Pll setting error
        return;
    }

    UINT32  TargetFVCO = OUT_FREQ * IntDivFVCO;
    FLOAT   N_DIV = ((float)TargetFVCO / (float)OSC_IN_FREQ) / PRESCALE;      // Calculate N-divider

    UINT    N_DIV_INT = (UINT)N_DIV;                            // Integer part of N-divider
    FLOAT   N_DIV_FRAC = N_DIV - N_DIV_INT;                     // Fractional part of N-divider
    UINT    NUME = (uint32_t)(N_DIV_FRAC * LMX2571_MAX_DEN);    // Calculate numerator


    tREG8 REG8 = {0};
    REG8.OUTBUF_TX_PWR_F1 = m_nTxPower;   // TX buffer output power

    tREG7 REG7 = {0};
    REG7.OUTBUF_RX_PWR_F1 = m_nRxPower;   // RX buffer output power
    REG7.OUTBUF_TX_EN_F1 = m_bTxEnable;   // TX buffer enable
    REG7.OUTBUF_RX_EN_F1 = m_bRxEnable;   // RX buffer enable
    REG7.LF_R4_F1 = 0x4;                  // Set LF_R4_F1 field to 4

    tREG4 REG4 = {0};
    REG4.PLL_N_PRE_F1 = 0;            // Set Prescaler
    REG4.PLL_N_F1 = N_DIV_INT;        // Set integer part of N-divider
    REG4.FRAC_ORDER_F1 = 3;           // 0 = Integer mode, 3 = Sigma-Delta Modulator
    //REG4.FRAC_ORDER_F1 = 0;         // 0 = Integer mode, 3 = Sigma-Delta Modulator

    tREG3 REG3 = {0};
    REG3.PLL_DEN_F1 = LMX2571_MAX_DEN & 0xFFFF;

    tREG2 REG2 = {0};
    REG2.PLL_NUM_F1 = NUME & 0xFFFF;

    tREG1 REG1 = {0};
    REG1.PLL_DEN_F1 = (LMX2571_MAX_DEN >> 16) & 0xFF;
    REG1.PLL_NUM_F1 = (NUME  >> 16)& 0xFF;

    tREG0 REG0 = {0};
    REG0.RSV_1 = 1;
    REG0.FCAL_EN = 1;

    WriteRegData(REG_R08, &REG8.Data, 1);
    WriteRegData(REG_R07, &REG7.Data, 1);
    WriteRegData(REG_R06, &REG6.Data, 1);
    WriteRegData(REG_R04, &REG4.Data, 1);
    WriteRegData(REG_R03, &REG3.Data, 1);
    WriteRegData(REG_R02, &REG2.Data, 1);
    WriteRegData(REG_R01, &REG1.Data, 1);
    WriteRegData(REG_R00, &REG0.Data, 1);

    m_nRFxOutFreq[nChNo] = nFrequency;
    m_nCurrSetRFFreq = nFrequency;
}

/**
 * @brief Set channel 1 frequency
 * @param nFrequency Frequency to set
 */
void CPllMgr::SetRxFrequency(int nFrequency)
{
    m_bRxEnable = TRUE;
    m_bTxEnable = FALSE;
    SetChFrequency(PLL_RX, nFrequency);
}

/**
 * @brief Set channel 2 frequency
 * @param nFrequency Frequency to set
 */
void CPllMgr::SetTxFrequency(int nFrequency)
{
    m_bRxEnable = FALSE;
    m_bTxEnable = TRUE;
    SetChFrequency(PLL_TX, nFrequency);
}

/**
 * @brief Set Rx channel output power
 * @param nPower Power to set
 */
void CPllMgr::SetRxOutPower(int nPower)
{
    if (m_nRxPower != nPower)
    {
        m_nRxPower = nPower;

        tREG7 REG7 = {0};
        REG7.OUTBUF_RX_PWR_F1 = m_nRxPower;   // RX buffer output power
        REG7.OUTBUF_TX_EN_F1 = m_bTxEnable;   // TX buffer enable
        REG7.OUTBUF_RX_EN_F1 = m_bRxEnable;   // RX buffer enable
        REG7.LF_R4_F1 = 0x4;                  // Set LF_R4_F1 field to 4

        WriteRegData(REG_R07, &REG7.Data, 1);
    }
}

/**
 * @brief Set Tx channel output power
 * @param nPower Power to set
 */
void CPllMgr::SetTxOutPower(int nPower)
{
    if (m_nTxPower != nPower)
    {
        m_nTxPower = nPower;

        tREG8 REG8 = {0};
        REG8.OUTBUF_TX_PWR_F1 = m_nTxPower;   // TX buffer output power

        WriteRegData(REG_R08, &REG8.Data, 1);
    }
}

/**
 * @brief Set Rx channel output enable
 * @param nEnable TRUE to enable, FALSE to disable
 */
void  CPllMgr::SetRxOutEnable(BOOL nEnable)
{
    if (m_bRxEnable != nEnable)
    {
        m_bRxEnable = nEnable;

        tREG7 REG7 = {0};
        REG7.OUTBUF_RX_PWR_F1 = m_nRxPower;   // RX buffer output power
        REG7.OUTBUF_TX_EN_F1 = m_bTxEnable;   // TX buffer enable
        REG7.OUTBUF_RX_EN_F1 = m_bRxEnable;   // RX buffer enable
        REG7.LF_R4_F1 = 0x4;                  // Set LF_R4_F1 field to 4

        WriteRegData(REG_R07, &REG7.Data, 1);
    }
}

/**
 * @brief Set Tx channel output enable
 * @param nEnable TRUE to enable, FALSE to disable
 */
void  CPllMgr::SetTxOutEnable(BOOL nEnable)
{
    if (m_bTxEnable != nEnable)
    {
        m_bTxEnable = nEnable;

        tREG7 REG7 = {0};
        REG7.OUTBUF_RX_PWR_F1 = m_nRxPower;   // RX buffer output power
        REG7.OUTBUF_TX_EN_F1 = m_bTxEnable;   // TX buffer enable
        REG7.OUTBUF_RX_EN_F1 = m_bRxEnable;   // RX buffer enable
        REG7.LF_R4_F1 = 0x4;                  // Set LF_R4_F1 field to 4

        WriteRegData(REG_R07, &REG7.Data, 1);
    }
}
