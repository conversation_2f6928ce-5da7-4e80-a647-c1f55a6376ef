#include <stdlib.h>
#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "AisLib.h"
#include "Timer.h"
#include "Ship.h"
#include "LayerPhysical.h"
#include "LayerNetwork.h"
#include "LayerTransport.h"
#include "SetupMgr.h"
#include "DSCMgr.h"
#include "SensorMgr.h"
#include "GPIOExt.h"
#include "AisModem.h"
#include "PI.h"
#include "BuiltInTestMgr.h"
#include "MKD.h"
#include "GmskLib.h"
#include "SysOpStatus.h"
#include "TestModeMgr.h"

#define GetFreqHzFromCh(nCh)    (((double)(nCh) * 1000) / 80 * 1000)
#define GetChFromFreqHz(nFreq)  (((double)(nFreq) / 1000) * 80 / 1000)


CTestModeMgr::CTestModeMgr()
{
    m_pTxParam              = (BYTE*)SysAllocMemory(sizeof(BYTE) * 2);

    m_nTestModeRunning      = PITESTMODE_NONE;
    m_pTestChTx             = CLayerNetwork::getInst()->GetChPrimary();
    m_pCommPI               = NULL;

    m_bRxTestSignalRunning  = FALSE;
    m_bTxTestSignalRunning  = FALSE;
    m_uTxTestSignalType     = TM_TX_DATA_NON;

    m_nTxCntRandomPRBS      = 0;
    m_wTxSlotRandomPRBS     = SLOTID_NONE;

    m_nTxCntFixedPRBS       = 0;
    m_nTxSentCntFixedPRBS   = 0;
    m_wTxSlotFixedPRBS      = SLOTID_NONE;

    m_wTxSlotMsg1           = SLOTID_NONE;

    m_nDscRcvTestCnt        = 0;

    m_wErrorCodeBITE        = BITE_NO_ERROR;

    m_bRunningTxLoopbackTest= FALSE;
    m_wLoopbackTxSlot       = SLOTID_NONE;
    m_nLoopbackTxCnt        = 0;
    m_dwLoopbackTestCheckSec= 0;
    m_nTxLoopbackRcvCntCH1  = 0;
    m_nTxLoopbackRcvCntCH2  = 0;
}

CTestModeMgr::~CTestModeMgr()
{

}

BOOL CTestModeMgr::IsTestModeRunning()
{
    return (m_nTestModeRunning != PITESTMODE_NONE);
}

BOOL CTestModeMgr::IsTransmitTestRunning()
{
    return (m_nTestModeRunning == PITESTMODE_TX);
}

BOOL CTestModeMgr::IsReceiveTestRunning()
{
    return (m_nTestModeRunning == PITESTMODE_RX);
}

BOOL CTestModeMgr::IsBuiltInTestRunning()
{
    return (m_nTestModeRunning == PITESTMODE_BITE);
}

BOOL CTestModeMgr::IsRunTxLoopbackTest()
{
    return m_bRunningTxLoopbackTest;
}

void CTestModeMgr::SendOutTestModeStr(char *pstrData)
{
    if(m_pCommPI)
    {
        m_pCommPI->SendOutStr(pstrData);
    }
}

void CTestModeMgr::SendOutNak(int nCmd)
{
    if(m_pCommPI)
    {
        char pstrResp[64];
        sprintf((char*)pstrResp, "#AIINL,RFT,NAK,%0X,%04X,%04X", nCmd);
        CSentence::AddSentenceTail(pstrResp);
        m_pCommPI->SendOutStr(pstrResp);
    }
}

void CTestModeMgr::SetTestMode(TagTestMode nTestMode, CPI *pPortPI)
{
    if(m_nTestModeRunning != nTestMode || m_pCommPI != pPortPI)
    {
        m_nTestModeRunning = nTestMode;
        if(IsTestModeRunning())
        {
            m_pCommPI = pPortPI;
            m_nOrgMMSI = cShip::getOwnShipInst()->xStaticData.dMMSI;
            cShip::getOwnShipInst()->xStaticData.dMMSI = MMSI_TESTMODE;

            CLayerNetwork::getInst()->SetOpPhase(OPPHASE_DIAGNOSTIC);

            DEBUG_LOG("==============================================================\r\n");
            DEBUG_LOG("       %s Test mode Start! mode: %d, MMSI: %09d\r\n",
                    STR_MODEL_ID, m_nTestModeRunning, cShip::getOwnShipInst()->xStaticData.dMMSI);
            DEBUG_LOG("==============================================================\r\n");

            CLayerTransport::getInst()->InitAllChannels();

            // Set Tx Channel to AIS1 when entering Diag. test mode
            SetAisTestModeTxCh(CLayerNetwork::getInst()->GetChPrimary());

            if(m_nTestModeRunning == PITESTMODE_RX)
            {
                CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(FALSE);
                CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(FALSE);

                TMGetRxParam(AIS_CHANNEL_AIS1);
                TMGetRxParam(AIS_CHANNEL_AIS2);
                TMGetRxParam(AIS_CHANNEL_DSC);
            }
            else if(m_nTestModeRunning == PITESTMODE_TX)
            {
                CLayerNetwork::getInst()->EnableRx(FALSE);
                TMGetTxParam();
                TMGetTxPower();
                TMGetFreqOffset();
                TMGetDcOffset();
                TMGetVswrLimit();
            }

            SetLedAllOn(FALSE);
            TMTransmitterTestStop();
            
            SysDelayMicroSec(300);
        }
        else
        {
            TMTransmitterTestStop();

            DEBUG_LOG("=============================================================\r\n");
            DEBUG_LOG("                     %s Test mode End!\r\n", STR_MODEL_ID);
            DEBUG_LOG("=============================================================\r\n");

            cShip::getOwnShipInst()->xStaticData.dMMSI = m_nOrgMMSI;

            SetAisTestModeTxCh(NULL);
            SetLedAllOn(FALSE);

            CLayerTransport::getInst()->InitAllChannels();

            CLayerNetwork::getInst()->EnableRx(TRUE);
            CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(TRUE);
            CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(TRUE);

            CLayerNetwork::getInst()->SetOpPhase(OPPHASE_MONITOR_VDL);
            m_pCommPI = NULL;
        }

        cAisModem::getInst()->SetInfinteTxMode(FALSE);
        cAisModem::getInst()->ResetAisMsgTx();
    }
}

void CTestModeMgr::SetAisTestModeTxCh(CChannelMgr *pCh)
{
    m_pTestChTx = pCh;
}

void CTestModeMgr::TMGetTxParam()
{
    WORD    wTxCh;
    char    pstrResp[64];
    UINT    uTxFreq, uTxPower;

    uTxFreq = CAisLib::GetAisFreqByChannelNo(m_pTestChTx->m_uChNumTx);
    wTxCh    = GetChFromFreqHz(uTxFreq);
    uTxPower= CLayerPhysical::getInst()->GetTxPowerMode();

    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X,%04X", TM_GET_TX_PARAM, wTxCh, uTxPower);
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

BOOL CTestModeMgr::TMSetTxParam(WORD wTxChNum, WORD wTxPower)
{
    UINT    uTxFreq;
    WORD    wTxCH;

    if(!CAisLib::IsValidTxPower(wTxPower))
    {
        SendOutNak(TM_SET_TX_PARAM);
        return FALSE;
    }

    uTxFreq = GetFreqHzFromCh(wTxChNum);
    wTxCH = CAisLib::GetAisChannelNoByFreq(uTxFreq);
    m_pTestChTx->SetTxChannelNumber(wTxCH);

    // Set physical HW directly without going through ROS layer.
    CLayerPhysical::getInst()->SetTxPowerMode(wTxPower);

    char pstrResp[64];
    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X,%04X", TM_GET_TX_PARAM, wTxChNum, wTxPower);
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);

    // If the frequency or power changes, the power level value is sent again.
    TMGetTxPower();
    return TRUE;
}

void CTestModeMgr::TMGetTxPower()
{
    char  pstrResp[64];
    UINT  uTxFreq;
    BYTE  tx_power;

    uTxFreq  = CAisLib::GetAisFreqByChannelNo(m_pTestChTx->m_uChNumTx);
    tx_power= CLayerPhysical::getInst()->GetTxPowerMode();
    int nPowerLevel = 0;

    if(uTxFreq < VCO_BASIC_FREQ)
    {
        if(tx_power == AIS_TX_POWER_HIGH)
        {
            // If the set frequency is less than 160MHz, apply pw_level1.
            nPowerLevel = CSetupMgr::getInst()->GetTxPowerLevelCh1High();
        }
        else
        {
            nPowerLevel = CSetupMgr::getInst()->GetTxPowerLevelCh1Low();
        }
    }
    else
    {
        if(tx_power == AIS_TX_POWER_HIGH)
        {
            // If the set frequency is 160MHz or higher, apply pw_level2.
            nPowerLevel = CSetupMgr::getInst()->GetTxPowerLevelCh2High();
        }
        else
        {
            nPowerLevel = CSetupMgr::getInst()->GetTxPowerLevelCh2Low();
        }
    }

    nPowerLevel >>= POWER_LEVEL_SCALE_SHIFT_BITS;

    sprintf(pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_TX_POWER, nPowerLevel);
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMSetTxPower(WORD wSetPowerLevel)
{
    char    pstrResp[64];
    UINT    uTxFreq;
    BYTE    tx_power;

    if(wSetPowerLevel > 0xFF)
    {
        DEBUG_LOG("TMSetTxPower] error! %d is too big, have to be less than 256\r\n", wSetPowerLevel);
        SendOutNak(TM_SET_TX_POWER);
        return;
    }

    WORD wPowerLevel = wSetPowerLevel << POWER_LEVEL_SCALE_SHIFT_BITS;
    uTxFreq  = CAisLib::GetAisFreqByChannelNo(m_pTestChTx->m_uChNumTx);
    tx_power= CLayerPhysical::getInst()->GetTxPowerMode();

    if(uTxFreq < VCO_BASIC_FREQ)
    {
        // CH-1 power level
        if(tx_power == AIS_TX_POWER_HIGH)
            CSetupMgr::getInst()->SetTxPowerLevelCh1High(wPowerLevel);
        else
            CSetupMgr::getInst()->SetTxPowerLevelCh1Low(wPowerLevel);
    }
    else
    {
        // CH-2 power level
        if(tx_power == AIS_TX_POWER_HIGH)
            CSetupMgr::getInst()->SetTxPowerLevelCh2High(wPowerLevel);
        else
            CSetupMgr::getInst()->SetTxPowerLevelCh2Low(wPowerLevel);
    }

    DEBUG_LOG("TMSetTxPower] pwLevel:%d, freq:%d, pwrHigh:%d\r\n", wPowerLevel, uTxFreq, tx_power);

    ///// jyyang - need modify
    CSetupMgr::getInst()->VerifyHwConfigData();
    TMGetTxPower();

    CSetupMgr::getInst()->SaveHwConfigData();

    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_TX_POWER, wSetPowerLevel);
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMGetFreqOffset()
{
    char pstrResp[64];
    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_TX_FREQ_OFFSET, CSetupMgr::getInst()->GetTcxoLevel());
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMSetFreqOffset(WORD wOffset)
{
    char pstrResp[64];

    CSetupMgr::getInst()->SetTcxoLevel(wOffset);

    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_TX_FREQ_OFFSET, wOffset);
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMGetDcOffset()
{
    char pstrResp[64];
    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X,%04X", 
        TM_GET_TX_DC_OFFSET, 
        CSetupMgr::getInst()->GetDcOffset(), 
        CSetupMgr::getInst()->GetDcShift());
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMSetDcOffset(WORD wOffset, WORD wShift)
{
    char pstrResp[64];

    CSetupMgr::getInst()->SetDcOffset(wOffset);
    CSetupMgr::getInst()->SetDcShift(wShift);

    cAisModem::getInst()->InitModemSetup(wOffset, wShift);

    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X,%04X", 
        TM_GET_TX_DC_OFFSET,
        CSetupMgr::getInst()->GetDcOffset(), 
        CSetupMgr::getInst()->GetDcShift());
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMGetVswrLimit()
{
    char pstrResp[64];
    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", 
        TM_GET_VSWRLIMIT, 
        CSetupMgr::getInst()->GetVswrLimit());
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::TMSetVswrLimit(WORD wVswr)
{
    char pstrResp[64];

    CSetupMgr::getInst()->SetVswrLimit(wVswr);

    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X,%04X", 
        TM_GET_VSWRLIMIT,
        CSetupMgr::getInst()->GetVswrLimit());
    CSentence::AddSentenceTail(pstrResp);
    SendOutTestModeStr(pstrResp);
}

BOOL CTestModeMgr::TMSetTranmitterTest(WORD wSignalPattern)
{
    if(TM_TX_DATA_0101 <= wSignalPattern && wSignalPattern <= TM_TX_DATA_FIXED_MSG1)
    {
        if(m_bTxTestSignalRunning)
        {
            DEBUG_LOG("TxTestSet] ignore, already running test, signal: %d\r\n", m_uTxTestSignalType);
        }
        else
        {
            TMTransmitterTestStart(wSignalPattern);
            return TRUE;
        }
    }

    SendOutNak(TM_MODE_START);
    return FALSE;
}

void CTestModeMgr::TMTransmitterTestProc()
{
    if(m_bTxTestSignalRunning)
    {
         switch(m_uTxTestSignalType)
        {
        case TM_TX_DATA_RANDOM_PRBS:
            TMTransmitRandomPRBSMsgProc();
            break;
        case TM_TX_DATA_FIXED_PRBS:
            TMTransmitFixedPRBSMsgProc();
            break;
        case TM_TX_DATA_FIXED_MSG1:
            TMTransmitFixedMsg1Proc();
            break;
        case TM_TX_LOOPBACK_TEST:
            RunTxLoopbackTest();
        }
    }
}

void CTestModeMgr::TMTransmitterTestStart(BYTE uTestSignalType)
{
    INT16 nMsgID = AIS_MSG_NO_ID_UNDEFINED;

    m_bTxTestSignalRunning = TRUE;
    m_uTxTestSignalType= uTestSignalType;

    switch(m_uTxTestSignalType)
    {
    case TM_TX_DATA_0101:
        nMsgID = AIS_TESTMODE_MSGID_0101;
        break;

    case TM_TX_DATA_0000_1111:
        nMsgID = AIS_TESTMODE_MSGID_00001111;
        break;

    case TM_TX_DATA_RANDOM_PRBS:
        TMTransmitRandomPRBSMsgRun(TRUE);
        break;

    case TM_TX_DATA_FIXED_PRBS:
        TMTransmitFixedPRBSMsgRun(TRUE);
        break;

    case TM_TX_DATA_UNMODE_CARRIER:
        nMsgID = AIS_TESTMODE_MSGID_UNMOD_CARRIER;
        break;

    case TM_TX_DATA_FIXED_MSG1:
        m_wTxSlotMsg1 = SLOTID_NONE;
        break;
    }

    if(nMsgID != AIS_MSG_NO_ID_UNDEFINED)
    {
        WORD wTestTxSlot = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, TESTMODE_START_SPACE);
        m_pTestChTx->SetFrameMapOneMsgColumn(TRUE, wTestTxSlot, INFINITE_TESTDATA_NUMSLOT, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, 0,
                                                            POS_REPORT_UNSCHEDULED, TDMA_RATDMA, nMsgID, 0, FALSE, FALSE, 0, FALSE);
    }
}

void CTestModeMgr::TMTransmitterTestStop(void)
{
    CLayerPhysical::getInst()->TurnTxOff(TRUE);
    cAisModem::getInst()->SetInfinteTxMode(FALSE);
    cAisModem::getInst()->ResetAisMsgTx();

    m_bTxTestSignalRunning = FALSE;
    m_uTxTestSignalType= TM_TX_DATA_NON;

    TMTransmitRandomPRBSMsgRun(FALSE);
    TMTransmitFixedPRBSMsgRun(FALSE);

    if(m_pTestChTx)
        m_pTestChTx->ClearFrameMap();

    SetGpioLed_Tx(FALSE);
}

void CTestModeMgr::TMTransmitRandomPRBSMsgRun(BOOL bRun)
{
    m_bTxTestSignalRunning = bRun;
    m_nTxCntRandomPRBS = 0;
    m_wTxSlotRandomPRBS= SLOTID_NONE;
}

void CTestModeMgr::TMTransmitRandomPRBSMsgProc()
{
    if(!m_bTxTestSignalRunning)
        return;

    if(m_uTxTestSignalType == TM_TX_DATA_RANDOM_PRBS)
    {
        const INT16 RANDOMMSG_TESTMODE_SLOT_SPACE = TESTMODE_SLOT_SPACE;
        if(m_wTxSlotRandomPRBS != SLOTID_NONE && CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_wTxSlotRandomPRBS) <= 1)
        {
            return;
        }

        // IEC61993-2:2018 15.2.7.2 Method of measurement
        // The EUT shall be set to the default power
        // (12,5 W) and be set to transmit 200 messages of Message 1 with 2 s interval. The message
        // measuring test set shall be monitored and the PER be observed.
        if(m_wTxSlotRandomPRBS == SLOTID_NONE)
            m_wTxSlotRandomPRBS = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, TESTMODE_START_SPACE);
        else
            m_wTxSlotRandomPRBS = CAisLib::FrameMapSlotIdAdd(m_wTxSlotRandomPRBS, RANDOMMSG_TESTMODE_SLOT_SPACE);

        m_pTestChTx->SetFrameMapOneMsgColumn(TRUE, m_wTxSlotRandomPRBS, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, 0,
                                                            POS_REPORT_UNSCHEDULED, TDMA_RATDMA, AIS_TESTMODE_MSGID_PRBS_ONE, 0, FALSE, FALSE, 0, FALSE);

        if(++m_nTxCntRandomPRBS >= 800)
        {
            DEBUG_LOG("End of PRBS Random Msg, #%d, %d\r\n", m_nTxCntRandomPRBS, SysGetDiffTimeMili(0));
            TMTransmitRandomPRBSMsgRun(FALSE);
        }
    }
}

void CTestModeMgr::TMTransmitFixedPRBSMsgRun(BOOL bRun)
{
    m_bTxTestSignalRunning = bRun;
    m_nTxCntFixedPRBS = 0;
    m_nTxSentCntFixedPRBS = 0;
    m_wTxSlotFixedPRBS= SLOTID_NONE;
}

void CTestModeMgr::TMTransmitFixedPRBSMsgProc()
{
    if(!m_bTxTestSignalRunning)
        return;

    if(m_uTxTestSignalType == TM_TX_DATA_FIXED_PRBS)
    {
        const INT16 FIXEDMSG_TESTMODE_SLOT_SPACE = TESTMODE_SLOT_SPACE;
        if(m_wTxSlotFixedPRBS != SLOTID_NONE && CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_wTxSlotFixedPRBS) <= 1)
        {
            return;
        }

        // IEC61993-2:2018 15.2.7.2 Method of measurement
        // The EUT shall be set to the default power
        // (12,5 W) and be set to transmit 200 messages of Message 1 with 2 s interval. The message
        // measuring test set shall be monitored and the PER be observed.
        if(m_wTxSlotFixedPRBS == SLOTID_NONE)
            m_wTxSlotFixedPRBS = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, TESTMODE_START_SPACE);
        else
            m_wTxSlotFixedPRBS = CAisLib::FrameMapSlotIdAdd(m_wTxSlotFixedPRBS, FIXEDMSG_TESTMODE_SLOT_SPACE);

        m_pTestChTx->SetFrameMapOneMsgColumn(TRUE, m_wTxSlotFixedPRBS, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, 0,
                                                            POS_REPORT_UNSCHEDULED, TDMA_RATDMA, AIS_TESTMODE_MSGID_PRBS_CLUSTER, 0, FALSE, FALSE, 0, FALSE);

        // IEC61993-2:2018 10.5 Standard test signal number 5 (PRBS)
        // This test signal consists of 200 packets grouped into clusters of 4 as described in Figure 3.
        // Each cluster consists of 2 consecutive transmissions of the packets described in Table 31.
        if(++m_nTxCntFixedPRBS >= 800)    // 1000 -> 800
        {
            DEBUG_LOG("End of PRBS Fixed Msg, %d\r\n", m_nTxCntFixedPRBS);
            TMTransmitFixedPRBSMsgRun(FALSE);
        }
    }
}

void CTestModeMgr::TMTransmitFixedMsg1Proc()
{
    if(m_uTxTestSignalType == TM_TX_DATA_FIXED_MSG1)
    {
        const INT16 TX_NUMSLOT = 1;
        const INT16 TX_MSGID = AIS_MSG_NO_ID_01;

        INT16 nSlotTerm = 75;
        if(m_wTxSlotMsg1 != SLOTID_NONE && CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_wTxSlotMsg1) >= (AIS_GMSK_TX_BUFF_SIZE-1))
            return;

        if(m_wTxSlotMsg1 == SLOTID_NONE)
        {
            int nCurFrSlot = CAisLib::GetFrameMapSlotID(cAisModem::getInst()->GetCurrentFrameNo(), cAisModem::getInst()->GetSlotNoCounter());
            int nAdd = TESTMODE_START_SPACE - (nCurFrSlot % TESTMODE_START_SPACE);
            m_wTxSlotMsg1 = CAisLib::FrameMapSlotIdAdd(nCurFrSlot, nAdd);
        }
        else
        {
            m_wTxSlotMsg1 = CAisLib::FrameMapSlotIdAdd(m_wTxSlotMsg1, nSlotTerm);
        }

        // MSG-1 test
        m_pTestChTx->SetFrameMapOneMsgColumn(TRUE, m_wTxSlotMsg1, TX_NUMSLOT, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, 0,
                                                            POS_REPORT_UNSCHEDULED, TDMA_RATDMA, TX_MSGID, 0, FALSE, FALSE, 0, FALSE);
    }
}

void CTestModeMgr::TMGetRxParam(BYTE bRxCH)
{
    UINT    rx_freq;
    WORD    wRxCh;
    char    pstrResp[64];

    pstrResp[0] = '\0';

    if(bRxCH == AIS_CHANNEL_AIS1)
    {
        // #AIINL,RFT,021,xxxx*hh<CR><LF>
        // xxxx = AIS Rx channel1 frequency
        rx_freq  = CAisLib::GetAisFreqByChannelNo(CLayerNetwork::getInst()->GetChPrimary()->m_uChNumRx);
        wRxCh = GetChFromFreqHz(rx_freq);
        sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_RX1, wRxCh);
        CSentence::AddSentenceTail(pstrResp);
    }
    else if(bRxCH == AIS_CHANNEL_AIS2)
    {
        // #AIINL,RFT,022,xxxx*hh<CR><LF>
        // xxxx = AIS Rx channel2 frequency
        rx_freq  = CAisLib::GetAisFreqByChannelNo(CLayerNetwork::getInst()->GetChSecondary()->m_uChNumRx);
        wRxCh = GetChFromFreqHz(rx_freq);
        sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_RX2, wRxCh);
        CSentence::AddSentenceTail(pstrResp);
    }
    else if(bRxCH == AIS_CHANNEL_DSC)
    {
        // #AIINL,RFT,023,xxxx*hh<CR><LF>
        // xxxx = DSC Rx channel frequency
        rx_freq  = CAisLib::GetAisFreqByChannelNo(CDSCMgr::getInst()->m_uChNumRx);
        wRxCh = GetChFromFreqHz(rx_freq);
        sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_RX3, wRxCh);
        CSentence::AddSentenceTail(pstrResp);
    }

    if(strlen(pstrResp) > 0)
    {
        SendOutTestModeStr(pstrResp);
    }
}

BOOL CTestModeMgr::TMSetRxParam(BYTE bRxCH, WORD wTxChNum)
{
    UINT    rx_freq;
    int     nChNum;
    char    pstrResp[64];

    pstrResp[0] = '\0';

    rx_freq = GetFreqHzFromCh(wTxChNum);
    nChNum = CAisLib::GetAisChannelNoByFreq(rx_freq);

    if(bRxCH == AIS_CHANNEL_AIS1)
    {
        if (CLayerNetwork::getInst()->GetChPrimary()->SetRxChannelNumber(nChNum))
        {
            sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_RX1, wTxChNum);
            CSentence::AddSentenceTail(pstrResp);
        }
        else
        {
            SendOutNak(TM_SET_RX1);
        }
    }
    else if(bRxCH == AIS_CHANNEL_AIS2)
    {
        if (CLayerNetwork::getInst()->GetChSecondary()->SetRxChannelNumber(nChNum))
        {
            sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_RX2, wTxChNum);
            CSentence::AddSentenceTail(pstrResp);
        }
        else
        {
            SendOutNak(TM_SET_RX2);
        }
    }
    else if(bRxCH == AIS_CHANNEL_DSC)
    {
        if (CDSCMgr::getInst()->SetRxChannelNumber(nChNum))
        {
            sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_GET_RX3, wTxChNum);
            CSentence::AddSentenceTail(pstrResp);
        }
        else
        {
            SendOutNak(TM_SET_RX3);
        }
    }
    else
    {
        return FALSE;
    }

    if(strlen(pstrResp) > 0)
    {
        SendOutTestModeStr(pstrResp);
    }

    return TRUE;
}

void CTestModeMgr::TMReceiveTestStart(void)
{
    m_bRxTestSignalRunning = TRUE;
}

void CTestModeMgr::TMReceiveTestStop(void)
{
    m_bRxTestSignalRunning = FALSE;
}

void CTestModeMgr::TMSetDscRcvTestCnt(int nCnt)
{
    m_nDscRcvTestCnt = nCnt;
    TMSendOutDscRcvTestCnt();
}

void CTestModeMgr::TMIncDscRcvTestCnt()
{
    m_nDscRcvTestCnt++;
    TMSendOutDscRcvTestCnt();
}

void CTestModeMgr::TMSendOutDscRcvTestCnt()
{
    char    pstrResp[64];

    sprintf((char*)pstrResp, "#AIINL,RFT,%03X,%04X", TM_RES_DSC_RX_COUNT, m_nDscRcvTestCnt);
    CSentence::AddSentenceTail(pstrResp);

    SendOutTestModeStr(pstrResp);
}

void CTestModeMgr::InitTxLoopbackTest()
{
    m_bTxTestSignalRunning  = TRUE;
    m_uTxTestSignalType     = TM_TX_LOOPBACK_TEST;

    m_wLoopbackTxSlot       = SLOTID_NONE;
    m_nLoopbackTxCnt        = 0;
    m_dwLoopbackTestCheckSec= cTimerSys::getInst()->GetCurTimerSec();

    m_bRunningTxLoopbackTest= TRUE;
    m_nTxLoopbackRcvCntCH1  = 0;
    m_nTxLoopbackRcvCntCH2  = 0;

    const INT16 nSlotTerm   = 5;

    int nCurFrSlot    = CAisLib::GetFrameMapSlotID(cAisModem::getInst()->GetCurrentFrameNo(), cAisModem::getInst()->GetSlotNoCounter());
    nCurFrSlot        = CAisLib::FrameMapSlotIdAdd(nCurFrSlot, 37);
    int nAdd          = nSlotTerm - (nCurFrSlot % nSlotTerm);
    m_wLoopbackTxSlot = CAisLib::FrameMapSlotIdAdd(nCurFrSlot, nAdd);
}

void CTestModeMgr::RunTxLoopbackTest()
{
    const int   LOOPBACK_TEST_TXCNT = 5;
    const INT16 TX_NUMSLOT = 1;
    const INT16 TX_MSGID= AIS_MSG_NO_ID_01;
    const INT16 nSlotTerm = 10;

    if(m_bRunningTxLoopbackTest && m_uTxTestSignalType == TM_TX_LOOPBACK_TEST)
    {
        if(m_nLoopbackTxCnt >= LOOPBACK_TEST_TXCNT && CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_wLoopbackTxSlot) >= NUM_SLOT_PER_HALFFRAME)
        {
            BOOL bResultCH1   = m_nTxLoopbackRcvCntCH1 >= 1;
            m_wErrorCodeBITE |= (bResultCH1 ? BITE_NO_ERROR : BITE_TXLOOPBACK_CH1_ERROR);

            BOOL bResultCH2   = m_nTxLoopbackRcvCntCH2 >= 1;
            m_wErrorCodeBITE |= (bResultCH2 ? BITE_NO_ERROR : BITE_TXLOOPBACK_CH2_ERROR);

            m_bRunningTxLoopbackTest= FALSE;

            m_bTxTestSignalRunning  = FALSE;
            m_uTxTestSignalType     = TM_TX_DATA_NON;

            FinishBITE();
            return;
        }

        if(m_nLoopbackTxCnt < LOOPBACK_TEST_TXCNT)
        {
            m_wLoopbackTxSlot = CAisLib::FrameMapSlotIdAdd(m_wLoopbackTxSlot, nSlotTerm);
            CLayerNetwork::getInst()->GetChPrimary()->SetFrameMapOneMsgColumn(TRUE, m_wLoopbackTxSlot, TX_NUMSLOT,
                                                                cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, 0,
                                                                POS_REPORT_UNSCHEDULED, TDMA_RATDMA, TX_MSGID, 0, FALSE, FALSE, 0, FALSE);

            m_wLoopbackTxSlot = CAisLib::FrameMapSlotIdAdd(m_wLoopbackTxSlot, nSlotTerm);
            CLayerNetwork::getInst()->GetChSecondary()->SetFrameMapOneMsgColumn(TRUE, m_wLoopbackTxSlot, TX_NUMSLOT,
                                                                cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC, 0,
                                                                POS_REPORT_UNSCHEDULED, TDMA_RATDMA, TX_MSGID, 0, FALSE, FALSE, 0, FALSE);
            m_nLoopbackTxCnt++;
        }

        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwLoopbackTestCheckSec) >= 1)
        {
            m_dwLoopbackTestCheckSec = cTimerSys::getInst()->GetCurTimerSec();
        }
    }
}

void CTestModeMgr::IncTxLoopbackRcvCntCH1()
{
    m_nTxLoopbackRcvCntCH1++;
}

void CTestModeMgr::IncTxLoopbackRcvCntCH2()
{
    m_nTxLoopbackRcvCntCH2++;
}

void CTestModeMgr::RunBITE(BOOL bRunOnBoot)
{
    if(!bRunOnBoot && !IsTestModeRunning())
    {
        m_wErrorCodeBITE = BITE_UNKNOWN_ERROR;
    }
    else
    {
        m_wErrorCodeBITE = BITE_NO_ERROR;
        m_bRunOnBootBITE = bRunOnBoot;

		if(CBuiltInTestMgr::getInst()->IsDcVoltageFail())
			m_wErrorCodeBITE |= BITE_VOLTAGE_ERROR;

        if(CBuiltInTestMgr::getInst()->IsErrorROM())
            m_wErrorCodeBITE |= BITE_ROM_ERROR;

        if(CBuiltInTestMgr::getInst()->IsErrorEEPROM())
            m_wErrorCodeBITE |= BITE_EEPROM_ERROR;

        if(!CSensorMgr::getInst()->IsIntGNSSConnected())
            m_wErrorCodeBITE |= BITE_GPS_ERROR;
    }

    if(bRunOnBoot)
    {
        FinishBITE();
    }
    else
    {
        InitTxLoopbackTest();
    }
}

void CTestModeMgr::FinishBITE()
{
    CMKD::getInst()->SendOutResultBITE();
}

BOOL CTestModeMgr::TestModeProcess()
{
    static DWORD dwCheckSec = 0;

    if(IsTestModeRunning())
    {
        if(m_pCommPI)
            m_pCommPI->ProcessData(NULL);

        if(!IsTestModeRunning())
            return FALSE;

        TMTransmitterTestProc();

        if(IsBuiltInTestRunning() && cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 1)
        {
            CMKD::getInst()->SendOutVoltageDC();
            dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
        }
    }
    return TRUE;
}
