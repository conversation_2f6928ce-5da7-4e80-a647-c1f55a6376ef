/**
 * @file    RxModem.cpp
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "AisModem.h"
#include "RxModem.h"
#include "SysLib.h"
#include "ComLib.h"
#include "AisLib.h"

#if defined(RX_GMSK_FILTER_USE_MODE)
static float G_vRxGmskCoefficient[RX_GMSK_BT_0_5_FIR_N] = 
{
    0.0000, 0.0000, 0.0000, 0.0004, 0.0212, 0.2275, 0.5018, 0.2275,
    0.0212, 0.0004, 0.0000, 0.0000, 0.0000
}; // BT=0.5
#endif

//============================================================================
// preamble : 110011001100110011001100
// oversampling : 5
// impulse response : gmsk
// BT : 0.5
static float G_vGmskPreamble[RX_PREAMBLE_LEN] =
{
    0.0000, 0.0000, 0.0000, 0.0004, 0.0212, 0.2275, 0.5018, 0.2275,
    0.0216, 0.0216, 0.2275, 0.5018, 0.2275, 0.0212, 0.0004, 0.0000,
    0.0000, 0.0000,      0,      0, 0.0000, 0.0000, 0.0000, 0.0004,
    0.0212, 0.2275, 0.5018, 0.2275, 0.0216, 0.0216, 0.2275, 0.5018,
    0.2275, 0.0212, 0.0004, 0.0000, 0.0000, 0.0000,      0,      0,
    0.0000, 0.0000, 0.0000, 0.0004, 0.0212, 0.2275, 0.5018, 0.2275,
    0.0216, 0.0216, 0.2275, 0.5018, 0.2275, 0.0212, 0.0004, 0.0000,
    0.0000, 0.0000,      0,      0, 0.0000, 0.0000, 0.0000, 0.0004,
    0.0212, 0.2275, 0.5018, 0.2275, 0.0216, 0.0216, 0.2275, 0.5018,
    0.2275, 0.0212, 0.0004, 0.0000, 0.0000, 0.0000,      0,      0,
    0.0000, 0.0000, 0.0000, 0.0004, 0.0212, 0.2275, 0.5018, 0.2275,
    0.0216, 0.0216, 0.2275, 0.5018, 0.2275, 0.0212, 0.0004, 0.0000,
    0.0000, 0.0000,      0,      0, 0.0000, 0.0000, 0.0000, 0.0004,
    0.0212, 0.2275, 0.5018, 0.2275, 0.0216, 0.0216, 0.2275, 0.5018,
    0.2275, 0.0212, 0.0004, 0.0000, 0.0000, 0.0000,      0,      0,
        0,      0,      0,      0,      0,      0,      0,      0,
        0,      0,      0,      0,
};
//============================================================================
cRxModem::cRxModem(int nRxChannelNo, float fRxReferMinVal, float fRxReferMidVal, float fRxReferMaxVal)
{
    m_nRxChannelNo  = nRxChannelNo;
    m_fRxReferMinVal= fRxReferMinVal;
    m_fRxReferMidVal= fRxReferMidVal;
    m_fRxReferMaxVal= fRxReferMaxVal;
    m_fRxReferValue = m_fRxReferMidVal;

    m_vRxRawFormBuff = (xAisRxRawForm*)SysAllocMemory(sizeof(xAisRxRawForm) * RX_RAW_FORM_BUFF_SIZE);

#if defined(RX_GMSK_FILTER_USE_MODE)
    memset((void *)m_pRxRawGmskBuff, 0x00, sizeof(float) * RX_GMSK_BT_0_5_FIR_N);
#endif

    m_dSwRxPllValue = 0;
    m_dSwRxPllSampC = 0;
    m_dSwRxPllSampP = 0;
    m_dSwRxPllCntrX = 0;

    m_wBitSamplCntr = 0;
    m_wBitSamplTggl = 0;

    m_wRxNrziPrev   = 0;
    m_wRxNrziCurr   = 0;
    m_wRxNrziTemp   = 0;
    m_wRxNrziCntr   = 0;

    m_fRxAfAdcSumVal = 0;
    m_dRxAfAdcCntVal = 0;

    m_wRxRunStatus  = RX_MDM_STATUS_PREAMBLE;
    m_wRxBitCount   = 0;
    m_wRxShiftReg   = 0;

    m_wReceivingID  = 0;
    m_wRxMaxBitSize = 168;

    m_wRxPrevBitD   = 0;
    m_wRxCurrBitD   = 0;

    m_wNewBitData   = 0;
    m_wCrcRegData   = 0;
    m_bRxByteData   = 0;

    m_fRxAfAdcRawX  = 0;
    m_fRxAfAdcData  = 0;

    m_dRxAdcErrCnt  = 0;

    m_fRxSyncPowSum = 0;
    for (int i = 0; i < RX_PREAMBLE_LEN; i++)
    {
        m_fRxSyncPowSum += (G_vGmskPreamble[i] * G_vGmskPreamble[i]);
    }

    m_vRxNormBuff  = (float*)SysAllocMemory(sizeof(float) * RX_PREAMBLE_LEN);

    ClearPreambleBuff();
    ClearFullDotData();
    ClearFullPreData();
    ClearRxRawBuff();
} 

cRxModem::~cRxModem(void)
{
}

void  cRxModem::ClearPreambleBuff(void)
{
    memset((void *)m_vRxNormBuff, 0x00, sizeof(float) * RX_PREAMBLE_LEN);

    m_fRxNormAdc    = 0;
    m_dRxNormBuffIdx = 0;
    m_bPreambleDetected = FALSE;
    m_fMaxCorr      = 0;
    m_dMaxCorrIdx   = 0;
    m_dMaxElapseCount = 0;
    m_fMaxCorrValue = 0;
}

void  cRxModem::ClearFullDotData(void)
{
    m_xDotData.wDotPattern = 0;
    m_xDotData.wDotChanged = 0;

    memset((void *)m_xDotData.wDotCountX, 0x00, sizeof(m_xDotData.wDotCountX));

    m_fRxAfAdcSumVal = 0;
    m_dRxAfAdcCntVal = 0;
}

void  cRxModem::ClearFullPreData(void)
{
    m_xPreData.nPntX =-1;
    m_xPreData.fSumX = m_fRxReferMidVal * RX_PRE_MAX_BUF_SIZE;
    m_xPreData.dCntX = 0;
    m_xPreData.fAvrX = m_fRxReferMidVal;
}

void  cRxModem::ClearRxRawBuff(void)
{
    memset((void *)m_vRxRawFormBuff, 0x00, sizeof(xAisRxRawForm)*RX_RAW_FORM_BUFF_SIZE);
    m_nRxRawFormHead = 0;
    m_nRxRawFormTail = 0;
    m_nRxRawFormTemp = 0;
}

void  cRxModem::ClearRxRawFormTemp(void)
{
    m_nRxRawFormTemp = 0;
}

#if defined(RX_GMSK_FILTER_USE_MODE)
float cRxModem::RunGmskFilter(float fInAdc)
{
    volatile float fOutAdc = 0;

    memmove((void *)&m_pRxRawGmskBuff[0], (void *)&m_pRxRawGmskBuff[1], sizeof(float) * (RX_GMSK_BT_0_5_FIR_N-1));
    m_pRxRawGmskBuff[RX_GMSK_BT_0_5_FIR_N - 1] = fInAdc;

    // Convolution with GMSK filter coefficients
    for (int i = 0; i < RX_GMSK_BT_0_5_FIR_N; i++)
    {
        fOutAdc += (m_pRxRawGmskBuff[i] * G_vRxGmskCoefficient[i]);
    }

    return fOutAdc;
}
#endif

BOOL cRxModem::RunDetectPreamble(float fInAdc)
{
    volatile float fCrossSync = 0;
    volatile float fSyncCorr  = 0;
    volatile int   nBuffIdx   = 0;

    // Pre calculate power value
    m_fRxNormAdc -= (m_vRxNormBuff[m_dRxNormBuffIdx] * m_vRxNormBuff[m_dRxNormBuffIdx]);
    m_fRxNormAdc += (fInAdc * fInAdc);
    m_vRxNormBuff[m_dRxNormBuffIdx] = fInAdc;

    m_dRxNormBuffIdx++;
    if (m_dRxNormBuffIdx >= RX_PREAMBLE_LEN)
    {
        m_dRxNormBuffIdx = 0;
    }

    // convolution with preamble
    nBuffIdx = m_dRxNormBuffIdx;
    for (int i = 0; i < RX_PREAMBLE_LEN; i++)
    {
        fCrossSync += (m_vRxNormBuff[nBuffIdx] * G_vGmskPreamble[i]);

        nBuffIdx++;
        if (nBuffIdx >= RX_PREAMBLE_LEN)
            nBuffIdx = 0;
    }

    // calculate correlation value
    if (m_fRxNormAdc != 0 && m_fRxSyncPowSum != 0)
        fSyncCorr = (fCrossSync * fCrossSync) / (m_fRxNormAdc * m_fRxSyncPowSum);
    else
        fSyncCorr = 0;

    // for debugging correlation value
    if (m_fMaxCorrValue < fSyncCorr)
    {
        m_fMaxCorrValue = fSyncCorr;
    }

    // find correlation peak point
    if ((fSyncCorr > RX_SYNC_THRESHOLD) && (fSyncCorr > m_fMaxCorr))
    {
        m_fMaxCorr = fSyncCorr;
        ////m_dMaxCorrIdx = dIdx;
        m_dMaxElapseCount = 0;
    }
    else if (m_fMaxCorr > RX_SYNC_THRESHOLD)
    {
        m_dMaxElapseCount++;

        // Confirm peak
        if (m_dMaxElapseCount >= RX_SYNC_STABLE_CNT)
        {
            m_bPreambleDetected = TRUE;

            // Use center symbol value
            //m_dRxDataStartIdx = (m_dMaxCorrIdx + m_dFskDelayDataStart) % m_dFskDwsBuffSize;

            //m_dRxDataSymbolCnt = 0;
            //m_bRxDataSymbolStarted = TRUE;
        }
    }

    return m_bPreambleDetected;
}

int cRxModem::ProcessRxDataAllInINTR(HWORD wRxAfAdcData)
{
    m_fRxAfAdcRawX = (float)wRxAfAdcData / ADC_RES_MAX_VALUE;
    m_fRxAfAdcData = m_fRxAfAdcRawX;

#if defined(RX_GMSK_FILTER_USE_MODE)
    // GMSK Filter
    m_fRxAfAdcData = RunGmskFilter(m_fRxAfAdcData);
#endif

#if defined(RX_CORR_PREAMBLE_DETECT)
    // correlation Sync detect
    if (RunDetectPreamble(m_fRxAfAdcData))
    {
        ClearPreambleBuff();
    }
#endif

    if(m_fRxAfAdcRawX < m_fRxReferMinVal || m_fRxAfAdcRawX > m_fRxReferMaxVal)
    {
        ClearFullPreData();

        if(m_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        {
            m_dRxAdcErrCnt = 0;
        }
        else
        {
            ++m_dRxAdcErrCnt;
            if(m_dRxAdcErrCnt > 20)
            {
                m_dRxAdcErrCnt = 0;
                ResetToRxStatusPreamble();
            }
        }
    }
    else
    {
        AppendOnePreData(m_fRxAfAdcData);
    }

    if(m_fRxAfAdcData > m_fRxReferValue)
        m_wRxNrziCurr = 1;
    else 
        m_wRxNrziCurr = 0;

    if(m_wRxNrziCurr != m_wRxNrziTemp)
    {
        if(m_wRxRunStatus > RX_MDM_STATUS_PREAMBLE && (m_wRxNrziCntr <= (AIS_DATA_SAMPLES_PER_ONE_BIT - 2) || (m_wRxNrziCntr == (AIS_DATA_SAMPLES_PER_ONE_BIT - 1) && m_dSwRxPllValue >= (RX_SW_PLL_FULL_VALUE - RX_SW_PLL_INCR_VALUE + RX_SW_PLL_STEP_VALUE))))
        {
            ++m_wRxNrziCntr;
            m_wRxNrziCurr = m_wRxNrziTemp;
        }
        else
        {
            m_wRxNrziCntr = 1;
        }
    }
    else
    {
        ++m_wRxNrziCntr;
    }
    
    m_wRxNrziTemp = m_wRxNrziCurr;

    return(ProcessRxDataSwPllRun());
}

int cRxModem::ProcessRxDataSwPllRun(void)
{
    if(m_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
    {
        ProcessRxStatusPreamble();
        m_wRxNrziPrev = m_wRxNrziCurr;
        return 0;
    }

    m_dSwRxPllSampC = m_wRxNrziCurr;

    if(m_dSwRxPllSampC != m_dSwRxPllSampP)
    {
        if(m_wRxRunStatus == RX_MDM_STATUS_START)
        {
            if(m_dSwRxPllCntrX >= (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 - 2) && m_dSwRxPllCntrX <= (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 + 2))
            {
                m_dSwRxPllValue = RX_SW_PLL_HALF_VALUE + RX_SW_PLL_STEP_VALUE;
            }

            if(m_dSwRxPllCntrX == (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 - 2))    m_dSwRxPllValue = RX_SW_PLL_HALF_VALUE - RX_SW_PLL_INCR_VALUE + RX_SW_PLL_STEP_VALUE;
            if(m_dSwRxPllCntrX == (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 - 1))    m_dSwRxPllValue = RX_SW_PLL_HALF_VALUE                        - RX_SW_PLL_STEP_VALUE;
            if(m_dSwRxPllCntrX == (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 + 2))    m_dSwRxPllValue = RX_SW_PLL_HALF_VALUE + RX_SW_PLL_INCR_VALUE - RX_SW_PLL_STEP_VALUE;
            if(m_dSwRxPllCntrX == (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 + 1))    m_dSwRxPllValue = RX_SW_PLL_HALF_VALUE                        + RX_SW_PLL_STEP_VALUE;
        }

        if(m_dSwRxPllValue < RX_SW_PLL_HALF_VALUE)
        {
            m_dSwRxPllValue += RX_SW_PLL_STEP_VALUE;
        }
        else
        {
            m_dSwRxPllValue -= RX_SW_PLL_STEP_VALUE;
        }

        m_dSwRxPllCntrX = 1;
    }
    else
    {
        m_dSwRxPllCntrX++;
    }

    m_dSwRxPllSampP = m_dSwRxPllSampC;

    m_dSwRxPllValue += RX_SW_PLL_INCR_VALUE;
    if(m_dSwRxPllValue >= RX_SW_PLL_FULL_VALUE)
        m_dSwRxPllValue -= RX_SW_PLL_FULL_VALUE;
    else
        return 0;

    m_wBitSamplTggl = 1 - m_wBitSamplTggl;
    m_wRxCurrBitD = m_dSwRxPllSampC;

    return(ProcessRxDataCommonRun());
}

int cRxModem::ProcessRxDataCommonRun(void)
{
    m_wRxShiftReg <<= 1;

    if(m_wRxCurrBitD == m_wRxPrevBitD)
        m_wRxShiftReg |= 0x0001;
    else
        m_wRxShiftReg &= 0xfffe;

    m_wRxPrevBitD = m_wRxCurrBitD;

    switch (m_wRxRunStatus)
    {
    case RX_MDM_STATUS_START:
        if((m_wRxShiftReg & 0x00ff) == 0x007e) {
            m_dSampleCounter = cAisModem::getInst()->GetSampleCounter();
            m_dSlotNoCounter = cAisModem::getInst()->GetSlotNoCounter();

            m_wRxBitCount    = 0;
            m_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
        }
        else {
            ++m_wRxBitCount;
            if(m_wRxBitCount >= 32) {
                ResetToRxStatusPreamble();
            }
        }
        break;

    case RX_MDM_STATUS_PRELOAD:
        if(++m_wRxBitCount == 8) {
            m_wRxBitCount = 0;
            m_wCrcRegData = 0xffff;
            m_wRxRunStatus= RX_MDM_STATUS_DATA;
            ClearRxRawFormTemp();

            int nMsgID = GetReverseBitValue((m_wRxShiftReg << 2) & 0xff);
            if(nMsgID < 0) {
                ResetToRxStatusPreamble();
            }
            else {
                m_wReceivingID  = nMsgID;
                m_wRxMaxBitSize = CAisLib::GetAisRxMsgMaxBitSizeByMsgID(m_wReceivingID);
            }
       }
       break;

    case RX_MDM_STATUS_DATA:
        if((m_wRxShiftReg & 0x3f00) != 0x3e00)        // It's not a stuffing bit
        {
            ++m_wRxBitCount;
            if(m_wRxBitCount >= m_wRxMaxBitSize) {
                ResetToRxStatusPreamble();
                return 0;
            }

            m_wNewBitData = (m_wRxShiftReg >> 8) & 0x0001;
            m_bRxByteData = (m_bRxByteData >> 1) | ((m_wRxShiftReg >> 1) & 0x0080);

            if(!(m_wRxBitCount & 0x07))
                PutDataIntoRxRawBuff(m_bRxByteData);

            if((m_wCrcRegData ^ m_wNewBitData) & 0x0001)          // Pass new bit through CRC calculation
                m_wCrcRegData = (m_wCrcRegData >> 1) ^ 0x8408;    // Xor with the CRC polynomial (X^16 + X^12 + X^5 + 1)
            else
                m_wCrcRegData >>= 1;
        }

        if((m_wRxShiftReg & 0x00ff) == 0x007e) {
            if(m_wCrcRegData == 0xf0b8) {    // This should give a result of 0xF0B8
                WritePacketIntoRxRawBuff();
                ResetToRxStatusPreamble();
            }
            else {
                m_dSampleCounter = cAisModem::getInst()->GetSampleCounter();
                m_dSlotNoCounter = cAisModem::getInst()->GetSlotNoCounter();

                m_wRxBitCount    = 0;
                m_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
            }
        }
        break;

    default:
        break;
    }

    return 0;
}

void  cRxModem::ProcessRxStatusPreamble(void)
{
    m_fRxAfAdcSumVal += m_fRxAfAdcData;
    m_dRxAfAdcCntVal++;

    if(m_wRxNrziPrev == m_wRxNrziCurr) {
        ++m_xDotData.wDotCountX[RX_DOT_MAX_CNT_LAST];
        return;
    }

    if(m_xDotData.wDotCountX[RX_DOT_MAX_CNT_LAST] < (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 - 3) || m_xDotData.wDotCountX[RX_DOT_MAX_CNT_LAST] > (AIS_DATA_SAMPLES_PER_ONE_BIT * 2 + 3))
    {
        if(m_xDotData.wDotChanged)
            ClearFullDotData();

        m_xDotData.wDotCountX[RX_DOT_MAX_CNT_LAST] = 1;

        m_fRxAfAdcSumVal = m_fRxAfAdcData;
        m_dRxAfAdcCntVal = 1;
        return;
    }

    if(m_wRxNrziPrev) {
        m_xDotData.wDotPattern |= 0x0001;
    }

    m_xDotData.wDotPattern &= RX_DOT_MAX_CNT_MASK;
    if(m_xDotData.wDotPattern != 0x0001 && m_xDotData.wDotPattern != 0x0002 && m_xDotData.wDotPattern != 0x0005 && m_xDotData.wDotPattern != 0x000a && m_xDotData.wDotPattern != 0x0015 && m_xDotData.wDotPattern != 0x002a && m_xDotData.wDotPattern != 0x0055)
    {
        if(m_xDotData.wDotPattern)
            ClearFullDotData();

        m_xDotData.wDotCountX[RX_DOT_MAX_CNT_LAST] = 1;

        m_fRxAfAdcSumVal = m_fRxAfAdcData;
        m_dRxAfAdcCntVal = 1;
        return;
    }

    if(m_xDotData.wDotPattern == RX_DOT_START_P_MASK) {
        m_fRxAfAdcSumVal = m_fRxAfAdcData;
        m_dRxAfAdcCntVal = 1;
    }

    m_xDotData.wDotChanged = 1;

    if(m_xDotData.wDotPattern == RX_DOT_DETCT_P_MASK) {
        m_fRxReferValue = m_fRxAfAdcSumVal / m_dRxAfAdcCntVal;

        BeginRxStatusStart();
        ClearFullDotData();
    }
    else {
        m_xDotData.wDotPattern <<= 1;
        memmove((void *)&m_xDotData.wDotCountX[0], (void *)&m_xDotData.wDotCountX[1], sizeof(m_xDotData.wDotCountX[1]) * RX_DOT_MAX_CNT_LAST);

        m_xDotData.wDotCountX[RX_DOT_MAX_CNT_LAST] = 1;
    }
}

void  cRxModem::ResetToRxStatusPreamble(void)
{
    m_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
    m_wRxShiftReg  = 0;
    m_fRxReferValue= m_fRxReferMidVal;

    ClearFullDotData();
    ClearFullPreData();
}

void  cRxModem::BeginRxStatusStart(void)
{
    m_wRxRunStatus  = RX_MDM_STATUS_START;
    m_wRxShiftReg   = 0;
    m_wRxBitCount   = 0;
    m_wRxPrevBitD   = m_wRxNrziPrev;

    m_wBitSamplCntr = 1;

    m_dRxAdcErrCnt  = 0;

    m_dSwRxPllValue = RX_SW_PLL_HALF_VALUE;
    m_dSwRxPllCntrX = 1;
    m_dSwRxPllSampC = m_wRxNrziPrev;
    m_dSwRxPllSampP = m_wRxNrziPrev;
}

xAisRxRawForm *cRxModem::GetFullPacketFromRxRawBuff(void)
{
    xAisRxRawForm *pAisRxRawForm;

    if(m_nRxRawFormHead == m_nRxRawFormTail)
        return(NULL);

    pAisRxRawForm = (xAisRxRawForm*)&m_vRxRawFormBuff[m_nRxRawFormTail];

    ++m_nRxRawFormTail;
    if(m_nRxRawFormTail >= RX_RAW_FORM_BUFF_SIZE)
        m_nRxRawFormTail  = 0;

    return(pAisRxRawForm);
}

void  cRxModem::WritePacketIntoRxRawBuff(void)
{
    if(m_nRxRawFormTemp == 0 || m_wRxBitCount < 40)
        return;
    m_vRxRawFormBuff[m_nRxRawFormHead].wMsgFrameNO    = cAisModem::getInst()->GetCurrentFrameNo();
    m_vRxRawFormBuff[m_nRxRawFormHead].dSlotNoCounter = m_dSlotNoCounter;
    m_vRxRawFormBuff[m_nRxRawFormHead].dSampleCounter = m_dSampleCounter;
    m_vRxRawFormBuff[m_nRxRawFormHead].wRxByteSize    = m_nRxRawFormTemp -  2; // - FCS (2-bytes)
    m_vRxRawFormBuff[m_nRxRawFormHead].wRxBitsSize    = m_wRxBitCount    - 16; // - FCS (16-bits)

    ++m_nRxRawFormHead;
    if(m_nRxRawFormHead >= RX_RAW_FORM_BUFF_SIZE)
        m_nRxRawFormHead  = 0;
}

void  cRxModem::PutDataIntoRxRawBuff(UCHAR bRxData)
{
    if(m_nRxRawFormTemp < (AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX - 2))
        m_vRxRawFormBuff[m_nRxRawFormHead].vRxRawData[m_nRxRawFormTemp++] = bRxData;
}

void  cRxModem::AppendOnePreData(float fData)
{
    if(m_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
    {
        ++m_xPreData.nPntX;
        if(m_xPreData.nPntX >= RX_PRE_MAX_BUF_SIZE)
            m_xPreData.nPntX  = 0;

        if(m_xPreData.dCntX >= RX_PRE_MAX_BUF_SIZE)
        {
            m_xPreData.fSumX -= m_xPreData.vData[m_xPreData.nPntX];
        }
        else
        {
            m_xPreData.fSumX -= m_fRxReferMidVal;
            ++m_xPreData.dCntX;
        }

        m_xPreData.fSumX += fData;
        m_xPreData.fAvrX  = m_xPreData.fSumX / RX_PRE_MAX_BUF_SIZE;
        m_xPreData.vData[m_xPreData.nPntX] = fData;

        m_fRxReferValue = m_xPreData.fAvrX;
    }
}
