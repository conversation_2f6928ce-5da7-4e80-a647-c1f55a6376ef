/**
 * @file    Lrf.h
 * @brief   Lrf header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __LRF_H__
#define __LRF_H__

/******************************************************************************
*
* LRF - Long Range Function
*
* $--LRF,x,xxxxxxxxx,c--c,c--c,c--c*hh<CR><LF>
*        | |         |    |    |
*        1 2         3    4    5
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of requester
* 3.  Name of requester, 1 to 20 character string
* 4.  Function request , 1 to 26 characters
* 5.  Function reply status
*
******************************************************************************/
class CLrf : public CSentence
{
public:
    CLrf();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

public:
    static int8_t m_nSequentialId;
};

#endif /* __LRF_H__ */

