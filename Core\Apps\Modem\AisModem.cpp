/**
 * @file    AisModem.cpp
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include "Define_AIS.h"
#include "GmskLib.h"
#include "AisLib.h"
#include "SysLog.h"
#include "Timer.h"
#include "DAC.h"
#include "PLL.h"
#include "GPIOExt.h"
#include "LayerPhysical.h"
#include "LayerNetwork.h"
#include "TestModeMgr.h"
#include "SyncMgr.h"
#include "SetupMgr.h"
#include "SensorMgr.h"
#include "AisModem.h"

BOOL m_bReserveTxOff = FALSE;
int  m_nReserveTxCnt = 0;

//---------------------------
// Definition
//---------------------------
#define IsAisTestModeInfiniteTx() (CTestModeMgr::getInst()->IsTestModeRunning() && m_bInfiniteTxMode)

cAisModem::cAisModem(std::shared_ptr<cRxModem> RxModemA, std::shared_ptr<cRxModem> RxModemB)
{
    m_nSampleCounter    = 0;
    m_nSlotNoCounter    = 0;

    m_nCurrentFrameNo   = 0;

    m_nUtcIndirectMode  = 0;
    m_nUtcIndirectSlot  = 0;
    m_nUtcIndirectSamp  = 0;
    m_dUtcIndirectTick  = 0;

    m_dUtcDirectPrevPin = 0;
    m_dUtcDirectCurrPin = 0;

    m_pAisTxGmskDATA    = GetAisTxGmskDATA();

    ClearAisTxGmskBuffData();
    ClearAisTxGmskDATA();

    SetAisTxGmskDacRefOffset(DEFAULT_DC_OFFSET, DEFAULT_DC_SHIFT);

    m_vTxGmskCnterDAC[0] = GetAisTxGmskCnterDAC(0);
    m_vTxGmskCnterDAC[1] = GetAisTxGmskCnterDAC(1);
    cDac::getInst()->SetDAC0Data(m_vTxGmskCnterDAC[0]);
    cDac::getInst()->SetDAC1Data(0);

    m_pRxModemA         = RxModemA;
    m_pRxModemB         = RxModemB;

    m_bRunTxBuffReset   = FALSE;
    m_bInfiniteTxMode   = FALSE;
}

cAisModem::~cAisModem(void)
{
}

void cAisModem::InitModemSetup(WORD wDcOffset, WORD wDcShift)
{
    SetAisTxGmskDacRefOffset(CSetupMgr::getInst()->GetDcOffset(), CSetupMgr::getInst()->GetDcShift());

    m_vTxGmskCnterDAC[0] = GetAisTxGmskCnterDAC(0);
    m_vTxGmskCnterDAC[1] = GetAisTxGmskCnterDAC(1);
    cDac::getInst()->SetDAC0Data(m_vTxGmskCnterDAC[0]);
    cDac::getInst()->SetDAC1Data(0);
}

void cAisModem::AddTotalSlotShift(int nCurShiftSlot)
{
    m_nTotalSlotShift += nCurShiftSlot;
}

void cAisModem::RecalcNewFrameID(int nOldSlotId)
{
    int nOldFrameID= m_nCurrentFrameNo;
    int nShiftSlot = m_nSlotNoCounter - nOldSlotId;
    BOOL bIncFrame = FALSE;
    if(nShiftSlot != 0)
    {
        int nDiffSlot1 = CAisLib::GetDiffDword((DWORD)nOldSlotId, (DWORD)m_nSlotNoCounter, SLOTID_MAX);
        int nDiffSlot2 = CAisLib::GetDiffDword((DWORD)m_nSlotNoCounter, (DWORD)nOldSlotId, SLOTID_MAX);
        int nMinDiffSlot = MIN(nDiffSlot1, nDiffSlot2);
        if(abs(nShiftSlot) != nMinDiffSlot)
        {
            bIncFrame = (nShiftSlot < 0 && nDiffSlot1 < nDiffSlot2);
            bIncFrame ? cAisModem::IncCurrentFrameNo() : cAisModem::DecCurrentFrameNo();
        }

        DEBUG_LOG("SSync] RecalcFrameID, syncSlot:%d -> %d, shiftSlot:%d, diff:%d,%d, minDiff:%d, incFr:%d, %d,%d -> %d,%d\r\n",
                nOldSlotId, m_nSlotNoCounter, nShiftSlot, nDiffSlot1, nDiffSlot2, nMinDiffSlot,
                bIncFrame, nOldFrameID, nOldSlotId, m_nCurrentFrameNo, m_nSlotNoCounter);
    }
}

void  cAisModem::ProcessTxRxAllInINTR(HWORD wRxAfAdcDataA, HWORD wRxAfAdcDataB)
{
#if (__UTC_DIRECT_TEST_ENABLED__)
    static DWORD dCounterMode = 0;
    DWORD  dTempX = m_nSlotNoCounter * AIS_SAMPLE_COUNTER_MAX_VAL + m_nSampleCounter;
    if (dTempX == (dTempX / ONE_SEC_TICK * ONE_SEC_TICK))
    {
        GPIOA->BSRRL = (0x1UL << TEST_STATE1);     // set
        dCounterMode = ONE_SEC_TICK / 10;
    }
    if (dCounterMode)
    {
        --dCounterMode;
        if (dCounterMode == 0)
            GPIOA->BSRRH = (0x1UL << TEST_STATE1); // clear
    }
#endif

    ++m_nSampleCounter;
    if (m_nSampleCounter >= AIS_SAMPLE_COUNTER_MAX_VAL)
    {
        m_nSampleCounter  = 0;
        ++m_nSlotNoCounter;
        if (m_nSlotNoCounter >= AIS_SLOTS_PER_ONE_FRAME)
        {
            m_nSlotNoCounter = 0;
            IncCurrentFrameNo();
        }
    }

    int nOldFrameNo = m_nCurrentFrameNo;
    int nOldSlotNo = m_nSlotNoCounter;

    m_dUtcDirectCurrPin = GetGpioGpsPPS();
    if(CSyncMgr::getInst()->IsUtcDirectSyncRunning() && CSensorMgr::getInst()->IsPosUtcFixed(SENSORID_0))
    {
        int nSec = CSensorMgr::getInst()->GetIntGNSSUtcSec();
        if (m_dUtcDirectCurrPin && !m_dUtcDirectPrevPin)
        {
            if (nSec & 0x00000001)                                                  // Sync to odd seconds
            {
                int nOldSyncSec = m_nUtcSyncSec;

                m_nSampleCounter = 0;
                m_nSlotNoCounter = (nSec+1) * AIS_SLOTS_PER_ONE_FRAME / 60;         // Sync to odd seconds
                if (m_nSlotNoCounter >= AIS_SLOTS_PER_ONE_FRAME)
                    m_nSlotNoCounter -= AIS_SLOTS_PER_ONE_FRAME;

                RecalcNewFrameID(nOldSlotNo);
                int nCurShiftSlot = CAisLib::GetSlotShift(nOldFrameNo, nOldSlotNo, m_nCurrentFrameNo, m_nSlotNoCounter);
                if(nCurShiftSlot)
                {
                    m_nUtcSyncSec = nSec;
                    AddTotalSlotShift(nCurShiftSlot);
                }

                DEBUG_LOG("SSync] DirSync end, syncSec:%d -> %d, slot:%d,%d -> %d,%d, shift:%d, total:%d\r\n",
                        nOldSyncSec, nSec, nOldFrameNo, nOldSlotNo,
                        m_nCurrentFrameNo, m_nSlotNoCounter,
                        nCurShiftSlot, m_nTotalSlotShift);
            }
        }
    }

    m_dUtcDirectPrevPin = m_dUtcDirectCurrPin;

    if (CSyncMgr::getInst()->IsUtcIndirectSyncRunning() && m_nUtcIndirectMode)
    {
        if (m_nUtcIndirectMode > 0)
        {
            m_nSlotNoCounter += m_nUtcIndirectSlot;
            m_nSampleCounter += m_nUtcIndirectSamp;

            if (m_nSampleCounter >= AIS_SAMPLE_COUNTER_MAX_VAL)
            {
                m_nSampleCounter -= AIS_SAMPLE_COUNTER_MAX_VAL;
                ++m_nSlotNoCounter;
            }
            if (m_nSlotNoCounter >= AIS_SLOTS_PER_ONE_FRAME)
                m_nSlotNoCounter -= AIS_SLOTS_PER_ONE_FRAME;
        }
        else
        {
            m_nSlotNoCounter -= m_nUtcIndirectSlot;
            m_nSampleCounter -= m_nUtcIndirectSamp;

            if (m_nSampleCounter < 0)
            {
                m_nSampleCounter += AIS_SAMPLE_COUNTER_MAX_VAL;
                --m_nSlotNoCounter;
            }
            if (m_nSlotNoCounter < 0)
                m_nSlotNoCounter += AIS_SLOTS_PER_ONE_FRAME;
        }

        RecalcNewFrameID(nOldSlotNo);
        int nCurShiftSlot = CAisLib::GetSlotShift(nOldFrameNo, nOldSlotNo, m_nCurrentFrameNo, m_nSlotNoCounter);
        if(nCurShiftSlot != 0)
        {
            AddTotalSlotShift(nCurShiftSlot);
        }

        m_nUtcIndirectMode = 0;
    }

    if (m_dUtcIndirectTick) {
        --m_dUtcIndirectTick;
    }

    m_pRxModemA->ProcessRxDataAllInINTR(wRxAfAdcDataA);
    m_pRxModemB->ProcessRxDataAllInINTR(wRxAfAdcDataB);

    ProcessTxInINTR();
}

void  cAisModem::ProcessTxInINTR(void)
{
    if(m_bRunTxBuffReset)
    {
        m_pAisTxGmskDATA->vGmskTxTail = m_pAisTxGmskDATA->vGmskTxHead;
        m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;
        m_bRunTxBuffReset = FALSE;
    }

    if (m_pAisTxGmskDATA->vGmskTxTail == m_pAisTxGmskDATA->vGmskTxHead)
    {
        CLayerPhysical::getInst()->TurnTxOff(FALSE);
    }

    if (m_pAisTxGmskDATA->vGmskTxTail != m_pAisTxGmskDATA->vGmskTxHead)
    {
        if(CAisLib::GetOneFrameDiffSlotID(m_nSlotNoCounter, m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo) == 1)
        {
            //-------------------------------
            // 다음 송신 준비
            //-------------------------------
            //if(m_nSampleCounter == 0)
            {
                if (m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxFreqVal)
                {
                    DEBUG_LOG("MODEM-T] freq %d -> %d M:%d C:%d\r\n",
                        m_pAisTxGmskDATA->nTxPrevFreq, m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxFreqVal,
                        m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxMsgID, m_nSlotNoCounter);

                    CLayerPhysical::getInst()->ReserveTxFreq(m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxFreqVal);
                }
            }
        }
        else if (IsAisTestModeInfiniteTx() ||
                m_nSlotNoCounter == m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo ||
                m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize >= 0)
        {
            if(m_nSlotNoCounter == m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo)
            {
                if(TX_START_INTERNAL_SHIFT <= m_nSampleCounter && m_nSampleCounter <= TX_START_INTERNAL_SHIFT+10)
                {
                    CLayerPhysical::getInst()->PrepToTransmitStep2();
                }

                #define  RAMPUP_SAMPLECNT_END    (TX_START_INTERNAL_SHIFT+RAMPUP_TIME_SAMPLECNT+10)    // end of Ramp up timing sample count
                if((TX_START_INTERNAL_SHIFT+1) <= m_nSampleCounter && m_nSampleCounter <= RAMPUP_SAMPLECNT_END)
                {
                    CLayerPhysical::getInst()->PrepToTransmitStep3();
                }
            }

            if(m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize == AIS_GMSK_TX_DATA_MODE_EMPTY)
            {
                m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize = AIS_GMSK_TX_DATA_MODE_WAITING;

                DEBUG_LOG("MODEM-T] S %d,%d CH:%d, S:%d(%d,%d), M:%d len:%d, %d\r\n",
                    m_pAisTxGmskDATA->vGmskTxHead, m_pAisTxGmskDATA->vGmskTxTail,
                    m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxChnlNo,
                    m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo, m_nSlotNoCounter, m_nSampleCounter,
                    m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxMsgID,
                    m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nGmskAdSize,
                    cTimerSys::getInst()->GetCurTimerSec());
            }
            if (m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize >= AIS_GMSK_TX_DATA_MODE_WAITING)
            {
                if(m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize >= 0)
                {
                    cDac::getInst()->SetDAC0Data(m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].pGmskDaData[m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize]);
                }

                ++m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize;
                if (m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize >= m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nGmskAdSize)
                {
                    if(IsAisTestModeInfiniteTx())
                    {
                        DEBUG_LOG("MODEM-T] Infinite restart %d, %d\r\n",
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize, m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nGmskAdSize);

                        m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize = 0;
                    }
                    else
                    {
                        DEBUG_LOG("MODEM-T] E %d,%d CH:%d, S:%d(%d,%d), M:%d txSize:%d, gmskSize:%d, %d\r\n",
                            m_pAisTxGmskDATA->vGmskTxHead, m_pAisTxGmskDATA->vGmskTxTail,
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxChnlNo,
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo, m_nSlotNoCounter, m_nSampleCounter,
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxMsgID,
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize, m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nGmskAdSize,
                            cTimerSys::getInst()->GetCurTimerSec());

                        cDac::getInst()->SetDAC0Data(m_vTxGmskCnterDAC[0]);

                        m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSendSize = AIS_GMSK_TX_DATA_MODE_EMPTY;

                    #ifdef __RFTEST_JIG_MODE__
                        // to transmit message infinitely

                        if(m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxChnlNo == CLayerNetwork::getInst()->GetChPrimary()->m_uChNumTx)
                        {
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo += gnSlotTermRI_Ch1;
                        }
                        else if(m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxChnlNo == CLayerNetwork::getInst()->GetChSecondary()->m_uChNumTx)
                        {
                            m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo += gnSlotTermRI_Ch2;
                        }
                        m_pAisTxGmskDATA->vGmskTxData[m_pAisTxGmskDATA->vGmskTxTail].nTxSlotNo %= NUM_SLOT_PER_FRAME;

                        ++m_pAisTxGmskDATA->vGmskTxTail;
                        if (m_pAisTxGmskDATA->vGmskTxTail >= gnRfTestTxBuffCnt)
                            m_pAisTxGmskDATA->vGmskTxTail = 0;
                    #else
                        ++m_pAisTxGmskDATA->vGmskTxTail;
                        if (m_pAisTxGmskDATA->vGmskTxTail >= AIS_GMSK_TX_BUFF_SIZE)
                            m_pAisTxGmskDATA->vGmskTxTail = 0;
                    #endif

                        CLayerPhysical::getInst()->TurnTxOff(TRUE);
                    }
                }
            }
        }
    }
}

BOOL  cAisModem::RunSyncToOtherSt(UINT uSyncSrcMMSI, xAisRxRawForm *pAisRxRawForm, UINT16 wRcvSubMsg, int nDistDelaySampCntr)
{
    int  nTempX = 0;
    int  nTempY = 0;
    int  nTempZ = 0;
    BOOL bRet = FALSE;

    if (m_dUtcIndirectTick == 0)
    {
        const int FILTER_DELAY_BITS = 0;
        const int SAMPLES_BEFORE_DATAPART = (AIS_SLOT_RF_RAMP_UP_BITS_LEN + AIS_SLOT_PREAMBLE_BITS_LEN + AIS_SLOT_STARTFLAG_BITS_LEN + FILTER_DELAY_BITS) * AIS_DATA_SAMPLES_PER_ONE_BIT;

        nTempX = pAisRxRawForm->dSlotNoCounter * AIS_SAMPLE_COUNTER_MAX_VAL + pAisRxRawForm->dSampleCounter - SAMPLES_BEFORE_DATAPART - nDistDelaySampCntr;
        nTempY = wRcvSubMsg * AIS_SAMPLE_COUNTER_MAX_VAL;
        nTempZ = nTempY - nTempX;

        if (abs(nTempZ) > 2)
        {
            if (nTempZ > 0)
                m_nUtcIndirectMode = 1;
            else
            {
                nTempZ = -nTempZ;
                m_nUtcIndirectMode = -1;
            }

            m_nUtcIndirectSlot = nTempZ / AIS_SAMPLE_COUNTER_MAX_VAL;
            m_nUtcIndirectSamp = nTempZ % AIS_SAMPLE_COUNTER_MAX_VAL;

            m_dUtcIndirectTick = ONE_SEC_TICK * 2;
            bRet = TRUE;

            DEBUG_LOG("ExtSync] Sync,Run,OK, src:%09d, utcIndTick:%d, rxSlot:%d, sampCnt:%d, delay:%d, subMsg:%d, X:%d, Y:%d, Z:%d, Slot:%d, Samp:%d, Mode:%d, s:%d\r\n",
                uSyncSrcMMSI, m_dUtcIndirectTick, pAisRxRawForm->dSlotNoCounter, pAisRxRawForm->dSampleCounter, nDistDelaySampCntr, wRcvSubMsg,
                nTempX, nTempY, nTempZ, m_nUtcIndirectSlot, m_nUtcIndirectSamp, m_nUtcIndirectMode, cTimerSys::getInst()->GetCurTimerSec());
        }
        else
        {
            DEBUG_LOG("ExtSync] Sync,Run,fail, tempZ error, src:%09d, utcIndTick:%d, rxSlot:%d, sampCnt:%d, delay:%d, subMsg:%d, X:%d, Y:%d, Z:%d, Slot:%d, Samp:%d, Mode:%d, s:%d\r\n",
                uSyncSrcMMSI, m_dUtcIndirectTick, pAisRxRawForm->dSlotNoCounter, pAisRxRawForm->dSampleCounter, nDistDelaySampCntr, wRcvSubMsg,
                nTempX, nTempY, nTempZ, m_nUtcIndirectSlot, m_nUtcIndirectSamp, m_nUtcIndirectMode, cTimerSys::getInst()->GetCurTimerSec());
        }
    }
    else
    {
        DEBUG_LOG("ExtSync] Sync,Run,fail, m_dUtcIndirectTick error, src:%09d, utcIndTick:%d, rxSlot:%d, sampCnt:%d, delay:%d, subMsg:%d, X:%d, Y:%d, Z:%d, Slot:%d, Samp:%d, Mode:%d, s:%d\r\n",
            uSyncSrcMMSI, m_dUtcIndirectTick, pAisRxRawForm->dSlotNoCounter, pAisRxRawForm->dSampleCounter, nDistDelaySampCntr, wRcvSubMsg,
            nTempX, nTempY, nTempZ, m_nUtcIndirectSlot, m_nUtcIndirectSamp, m_nUtcIndirectMode, cTimerSys::getInst()->GetCurTimerSec());
    }

    return bRet;
}

//============================================================================
void  cAisModem::SetAisTxFrequency(int nFrequency)
{
    if(m_pAisTxGmskDATA->nTxPrevFreq != nFrequency)
    {
        cPllSYS::getInstPLL1()->SetCh2Frequency(nFrequency);

        m_pAisTxGmskDATA->nTxPrevFreq = nFrequency;
    }
}

int  cAisModem::GetAisTxFrequency()
{
    return cPllSYS::getInstPLL1()->GetCh1Frequency();
}

BOOL cAisModem::CheckTxFreqFixed()
{
    return (cPllSYS::getInstPLL1()->GetCh1Frequency() == m_pAisTxGmskDATA->nTxPrevFreq);
}

HWORD cAisModem::IncCurrentFrameNo(void)
{
      ++m_nCurrentFrameNo;
      if (m_nCurrentFrameNo >= NUM_FRAME_FRAMEMAP)
          m_nCurrentFrameNo = 0;

      return(m_nCurrentFrameNo);
}

HWORD cAisModem::DecCurrentFrameNo(void)
{
    --m_nCurrentFrameNo;
    if (m_nCurrentFrameNo < 0)
        m_nCurrentFrameNo = NUM_FRAME_FRAMEMAP-1;

    return(m_nCurrentFrameNo);
}

void cAisModem::SetInfinteTxMode(BOOL bOn)
{
    m_bInfiniteTxMode = bOn;
}

void cAisModem::ResetAisMsgTx()
{
    m_bRunTxBuffReset = TRUE;
}
