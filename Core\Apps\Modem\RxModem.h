/**
 * @file    RxModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __RXMODEM_H__
#define  __RXMODEM_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AisMsg.h"
#include "GmskLib.h"

//=============================================================================
#define  DC_MIN_LEVEL_A                 (( 100.0f / 3300))  // 0.100 Volt
#define  DC_MID_LEVEL_A                 ((1600.0f / 3300))  // 1.600 Volt
#define  DC_MAX_LEVEL_A                 ((2200.0f / 3300))  // 2.700 Volt
//-----------------------------------------------------------------------------
#define  DC_MIN_LEVEL_B                 (( 100.0f / 3300))  // 0.100 Volt
#define  DC_MID_LEVEL_B                 ((1600.0f / 3300))  // 1.700 Volt
#define  DC_MAX_LEVEL_B                 ((2200.0f / 3300))  // 2.700 Volt
//=============================================================================

//=============================================================================
#define  RX_SW_PLL_FULL_VALUE           (2400)
#define  RX_SW_PLL_HALF_VALUE           (RX_SW_PLL_FULL_VALUE / 2)
#define  RX_SW_PLL_INCR_VALUE           (RX_SW_PLL_FULL_VALUE / AIS_DATA_SAMPLES_PER_ONE_BIT)
#define  RX_SW_PLL_STEP_VALUE           (RX_SW_PLL_INCR_VALUE / 3)
//=============================================================================

//=============================================================================
#define  RX_MDM_STATUS_PREAMBLE         (0)
#define  RX_MDM_STATUS_START            (1)
#define  RX_MDM_STATUS_PRELOAD          (2)
#define  RX_MDM_STATUS_DATA             (3)
//===========================================================================
#define  RX_RAW_FORM_BUFF_SIZE          (37)
//===========================================================================
#define  RX_PREAMBLE_LEN                (132)	    // 132
#define  RX_SYNC_THRESHOLD              (0.3844)    // 0.62*0.62 Preamble max corrolation threshold
#define  RX_SYNC_STABLE_CNT             (20)        // Stable max corrolation count
//============================================================================
#define  RX_DOT_MAX_CNT_SIZE            (7)
#define  RX_DOT_MAX_CNT_MASK            (0x007f)
#define  RX_DOT_START_P_MASK            (0x0005)
#define  RX_DOT_DETCT_P_MASK            (0x0055)
#define  RX_DOT_MAX_CNT_LAST            (RX_DOT_MAX_CNT_SIZE - 1)
//============================================================================

//============================================================================
#define  RX_PRE_MAX_CNT_SIZE            (12)
#define  RX_PRE_MAX_BUF_SIZE            (RX_PRE_MAX_CNT_SIZE * AIS_DATA_SAMPLES_PER_ONE_BIT)
//============================================================================

//============================================================================
//#define  __UTC_DIRECT_TEST_ENABLED__
//============================================================================

//============================================================================
typedef  struct _xDotDATA {
    HWORD  wDotPattern;
    HWORD  wDotChanged;
    HWORD  wDotCountX[RX_DOT_MAX_CNT_SIZE];
} xDotDATA;
//============================================================================

//============================================================================
typedef  struct _xPreDATA {
    float  vData[RX_PRE_MAX_BUF_SIZE];
    int    nPntX;
    float  fSumX;
    DWORD  dCntX;
    float  fAvrX;
} xPreDATA;
//============================================================================

class cRxModem
{
public:
    cRxModem(int nRxChannelNo, float fRxReferMinVal, float fRxReferMidVal, float fRxReferMaxVal);
    virtual ~cRxModem(void);

    static std::shared_ptr<cRxModem> getInstRxA() {
        static std::shared_ptr<cRxModem> pInstRxA = std::make_shared<cRxModem>( AIS_CHANNEL_AIS1, 
                                                                                DC_MIN_LEVEL_A, 
                                                                                DC_MID_LEVEL_A, 
                                                                                DC_MAX_LEVEL_A
                                                                                );
        return pInstRxA;
    }

    static std::shared_ptr<cRxModem> getInstRxB() {
        static std::shared_ptr<cRxModem> pInstRxB = std::make_shared<cRxModem>( AIS_CHANNEL_AIS2, 
                                                                                DC_MIN_LEVEL_B, 
                                                                                DC_MID_LEVEL_B, 
                                                                                DC_MAX_LEVEL_B
                                                                                );
        return pInstRxB;
    }

public:
    void  ClearPreambleBuff(void);
    void  ClearFullDotData(void);
    void  ClearFullPreData(void);
    void  ClearRxRawBuff(void);
    void  ClearRxRawFormTemp(void);

    float RunGmskFilter(float fInAdc);
    BOOL  RunDetectPreamble(float fInAdc);
    int   ProcessRxDataAllInINTR(HWORD wRxAfAdcData);
    int   ProcessRxDataSwPllRun(void);
    int   ProcessRxDataCommonRun(void);

    void  ProcessRxStatusPreamble(void);
    void  ResetToRxStatusPreamble(void);
    void  BeginRxStatusStart(void);

    xAisRxRawForm *GetFullPacketFromRxRawBuff(void);
    void  WritePacketIntoRxRawBuff(void);
    void  PutDataIntoRxRawBuff(UCHAR bRxData);
    void  AppendOnePreData(float fData);

protected:
    volatile int    m_nRxChannelNo;
    volatile float  m_fRxReferMinVal;
    volatile float  m_fRxReferMidVal;
    volatile float  m_fRxReferMaxVal;

#if defined(RX_GMSK_FILTER_USE_MODE)
    volatile float m_pRxRawGmskBuff[RX_GMSK_BT_0_5_FIR_N];
#endif

    volatile DWORD  m_dSwRxPllValue;
    volatile DWORD  m_dSwRxPllSampC;
    volatile DWORD  m_dSwRxPllSampP;
    volatile DWORD  m_dSwRxPllCntrX;

    volatile HWORD  m_wBitSamplCntr;
    volatile HWORD  m_wBitSamplBitD;
    volatile HWORD  m_wBitSamplTggl;

    volatile float  m_fRxReferValue;

    volatile HWORD  m_wRxNrziPrev;
    volatile HWORD  m_wRxNrziCurr;
    volatile HWORD  m_wRxNrziTemp;
    volatile HWORD  m_wRxNrziCntr;

    volatile float  m_fRxAfAdcSumVal;
    volatile DWORD  m_dRxAfAdcCntVal;

    volatile HWORD  m_wRxRunStatus;
    volatile HWORD  m_wRxBitCount;
    volatile HWORD  m_wRxShiftReg;

    volatile HWORD  m_wReceivingID;
    volatile HWORD  m_wRxMaxBitSize;

    volatile HWORD  m_wRxPrevBitD;
    volatile HWORD  m_wRxCurrBitD;

    volatile HWORD  m_wNewBitData;
    volatile HWORD  m_wCrcRegData;
    volatile UCHAR  m_bRxByteData;

    volatile xAisRxRawForm *m_vRxRawFormBuff;
    volatile int    m_nRxRawFormHead;
    volatile int    m_nRxRawFormTail;
    volatile int    m_nRxRawFormTemp;

    volatile float  m_fRxAfAdcRawX;
    volatile float  m_fRxAfAdcData;

    volatile DWORD  m_dRxAdcErrCnt;

    volatile float  m_fRxNormAdc;
    volatile float  *m_vRxNormBuff;
    volatile HWORD  m_dRxNormBuffIdx;
    volatile BOOL   m_bPreambleDetected;
    volatile float  m_fMaxCorr;
    volatile DWORD  m_dMaxCorrIdx;
    volatile DWORD  m_dMaxElapseCount;
    volatile float  m_fRxSyncPowSum;
    volatile float  m_fMaxCorrValue;

    volatile DWORD  m_dSampleCounter;
    volatile DWORD  m_dSlotNoCounter;

    volatile xDotDATA m_xDotData;
    volatile xPreDATA m_xPreData;
};

#endif /*__RXMODEM_H__*/

