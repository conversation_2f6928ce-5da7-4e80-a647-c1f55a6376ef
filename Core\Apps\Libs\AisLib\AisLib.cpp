/**
 * @file    AisLib.c
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <ctype.h>
#include "DataType.h"
#include "SysConst.h"
#include "AllConst.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "SysLib.h"
#include "SysLog.h"
#include "AisMsg.h"
#include "Ship.h"
#include "SensorMgr.h"
#include "RosMgr.h"
#include "UserDirMgr.h"
#include "LayerNetwork.h"
#include "ReportRateMgr.h"
#include "AisLib.h"

//============================================================================
static UCHAR     G_vBitMaskA[8]  = {0x01,0x02,0x04,0x08,0x10,0x20,0x40,0x80};
//============================================================================
static xCHNLFREQ G_vChFreqData[] = {{2087 ,161975000}, {2088 ,162025000}, {  75 ,156775000}, {  76 ,156825000}, {1060 ,156025000}, {1260 ,156037500}, {1001 ,156050000}, {1201 ,156062500}, {1061 ,156075000}, {1261 ,156087500},
                                    {1002 ,156100000}, {1202 ,156112500}, {1062 ,156125000}, {1262 ,156137500},
                                    {1003 ,156150000}, {1203 ,156162500}, {1063 ,156175000}, {1263 ,156187500}, {1004 ,156200000}, {1204 ,156212500}, {1064 ,156225000}, {1264 ,156237500}, {1005 ,156250000}, {1205 ,156262500},
                                    {1065 ,156275000}, {1265 ,156287500}, {   6 ,156300000}, {1206 ,156312500}, {1066 ,156325000}, {1266 ,156337500}, {1007 ,156350000}, {1207 ,156362500}, {  67 ,156375000}, { 267 ,156387500},
                                    {   8 ,156400000}, { 208 ,156412500}, {  68 ,156425000}, { 268 ,156437500}, {   9 ,156450000}, { 209 ,156462500}, {  69 ,156475000}, { 269 ,156487500}, {  10 ,156500000}, { 210 ,156512500},
                                    {  70 ,156525000}, { 270 ,156537500}, {  11 ,156550000}, { 211 ,156562500}, {  71 ,156575000}, { 271 ,156587500}, {  12 ,156600000}, { 212 ,156612500}, {  72 ,156625000}, { 272 ,156637500},
                                    {  13 ,156650000}, { 213 ,156662500}, {  73 ,156675000}, { 273 ,156687500}, {  14 ,156700000}, { 214 ,156712500}, {  74 ,156725000}, { 274 ,156737500}, {  15 ,156750000}, { 215 ,156762500},
                                    {1075 ,156775000}, {1076 ,156825000}, { 275 ,156787500}, { 216 ,156812500}, { 276 ,156837500}, {  17 ,156850000}, { 217 ,156862500}, {  77 ,156875000}, { 277 ,156887500}, {1018 ,156900000},
                                    {1218 ,156912500}, {1078 ,156925000}, {1278 ,156937500}, {1019 ,156950000}, {1219 ,156962500}, {1079 ,156975000}, {1279 ,156987500}, {1020 ,157000000}, {1220 ,157012500}, {1080 ,157025000},
                                    {1280 ,157037500}, {1021 ,157050000}, {1221 ,157062500}, {1081 ,157075000}, {1281 ,157087500}, {1022 ,157100000}, {1222 ,157112500}, {1082 ,157125000}, {1282 ,157137500}, {1023 ,157150000},
                                    {1223 ,157162500}, {1083 ,157175000}, {1283 ,157187500}, {1024 ,157200000}, {1224 ,157212500}, {1084 ,157225000}, {1284 ,157237500}, {1025 ,157250000}, {1225 ,157262500}, {1085 ,157275000},
                                    {1285 ,157287500}, {1026 ,157300000}, {1226 ,157312500}, {1086 ,157325000}, {1286 ,157337500}, {1027 ,157350000}, {1227 ,157362500}, {1087 ,157375000}, {1028 ,157400000}, {1228 ,157412500},
                                    {1088 ,157425000}, {1287 ,158387500}, {2060 ,160625000}, {2260 ,160637500}, {2001 ,160650000}, {2201 ,160662500}, {2061 ,160675000}, {2261 ,160687500}, {2002 ,160700000}, {2202 ,160712500},
                                    {2062 ,160725000}, {2262 ,160737500}, {2003 ,160750000}, {2203 ,160762500}, {2063 ,160775000}, {2263 ,160787500}, {2004 ,160800000}, {2204 ,160812500}, {2064 ,160825000}, {2264 ,160837500},
                                    {2005 ,160850000}, {2205 ,160862500}, {2065 ,160875000}, {2265 ,160887500}, {2206 ,160912500}, {2066 ,160925000}, {2266 ,160937500}, {2007 ,160950000}, {2207 ,160962500}, {2018 ,161500000},
                                    {2218 ,161512500}, {2078 ,161525000}, {2278 ,161537500}, {2019 ,161550000}, {2219 ,161562500}, {2079 ,161575000}, {2279 ,161587500}, {2020 ,161600000}, {2220 ,161612500}, {2080 ,161625000},
                                    {2280 ,161637500}, {2021 ,161650000}, {2221 ,161662500}, {2081 ,161675000}, {2281 ,161687500}, {2022 ,161700000}, {2222 ,161712500}, {2082 ,161725000}, {2282 ,161737500}, {2023 ,161750000},
                                    {2223 ,161762500}, {2083 ,161775000}, {2283 ,161787500}, {2024 ,161800000}, {2224 ,161812500}, {2084 ,161825000}, {2284 ,161837500}, {2025 ,161850000}, {2225 ,161862500}, {2085 ,161875000},
                                    {2285 ,161887500}, {2026 ,161900000}, {2226 ,161912500}, {2086 ,161925000}, {2286 ,161937500}, {2027 ,161950000}, {2227 ,161962500}, {2287 ,161987500}, {2028 ,162000000}, {2228 ,162012500}};
//============================================================================

//============================================================================
int   CAisLib::GetDaysInMonth(int nYear, int nMonth)
{
      static UCHAR vDaysInMonth[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

      if (nMonth < 1)
          return 0;

      if (nMonth != 2)
          return(vDaysInMonth[nMonth - 1]);
      else
      {
          if ((nYear % 400) == 0) return(29);
          if ((nYear % 100) == 0) return(28);
          if ((nYear %   4) == 0) return(29);
      }

      return(28);
}

int   CAisLib::GetDaysByDate(int nYear, int nMonth, int nDay)
{
    int i, nTotalDays;

    nTotalDays = nDay;

    for (i = 1; i < nMonth; i++)
        nTotalDays += GetDaysInMonth(nYear, i);

    return(nTotalDays);
}

int CAisLib::GetDiffTimeSeconds(SYS_DATE_TIME *pCurrTime, SYS_DATE_TIME *pPastTime)
{
    if(!pCurrTime->nValid || !pPastTime->nValid)
        return 0;

    if(!IsValidAisSysDateTime(pCurrTime) || !IsValidAisSysDateTime(pPastTime))
        return 0;

    int  nYearC    = pCurrTime->xDate.nYear + (DTTM_BASE_YEAR - 1);
    int  nYearP    = pPastTime->xDate.nYear + (DTTM_BASE_YEAR - 1);
    int  nLeapdays = (nYearC / 4 - nYearP / 4) - (nYearC / 100 - nYearP / 100) + (nYearC / 400 - nYearP / 400);
    int  nYears    = nYearC - nYearP;

    int  nDays     = 365 * nYears + nLeapdays + (GetDaysByDate(pCurrTime->xDate.nYear, pCurrTime->xDate.nMon, pCurrTime->xDate.nDay)
                                            -  GetDaysByDate(pPastTime->xDate.nYear, pPastTime->xDate.nMon, pPastTime->xDate.nDay));

    int  nHours    = 24 * nDays    + (pCurrTime->xTime.nHour - pPastTime->xTime.nHour);
    int  nMinutes  = 60 * nHours   + (pCurrTime->xTime.nMin  - pPastTime->xTime.nMin);
    int  nSeconds  = 60 * nMinutes + (pCurrTime->xTime.nSec  - pPastTime->xTime.nSec);

    return(nSeconds);
}

//============================================================================
UCHAR CAisLib::ConvertAsc06ToAscii(UCHAR bAsc06)
{
    if (bAsc06 >= 0x20 && bAsc06 <= 0x3F)
        return(bAsc06);

    if (bAsc06 <= 0x1F)
        return (bAsc06 + 0x40);

    return(0x00);
}

UCHAR CAisLib::ConvertBin06ToAscii(UCHAR bBin06)
{
    if (bBin06 < 0x28)
        bBin06 += 0x30;
    else
        bBin06 += 0x38;

    return(bBin06);
}

UCHAR CAisLib::ConvertAsciiToBin06(UCHAR bAscii)
{
    UCHAR nSixBit = bAscii;
    if (bAscii >= 0x40 && bAscii <= 0x5f)
        nSixBit = bAscii - 0x40;

    return (nSixBit & 0x3f);
}

DWORD CAisLib::GetAisMsgFieldINT(UCHAR *pAisMsg, int nStartBit, int nNumOfBits)
{
    DWORD dResult = 0x00000000;
    int   nStartByte, nStartPos;
    DWORD dBitVal;
    UCHAR bMaskBit;
    int   i;

    nStartByte = nStartBit / 8;
    nStartPos  = nStartBit % 8;
    bMaskBit   = G_vBitMaskA[7 - nStartPos];
    pAisMsg   += nStartByte;

    for (i = 0;i < nNumOfBits; i++) {
        dBitVal = (*pAisMsg & bMaskBit) ? 1 : 0;
        dResult|= (dBitVal << (nNumOfBits - i - 1));
        bMaskBit= bMaskBit >> 1;
        if (bMaskBit == 0x00) {
            bMaskBit = 0x80;
            ++pAisMsg;
        }
    }

    return(dResult);
}

int   CAisLib::GetAisMsgFieldSTR(UCHAR *pAisMsg, char *pString, int nStartBit, int nNumOfBits)
{
    int   i;
    int   nStrLen = nNumOfBits / 6;
    UCHAR bCode;

    for (i = 0;i < nStrLen; i++) {
        bCode = (UCHAR)(GetAisMsgFieldINT(pAisMsg, nStartBit, 6));
        *pString++ = (char)ConvertAsc06ToAscii(bCode);
        nStartBit += 6;
    }

    *pString = 0x00;

    return(nStrLen);
}
//------------------------------------------------------------------------
int   CAisLib::MakeBin08PacketToAsc06Str(UCHAR *pBin08Str, int nBin08Len, UCHAR *pAsc06Str, HWORD *pAsc06Len)
{
    int   i, j;
    UCHAR bChar8bit, bChar6bit = 0;
    int   nBitCount;
    HWORD wFillBits;

    // Encode packet into 6-bit string
    nBitCount  = 0;
    *pAsc06Len = 0;

    for (i= 0; i < nBin08Len; i++) {
        bChar8bit = *pBin08Str++;

        for (j = 0; j < 8; j++) {
            if (bChar8bit & 0x80)
                bChar6bit |= 0x01;
            else
                bChar6bit &= 0xfe;
            if (++nBitCount == 6) {
                nBitCount  = 0;
                bChar6bit &= 0x3f;

                *pAsc06Str++ = ConvertBin06ToAscii(bChar6bit);
                bChar6bit  = 0x00;;
                ++*pAsc06Len;
            }
            else {
                bChar6bit <<= 1;
            }

            bChar8bit <<= 1;
        }
    }

    if (nBitCount != 0) {                           // Add any fill bits required to the last character
        wFillBits = 6 - nBitCount;                  // Fill bits required
        bChar6bit <<= (wFillBits-1);
        bChar6bit &= 0x3f;

        *pAsc06Str++ = ConvertBin06ToAscii(bChar6bit);
        ++*pAsc06Len;
    }
    else {
        wFillBits = 0;
    }

    *pAsc06Str = 0x00;

    return(wFillBits);
}

//----------------------------------------------------------------------------
int   CAisLib::MakeValidAisAsciiString(char *pAsciiStr, int nAsciiLen)
{
    if(strlen(pAsciiStr) < nAsciiLen)
        memset(&pAsciiStr[strlen(pAsciiStr)], '@', nAsciiLen-strlen(pAsciiStr));
    return nAsciiLen;
}

char xtod(char c)
{
    return (c>='0' && c<='9') ? c-'0' : ((c>='A' && c<='F') ? c-'A'+10 : ((c>='a' && c<='f') ? c-'a'+10 : 0));
}

BOOL CAisLib::ConvertNormalString(const char *pszText, char *pszBuf, int nBuffSize)
{
    int  nLen = strlen(pszText);
    int  nPos = 0, i = 0;
    char ch;

    memset(pszBuf, 0x00, nBuffSize);

    while(i < nLen)
    {
        if(pszText[i] == '^') {
            ch  = xtod(pszText[i+1]) * 16;
            ch += xtod(pszText[i+2]);
            pszBuf[nPos] = ch;
            ++nPos;
            i += 3;
        }
        else {
            pszBuf[nPos] = pszText[i];
            ++nPos;
            ++i;
        }
    }
    pszBuf[nPos] = '\0';
    if(i > nLen)
        return FALSE;
    return TRUE;
}

BOOL CAisLib::IsNMEAReservedCharater(char ch)
{
    if( ch == '\x0d' ) return TRUE;
    if( ch == '\x0a' ) return TRUE;
    if( ch == '$' )    return TRUE;
    if( ch == '*' )    return TRUE;
    if( ch == ',' )    return TRUE;
    if( ch == '!' )    return TRUE;
    if( ch == '\\' )   return TRUE;
    if( ch == '^' )    return TRUE;
    if( ch == '~' )    return TRUE;
    if( ch == '\x7f' ) return TRUE;

    return FALSE;
}

char* CAisLib::ConvertNMEAString(const char *pszText)
{
    static char szBuf[BUFSIZ];
    char szHex[8];
    int  nLen = strlen(pszText);
    int  nPos = 0;

    szBuf[0] = '\0';

    for(int i = 0; i < nLen; ++i) {
        if(IsNMEAReservedCharater(pszText[i])) {
            sprintf(szHex, "^%02X", pszText[i]);
            strcpy(szBuf+nPos, szHex);
            nPos += strlen(szHex);
        }
        else {
            szBuf[nPos++] = pszText[i];
        }
    }

    szBuf[nPos] = '\0';
    return szBuf;
}

//------------------------------------------------------------------------
void  CAisLib::SetAisTxMsgFieldPack(UCHAR *pAisMsg, int nStartBit, int nBitWidth, DWORD dData)
{
    int   nStartByte, nStartPos;
    DWORD dMaskData;
    UCHAR bMaskBitX;
    int   i;

    dMaskData  = 0x00000001 << (nBitWidth - 1);

    nStartByte = nStartBit / 8;
    nStartPos  = nStartBit % 8;
    bMaskBitX  = G_vBitMaskA[7 - nStartPos];
    pAisMsg   += nStartByte;

    for (i = 0; i < nBitWidth; i++)
    {
        if (dMaskData & dData)
            *pAisMsg |= bMaskBitX;

        dMaskData = dMaskData >> 1;
        bMaskBitX = bMaskBitX >> 1;

        if (bMaskBitX == 0x00)
        {
            bMaskBitX = 0x80;
            ++pAisMsg;
        }
    }
}

void  CAisLib::SetAisTxMsgFieldPackY(UCHAR *pAisMsg, int nStartBit, int nBitWidth, UCHAR *pData)
{
    int   nStartByte, nStartPos;
    UCHAR bMaskBitD;
    UCHAR bMaskBitX;
    int   i;

    bMaskBitD  = 0x80;

    nStartByte = nStartBit / 8;
    nStartPos  = nStartBit % 8;
    bMaskBitX  = G_vBitMaskA[7 - nStartPos];
    pAisMsg   += nStartByte;

    for (i = 0; i < nBitWidth; i++) {
        if (bMaskBitD & *pData)
            *pAisMsg |= bMaskBitX;

        bMaskBitD = bMaskBitD >> 1;
        if (bMaskBitD == 0x00) {
            bMaskBitD = 0x80;
            ++pData;
        }

        bMaskBitX = bMaskBitX >> 1;
        if (bMaskBitX == 0x00) {
            bMaskBitX = 0x80;
            ++pAisMsg;
        }
    }
}

void  CAisLib::SetAisTxMsgFieldPackStr(UCHAR *pAisMsg, int nStartBit, int nBitWidth, char *pData, int nLenStr)
{
    int   nStartByte, nStartPos;
    UCHAR bMaskBitD;
    UCHAR bMaskBitX;
    int   i;

    int nX = strlen(pData);
    if(nX < nLenStr)
        memset(&pData[strlen(pData)], '@', nLenStr-nX);

    bMaskBitD  = 0x80;

    nStartByte = nStartBit / 8;
    nStartPos  = nStartBit % 8;
    bMaskBitX  = G_vBitMaskA[7 - nStartPos];
    pAisMsg   += nStartByte;

    BYTE bDataByte = *pData << 2;

    for (i = 0; i < nBitWidth; i++)
    {
        if (bMaskBitD & bDataByte)
            *pAisMsg |= bMaskBitX;

        bMaskBitD = bMaskBitD >> 1;
//        if (bMaskBitD == 0x00)
        if (bMaskBitD == 0x02)
        {
            bMaskBitD = 0x80;
            ++pData;
            bDataByte = *pData << 2;
        }

        bMaskBitX = bMaskBitX >> 1;
        if (bMaskBitX == 0x00)
        {
            bMaskBitX = 0x80;
            ++pAisMsg;
        }
    }
}
//------------------------------------------------------------------------
void  CAisLib::SetAisTxMsgFieldDataX(UCHAR *pAisMsg, int nStartBit, int nBitWidth, DWORD dData)
{
      DWORD dMaskData;
      int   i;

      dMaskData = 0x00000001 << (nBitWidth - 1);
      pAisMsg  += nStartBit;

      for (i = 0; i < nBitWidth; i++)
      {
           if (dMaskData & dData)
               *pAisMsg++ = 0x01;
           else
               *pAisMsg++ = 0x00;

           dMaskData = dMaskData >> 1;
      }
}
void  CAisLib::SetAisTxMsgFieldDataY(UCHAR *pAisMsg, int nStartBit, int nBitWidth, UCHAR *pData)
{
      UCHAR bMaskBitD;
      int   i;

      bMaskBitD = 0x80;
      pAisMsg  += nStartBit;

      for (i = 0; i < nBitWidth; i++)
      {
           if (bMaskBitD & *pData)
               *pAisMsg++ = 0x01;
           else
               *pAisMsg++ = 0x00;

           bMaskBitD = bMaskBitD >> 1;
           if (bMaskBitD == 0x00)
           {
               bMaskBitD = 0x80;
               ++pData;
           }
      }
}
//------------------------------------------------------------------------
void  CAisLib::ConvertAisTxDataToPack(UCHAR *pTxPack, UCHAR *pTxData, int nBitWidth)
{
      UCHAR bMaskBitX;
      int   i;

      memset(pTxPack, 0x00, nBitWidth / 8);

      bMaskBitX = 0x80;

      for (i = 0; i < nBitWidth; i++)
      {
           if (*pTxData++)
               *pTxPack |= bMaskBitX;

           bMaskBitX = bMaskBitX >> 1;
           if (bMaskBitX == 0x00)
           {
               bMaskBitX = 0x80;
               ++pTxPack;
           }
      }
}
int CAisLib::IsValidAisFloatPOS(POS_FLOAT *pPosF)
{
    if (pPosF->fLAT == AIS_FLOAT_LAT_NULL_VAL || pPosF->fLON == AIS_FLOAT_LON_NULL_VAL)
        return 0;

    if (pPosF->fLAT <  AIS_FLOAT_LAT_MIN_VAL  ||  pPosF->fLAT >  AIS_FLOAT_LAT_MAX_VAL)
        return 0;

    if (pPosF->fLON <  AIS_FLOAT_LON_MIN_VAL  ||  pPosF->fLON >= AIS_FLOAT_LON_MAX_VAL)
        return 0;

    return 1;
}
int CAisLib::IsValidAisGridPOS(POS_GRID *pPosG)
{
      if (pPosG->nLAT == AIS_GRID_LAT_NULL_VAL || pPosG->nLON == AIS_GRID_LON_NULL_VAL)
          return 0;

      if (pPosG->nLAT <  AIS_GRID_LAT_MIN_VAL  ||  pPosG->nLAT >  AIS_GRID_LAT_MAX_VAL)
          return 0;

      if (pPosG->nLON <  AIS_GRID_LON_MIN_VAL  ||  pPosG->nLON >= AIS_GRID_LON_MAX_VAL)
          return 0;

      return 1;
}
int CAisLib::IsValidAisHighPOS(POS_HIGH *pPosHigh)
{
      if (pPosHigh->nLAT == AIS_FULL_LAT_NULL_VAL || pPosHigh->nLON == AIS_FULL_LON_NULL_VAL)
          return 0;

      if (pPosHigh->nLAT <  AIS_FULL_LAT_MIN_VAL  ||  pPosHigh->nLAT >  AIS_FULL_LAT_MAX_VAL)
          return 0;

      if (pPosHigh->nLON <  AIS_FULL_LON_MIN_VAL  ||  pPosHigh->nLON >  AIS_FULL_LON_MAX_VAL)
          return 0;

      return 1;
}
int CAisLib::IsValidAisLowPOS(POS_LOW *pPosLow)
{
      if (pPosLow->nLAT == AIS_HALF_LAT_NULL_VAL || pPosLow->nLON == AIS_HALF_LON_NULL_VAL)
          return 0;

      if (pPosLow->nLAT <  AIS_HALF_LAT_MIN_VAL  ||  pPosLow->nLAT >  AIS_HALF_LAT_MAX_VAL)
          return 0;

      if (pPosLow->nLON <  AIS_HALF_LON_MIN_VAL  ||  pPosLow->nLON >  AIS_HALF_LON_MAX_VAL)
          return 0;

      return 1;
}
//----------------------------------------------------------------------------
AISFF CAisLib::AisGridLatToHigh(LGRID nData)
{
      if (nData == AIS_GRID_LAT_NULL_VAL)
          return(AIS_FULL_LAT_NULL_VAL);

      return(nData / MUL_FACTOR_FULL_AIS_TO_GRID);
}
AISFF CAisLib::AisGridLonToHigh(LGRID nData)
{
    if (nData == AIS_GRID_LON_NULL_VAL)
        return(AIS_FULL_LON_NULL_VAL);

    if (nData > GRID_ABS_COOR_180)
        nData = nData - GRID_ABS_COOR_360;

    return(nData / MUL_FACTOR_FULL_AIS_TO_GRID);
}
LGRID CAisLib::AisHighPosLatToGrid(AISFF nData)
{
    if (nData == AIS_FULL_LAT_NULL_VAL)
        return(AIS_GRID_LAT_NULL_VAL);

    return(nData * MUL_FACTOR_FULL_AIS_TO_GRID);
}
LGRID CAisLib::AisHighPosLonToGrid(AISFF nData)
{
    if (nData == AIS_FULL_LON_NULL_VAL)
        return(AIS_GRID_LON_NULL_VAL);

    nData = nData * MUL_FACTOR_FULL_AIS_TO_GRID;
    if (nData < -GRID_ABS_COOR_180)
        nData = nData + GRID_ABS_COOR_360;

    return(nData);
}
//------------------------------------------------------------------------
AISFF CAisLib::AisGridLatToLowLat(LGRID nData)
{
    if (nData == AIS_GRID_LAT_NULL_VAL)
        return(AIS_HALF_LAT_NULL_VAL);

    return(nData / MUL_FACTOR_HALF_AIS_TO_GRID);
}
AISFF CAisLib::AisGridLonToLowLon(LGRID nData)
{
    if (nData == AIS_GRID_LON_NULL_VAL)
        return(AIS_HALF_LON_NULL_VAL);

    if (nData > GRID_ABS_COOR_180)
        nData = nData - GRID_ABS_COOR_360;

    return(nData / MUL_FACTOR_HALF_AIS_TO_GRID);
}
LGRID CAisLib::AisLowLatToGridLat(AISFF nData)
{
    if (nData == AIS_HALF_LAT_NULL_VAL)
        return(AIS_GRID_LAT_NULL_VAL);

    return(nData * MUL_FACTOR_HALF_AIS_TO_GRID);
}
LGRID CAisLib::AisLowPosLonToGrid(AISFF nData)
{
    if (nData == AIS_HALF_LON_NULL_VAL)
        return(AIS_GRID_LON_NULL_VAL);

    nData = nData * MUL_FACTOR_HALF_AIS_TO_GRID;

    if (nData < -GRID_ABS_COOR_180)
        nData = nData + GRID_ABS_COOR_360;

    return(nData);
}

//------------------------------------------------------------------------
void  CAisLib::AisFullPosToFLOAT(POS_HIGH *pHighPos, POS_FLOAT *pFloatPos)
{
      if (pHighPos->nLAT == AIS_FULL_LAT_NULL_VAL)
          pFloatPos->fLAT = AIS_FLOAT_LAT_NULL_VAL;
      else
          pFloatPos->fLAT = (FLOAT)pHighPos->nLAT / (FLOAT)AIS_HIGH_SCALE;

      if (pHighPos->nLON == AIS_FULL_LON_NULL_VAL)
          pFloatPos->fLON = AIS_FLOAT_LON_NULL_VAL;
      else
          pFloatPos->fLON = (FLOAT)pHighPos->nLON / (FLOAT)AIS_HIGH_SCALE;
}
void  CAisLib::AisFullPosToGRID(POS_HIGH *pHighPos, POS_GRID *pGridPos)
{
      if (pHighPos->nLAT == AIS_FULL_LAT_NULL_VAL)
          pGridPos->nLAT = AIS_GRID_LAT_NULL_VAL;
      else
          pGridPos->nLAT = AisHighPosLatToGrid(pHighPos->nLAT);

      if (pHighPos->nLON == AIS_FULL_LON_NULL_VAL)
          pGridPos->nLON = AIS_GRID_LON_NULL_VAL;
      else
          pGridPos->nLON = AisHighPosLonToGrid(pHighPos->nLON);
}
//------------------------------------------------------------------------
void  CAisLib::AisLowPosToFLOAT(POS_LOW *pLowPos, POS_FLOAT *pFloatPos)
{
      if (pLowPos->nLAT == AIS_HALF_LAT_NULL_VAL)
          pFloatPos->fLAT = AIS_FLOAT_LAT_NULL_VAL;
      else
          pFloatPos->fLAT = (FLOAT)pLowPos->nLAT / (FLOAT)AIS_POS_LOW_SCALE;

      if (pLowPos->nLON == AIS_HALF_LON_NULL_VAL)
          pFloatPos->fLON = AIS_FLOAT_LON_NULL_VAL;
      else
          pFloatPos->fLON = (FLOAT)pLowPos->nLON / (FLOAT)AIS_POS_LOW_SCALE;
}
void  CAisLib::AisLowPosToGRID(POS_LOW *pLowPos, POS_GRID *pGridPos)
{
    if (pLowPos->nLAT == AIS_HALF_LAT_NULL_VAL)
        pGridPos->nLAT = AIS_GRID_LAT_NULL_VAL;
    else
        pGridPos->nLAT = AisLowLatToGridLat(pLowPos->nLAT);

    if (pLowPos->nLON == AIS_HALF_LON_NULL_VAL)
        pGridPos->nLON = AIS_GRID_LON_NULL_VAL;
    else
        pGridPos->nLON = AisLowPosLonToGrid(pLowPos->nLON);
}
//----------------------------------------------------------------------------
void  CAisLib::AisFullPosToLow(POS_HIGH *pHighPos, POS_LOW *pLowPos)
{
    const int SCALE_HIGH_TO_LOW = AIS_HIGH_SCALE / AIS_POS_LOW_SCALE;

    if (pHighPos->nLAT == AIS_FULL_LAT_NULL_VAL)
        pLowPos->nLAT = AIS_LOW_LAT_NULL_VAL;
    else
        pLowPos->nLAT = pHighPos->nLAT / SCALE_HIGH_TO_LOW;

    if (pHighPos->nLON == AIS_FULL_LON_NULL_VAL)
        pLowPos->nLON = AIS_LOW_LON_NULL_VAL;
    else
        pLowPos->nLON = pHighPos->nLON / SCALE_HIGH_TO_LOW;
}

//----------------------------------------------------------------------------
int   CAisLib::GetAisHighPosLatByMsgField(int nAisFullLat)
{
    if (nAisFullLat & 0x04000000)
        nAisFullLat = -(0x08000000 - nAisFullLat);

    return(nAisFullLat);
}

int   CAisLib::GetAisHighPosLonByMsgField(int nAisFullLon)
{
    if (nAisFullLon & 0x08000000)
        nAisFullLon = -(0x10000000 - nAisFullLon);

    return(nAisFullLon);
}

int   CAisLib::GetAisHalfLatDataByAisLAT(int nAisHalfLat)
{
    if (nAisHalfLat & 0x00010000)
        nAisHalfLat = -(0x00020000 - nAisHalfLat);

    return(nAisHalfLat);
}

int   CAisLib::GetAisHalfLonDataByAisLON(int nAisHalfLon)
{
    if (nAisHalfLon & 0x00020000)
        nAisHalfLon = -(0x00040000 - nAisHalfLon);

    return(nAisHalfLon);
}
//------------------------------------------------------------------------
void  CAisLib::GetAisHighPosDataByMsg(POS_HIGH *pHighPos)
{
    pHighPos->nLAT = GetAisHighPosLatByMsgField(pHighPos->nLAT);
    pHighPos->nLON = GetAisHighPosLonByMsgField(pHighPos->nLON);

    if ((pHighPos->nLAT >= +( 90 * AIS_HIGH_SCALE)) || (pHighPos->nLAT <= -( 90 * AIS_HIGH_SCALE)) ||
        (pHighPos->nLON >  +(180 * AIS_HIGH_SCALE)) || (pHighPos->nLON <  -(180 * AIS_HIGH_SCALE)))
    {
        pHighPos->nLAT = AIS_FULL_LAT_NULL_VAL;
        pHighPos->nLON = AIS_FULL_LON_NULL_VAL;
    }
}
void  CAisLib::GetAisLowPosDataByMsg(POS_LOW *pLowPos)
{
    pLowPos->nLAT = GetAisHalfLatDataByAisLAT(pLowPos->nLAT);
    pLowPos->nLON = GetAisHalfLonDataByAisLON(pLowPos->nLON);

    if ((pLowPos->nLAT >= +( 90 * AIS_POS_LOW_SCALE)) || (pLowPos->nLAT <= -( 90 * AIS_POS_LOW_SCALE)) ||
        (pLowPos->nLON >  +(180 * AIS_POS_LOW_SCALE)) || (pLowPos->nLON <  -(180 * AIS_POS_LOW_SCALE)))
    {
        pLowPos->nLAT = AIS_HALF_LAT_NULL_VAL;
        pLowPos->nLON = AIS_HALF_LON_NULL_VAL;
    }
}

//========================================================================
/**
 * @brief Check if latitude is valid
 * @param rLat Latitude to check
 * @return Valid latitude
 */
LREAL CAisLib::LatToLat(LREAL rLat)
{
    rLat = MAX(rLat, -90);
    rLat = MIN(rLat, 90);
    return rLat;
}

/**
 * @brief Check if longitude is valid
 * @param rLon Longitude to check
 * @return Valid longitude
 */
LREAL CAisLib::LonToLon(LREAL rLon)
{
    if(rLon < -180)
        return (rLon + 180);
    else if(rLon > 180)
        return (rLon - 180);
    return rLon;
}

//------------------------------------------------------------------------
void  CAisLib::CalcFullPosByFULL(POS_ALLF *pFullPos)
{
    AisFullPosToFLOAT(&pFullPos->xPosH, &pFullPos->xPosF);
    AisFullPosToGRID (&pFullPos->xPosH, &pFullPos->xPosG);
}

void  CAisLib::CalcFullPosByFLOAT(POS_ALLF *pFullPos)
{
    pFullPos->xPosG.nLAT = CGps::RealLatToGrid(pFullPos->xPosF.fLAT);
    pFullPos->xPosG.nLON = CGps::RealLonToGrid(pFullPos->xPosF.fLON);

    pFullPos->xPosH.nLAT = AisGridLatToHigh(pFullPos->xPosG.nLAT);
    pFullPos->xPosH.nLON = AisGridLonToHigh(pFullPos->xPosG.nLON);
}

//------------------------------------------------------------------------
void  CAisLib::CalcFullPosByLow(POS_ALLH *pAllPosH)
{
    AisLowPosToFLOAT(&pAllPosH->xPosL, &pAllPosH->xPosF);
    AisLowPosToGRID (&pAllPosH->xPosL, &pAllPosH->xPosG);
}

void  CAisLib::CalcLowPosByGRID(POS_ALLH *pAllPosH)
{
    pAllPosH->xPosL.nLAT = AisGridLatToLowLat(pAllPosH->xPosG.nLAT);
    pAllPosH->xPosL.nLON = AisGridLonToLowLon(pAllPosH->xPosG.nLON);

    pAllPosH->xPosF.fLAT = CGps::GridLatToReal(pAllPosH->xPosG.nLAT);
    pAllPosH->xPosF.fLON = CGps::GridLonToReal(pAllPosH->xPosG.nLON);
}

void  CAisLib::CalcGridLowPosByFLOAT(POS_ALLH *pAllPosH)
{
    pAllPosH->xPosG.nLAT = CGps::RealLatToGrid(pAllPosH->xPosF.fLAT);
    pAllPosH->xPosG.nLON = CGps::RealLonToGrid(pAllPosH->xPosF.fLON);

    pAllPosH->xPosL.nLAT = AisGridLatToLowLat(pAllPosH->xPosG.nLAT);
    pAllPosH->xPosL.nLON = AisGridLonToLowLon(pAllPosH->xPosG.nLON);
}

//------------------------------------------------------------------------
void  CAisLib::ClearFullPosToNULL(POS_ALLF *pFullPos)
{
    pFullPos->xPosH.nLAT = AIS_FULL_LAT_NULL_VAL;
    pFullPos->xPosH.nLON = AIS_FULL_LON_NULL_VAL;

    CalcFullPosByFULL(pFullPos);
}

void  CAisLib::ClearHalfPosToNULL(POS_ALLH *pAllPosH)
{
    pAllPosH->xPosL.nLAT = AIS_HALF_LAT_NULL_VAL;
    pAllPosH->xPosL.nLON = AIS_HALF_LON_NULL_VAL;

    CalcFullPosByLow(pAllPosH);
}

void CAisLib::CalcGrfPosByReal(REAL rLat, REAL rLon, POS_GRFP *psShipPos)
{
    psShipPos->xPosR.rLAT = rLat;
    psShipPos->xPosR.rLON = rLon;

    psShipPos->xPosG.nLAT = CGps::RealLatToGrid(rLat);
    psShipPos->xPosG.nLON = CGps::RealLonToGrid(rLon);

    psShipPos->xPosH.nLAT = AisGridLatToHigh(psShipPos->xPosG.nLAT);
    psShipPos->xPosH.nLON = AisGridLonToHigh(psShipPos->xPosG.nLON);

    psShipPos->xPosF.fLAT = CGps::GridLatToReal(psShipPos->xPosG.nLAT);
    psShipPos->xPosF.fLON = CGps::GridLonToReal(psShipPos->xPosG.nLON);
}
//========================================================================

//========================================================================
DWORD CAisLib::GetAisFreqByChannelNo(HWORD wChNo)
{
    int  i;

    for (i = 0; i < (sizeof(G_vChFreqData) / sizeof(G_vChFreqData[0])); i++)
         if (G_vChFreqData[i].wChNo == wChNo)
             return(G_vChFreqData[i].dFreq);

    return 0;
}

HWORD CAisLib::GetAisChannelNoByFreq(DWORD dFreq)
{
    for (int i = 0; i < (sizeof(G_vChFreqData) / sizeof(G_vChFreqData[0])); i++)
         if (G_vChFreqData[i].dFreq == dFreq)
             return(G_vChFreqData[i].wChNo);

    return 0;
}

int CAisLib::IsAisChanneFreqValidByCH(HWORD wChNo)
{
    return(IsAisChanneFreqValidByFR(GetAisFreqByChannelNo(wChNo)));
}

int CAisLib::IsAisChanneFreqValidByFR(DWORD dFreq)
{
    if (dFreq >= 161500000 && dFreq <= 162025000)
        return 1;

    return 0;
}

int CAisLib::IsAisChanneBW25ValidByCH(HWORD wChNo)
{
    return(IsAisChanneBW25ValidByFR(GetAisFreqByChannelNo(wChNo)));
}

int CAisLib::IsAisChanneBW25ValidByFR(DWORD dFreq)
{
    if (IsAisChanneFreqValidByFR(dFreq) && ((dFreq / 25000 * 25000) == dFreq))
        return 1;

    return 0;
}

//============================================================================
int CAisLib::IsValidMMSI_BaseSt(DWORD dMMSI)
{
    return (2000000 <= dMMSI && dMMSI <= 7999999);
}

int CAisLib::IsValidMMSI_MobileSt(DWORD dMMSI)
{
    // AIS-SART/MOB/EPIRB 는 모바일스테이션에 포함되지 않는다!
    return (200000000 <= dMMSI && dMMSI <= 799999999) || (982000000 <= dMMSI && dMMSI <= 987999999);
}

int CAisLib::IsValidMMSI_SAR(DWORD dMMSI)
{
    return (111000000 <= dMMSI && dMMSI <= 111999999);
}

int CAisLib::IsValidMMSI_AtoN(DWORD dMMSI)
{
    return (990000000 <= dMMSI && dMMSI <= 999996999);
}

int CAisLib::IsValidMMSI(DWORD dMMSI)
{
    return (dMMSI == AIS_AB_MMSI_NULL || IsValidMMSI_MobileSt(dMMSI));
}

int CAisLib::IsValidMMSI_SART(DWORD dMMSI)
{
    return ((DWORD)MMSI_MIN_SART <= dMMSI && dMMSI <= (DWORD)MMSI_MAX_SART);
}

BOOL CAisLib::IsValidImoNum(UINT uImoNum)
{
    return (AIS_IMOID_VALID_MIN <= uImoNum && uImoNum <= AIS_IMOID_VALID_MAX) || (AIS_IMOID_VALID_OFFICIAL_MIN <= uImoNum && uImoNum <= AIS_IMOID_VALID_OFFICIAL_MAX);
}

BOOL CAisLib::IsValidImo(UINT uImoNum)
{
    return (uImoNum == AIS_IMOID_NAN || IsValidImoNum(uImoNum));
}

BOOL CAisLib::IsShipTypeTanker(int nShipType)
{
    return (BOOL)((nShipType / 10) == 8);            // tanker ship : 8x
}

DWORD CAisLib::GetDimensionOfShip(xANTPOS *pAntPos, xANTPOS *pExtPos)
{
    DWORD dA, dB, dC, dD;

    dA = (pAntPos->wA + pExtPos->wA);
    if (dA > AIS_GNSS_ANT_POS_A_MAX)
        dA = AIS_GNSS_ANT_POS_A_MAX;

    dB = (pAntPos->wB + pExtPos->wB);
    if (dB > AIS_GNSS_ANT_POS_B_MAX)
        dB = AIS_GNSS_ANT_POS_B_MAX;

    dC = (pAntPos->wC + pExtPos->wC);
    if (dC > AIS_GNSS_ANT_POS_C_MAX)
        dC = AIS_GNSS_ANT_POS_C_MAX;

    dD = (pAntPos->wD + pExtPos->wD);
    if (dD > AIS_GNSS_ANT_POS_D_MAX)
        dD = AIS_GNSS_ANT_POS_D_MAX;

    return((dA << 21) | (dB << 12) | (dC << 6) | (dD));
}

int CAisLib::SetETAData(ETA_DATA *eta)
{
    int a, b, c, d;

    a = (eta->nMonth & 0x0F);
    b = (eta->nDay   & 0x1F);
    c = (eta->nHour  & 0x1F);
    d = (eta->nMin   & 0x3F);

    return (a << 16 | b << 11 | c << 6 | d);
}

//========================================================================
UINT16 CAisLib::GetScheduledPosReportMsgID(BYTE bOpMode)
{
    UINT16 uMsgID;
    switch(bOpMode)
    {
    case OPMODE_ASSIGNED_RR:
    case OPMODE_ASSIGNED_SLOT:
        uMsgID = AIS_MSG_NO_ID_02;
        break;
    case OPMODE_CONTINUOUS:
        uMsgID = CReportRateMgr::getInst()->IsReportRateForSOTDMA() ? AIS_MSG_NO_ID_01 : AIS_MSG_NO_ID_03;
        break;
    default:
        uMsgID = AIS_MSG_NO_ID_UNDEFINED;
        break;
    }
    return uMsgID;
}

int CAisLib::GetNIfromRR(float fReportRate)
{
    if(fReportRate >= 0)
        return Round(NUM_SLOT_PER_FRAME / fReportRate);
    return 0;
}

int CAisLib::GetNIfromRI(float fReportIntSec)
{
    if(fReportIntSec >= 0)
        return Round(fReportIntSec / 60 * NUM_SLOT_PER_FRAME);
    return 0;
}

BOOL CAisLib::IsValidTxPower(BYTE tx_power)
{
    return (tx_power == AIS_TX_POWER_HIGH || tx_power == AIS_TX_POWER_LOW);
}

BOOL CAisLib::IsValidBandwidth(WORD wBandwidth)
{
    return (wBandwidth == CH_BW_25KHZ);
}

BOOL CAisLib::IsOwnShipTypeBelongsToAssigned(int nShipType)
{
    if(!nShipType)
        return TRUE;

    int nFirstDigit     = nShipType / 10;
    int nSecondDigit    = nShipType % 10;
    uint16_t ShipType   = cShip::getOwnShipInst()->xNavData.uShipType;
    int nOwnFirstDigit  = ShipType / 10;

    if(!nSecondDigit)
    {
        if(nFirstDigit != 5)
            return (nFirstDigit == nOwnFirstDigit);
    }
    return (nShipType == cShip::getOwnShipInst()->xNavData.uShipType);
}

int CAisLib::GetAisRxMsgMaxBitSizeByMsgID(int nMsgID)
{
    //                             0,   1,   2,   3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,  14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25,  26,  27,  28,  29,  30,  31
    static int  vMaxBitSize[] ={1064, 168, 168, 168, 168, 424,1008, 168,1008, 168,  72, 168,1008, 168,1008, 160, 144, 816, 168, 312, 160, 360, 168, 160, 168, 168,1064,  96, 1064, 1064, 1064, 1064,
                                1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064};

    return(vMaxBitSize[nMsgID] + 16 + 2);
}

int CAisLib::GetRandomValueOneBySEED(int nStartVal, int nLastVal)
{
    DWORD dSeedVal;
    DWORD dSysTick= SysGetSystemTimer();
    DWORD dwTmrCnt= SysGetFreeRunTimerCount();

    int nKey     = cShip::getOwnShipInst()->GetOwnShipMMSI();
    int nCurrMIN = cShip::getOwnShipInst()->xSysTime.xTime.nMin;
    int nCurrSEC = cShip::getOwnShipInst()->xSysTime.xTime.nSec;

    DWORD dTempX, dTempY;
    UCHAR *pX = (UCHAR *)&dTempX;
    UCHAR *pY = (UCHAR *)&dTempY;

    pX[0] = (UCHAR)nCurrSEC ^ 0xff;
    pX[1] = (UCHAR)nCurrMIN ^ 0xff;
    pX[2] = (UCHAR)nCurrMIN;
    pX[3] = (UCHAR)nCurrSEC;

    pY[0] = pX[3] ^ 0x55;
    pY[1] = pX[2];
    pY[2] = pX[1];
    pY[3] = pX[0] ^ 0xaa;

    int nRange = nLastVal - nStartVal + 1;
    dSeedVal = nKey ^ dSysTick ^ dTempX ^ dTempY ^ dwTmrCnt;
#if 0
    srand(dSeedVal);

    int nTempX = rand() % nRange + nStartVal;
#else
    int nTempX = /*rand()*/ dSeedVal % nRange + nStartVal;
#endif

    return(nTempX);
}

int CAisLib::GetRandomSlotTimeOut()
{
    static int nOldTmo = 0;
    int nRandTmo;

    nRandTmo = GetRandomValueOneBySEED(TMO_MIN, TMO_MAX);
    if(nRandTmo == nOldTmo)                                    // to avoid selecting the same value as before
    {
        if(++nRandTmo > TMO_MAX)
            nRandTmo = TMO_MIN;
    }

    nOldTmo = nRandTmo;
    return nRandTmo;
}
//============================================================================
int CAisLib::GetAssignedRRby10minRR(int nOffset, int nMinutes)
{
    //-------------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex2. 3.3.6 - Calculate Assign Rr by Msg 16 RR assignment
    // When number of reports per 10 min are assigned, only multiples of 20 between 20 and 600 should be used.
    // If a mobile station received a value which is not a multiple of 20 but below 600.
    // it should use the next higher multiple of 20.
    // If a mobile station receives a value greater than 600 is should use 600.
    //-------------------------------------------------------------------------------------------------------
    int  nRR;

    if (nOffset < 20)
        return -1;

    nOffset = MIN(nOffset, 600);

    if ((nOffset % 20) == 0)
        nRR = nOffset / nMinutes;
    else
        nRR = ((nOffset / 20) + 1) * 20 / nMinutes;        // 20의 배수로 만들기
    return nRR;
}

float CAisLib::GetAssignedRRbySlotInc(int nInc)
{
    // -----------------------------------------------
    // Calculate Assign Rr by Msg 16 slot increment
    // -----------------------------------------------
    return ((float)NUM_SLOT_PER_FRAME / nInc);
}

//============================================================================
int CAisLib::IsValidAisSysDate(int nYear, int nMon, int nDay)
{
    if (nYear == DTTM_YEAR_NULL ||
        nMon  == DTTM_MONTH_NULL ||
        nDay  == DTTM_DAY_NULL)
        return 0;

    if (nYear < DTTM_YEAR_MIN || nYear > DTTM_YEAR_MAX ||
        nMon  <    1 || nMon  >   12 ||
        nDay  <    1 || nDay  >   31)
        return 0;

    return 1;
}

int CAisLib::IsValidAisSysDate(SYS_DATE *pSysTime)
{
    return IsValidAisSysDate(pSysTime->nYear, pSysTime->nMon, pSysTime->nDay);
}

int CAisLib::IsValidAisSysTime(int nHour, int nMin, int nSec)
{
    return (nHour >=  0 && nHour <= 23 &&      // 0 -- 23
            nMin  >=  0 && nMin  <= 59 &&      // 0 -- 59
            nSec  >=  0 && nSec  <= 59);       // 0 -- 59
}

int CAisLib::IsValidAisSysTime(SYS_TIME *pSysTime)
{
    return IsValidAisSysTime(pSysTime->nHour, pSysTime->nMin, pSysTime->nSec);
}

int CAisLib::IsValidAisSysDateTime(SYS_DATE_TIME *pSysTime)
{
    return pSysTime->nValid && IsValidAisSysDate(&(pSysTime->xDate)) && IsValidAisSysTime(&(pSysTime->xTime));
}

int CAisLib::IsValidETATime(ETA_DATA *pEta)
{
    if (pEta->nMonth< ETA_MONTH_MIN || pEta->nMonth > ETA_MONTH_MAX ||
        pEta->nDay  < ETA_DAY_MIN   || pEta->nDay   > ETA_DAY_MAX ||
        pEta->nHour < ETA_HOUR_MIN  || pEta->nHour  > ETA_HOUR_MAX ||
        pEta->nMin  < ETA_MIN_MIN   || pEta->nMin   > ETA_MIN_MAX ||
        pEta->nSec  < ETA_SEC_MIN   || pEta->nSec   > ETA_SEC_MAX)
        return 0;

    return 1;
}

void CAisLib::ResetDefaultSysTime(SYS_DATE_TIME *sys_time)
{
    //----------------------------------------------------
    // - 근거: M.1371-1, Message 4(Base Station Report)
    //----------------------------------------------------
    sys_time->xDate.nYear  = DTTM_YEAR_NULL;
    sys_time->xDate.nMon   = DTTM_MONTH_NULL;
    sys_time->xDate.nDay   = DTTM_DAY_NULL;

    sys_time->xTime.nHour  = DTTM_HOUR_NULL;
    sys_time->xTime.nMin   = DTTM_MIN_NULL;
    sys_time->xTime.nSec   = DTTM_SEC_NULL;

    sys_time->nValid =  FALSE;
}

void CAisLib::SetDefaultSysDate(SYS_DATE *pSysDate)
{
    pSysDate->nYear = DTTM_YEAR_NULL;
    pSysDate->nMon  = DTTM_MONTH_NULL;
    pSysDate->nDay  = DTTM_DAY_NULL;
}

void CAisLib::SetDefaultSysTime(SYS_TIME *pSysTime)
{
    pSysTime->nHour = DTTM_HOUR_NULL;
    pSysTime->nMin  = DTTM_MIN_NULL;
    pSysTime->nSec  = DTTM_SEC_NULL;
}

void CAisLib::SetDefaultSysDateTime(SYS_DATE_TIME *pSysDateTime)
{
    SetDefaultSysDate(&(pSysDateTime->xDate));
    SetDefaultSysTime(&(pSysDateTime->xTime));
    pSysDateTime->nValid = MODE_VAL_OFF;
}

BOOL CAisLib::GetSysDateTime(SYS_DATE_TIME *pDateTime, SYS_DATE *pSrcDate, SYS_TIME *pSrcTime)
{
    pDateTime->xDate = *pSrcDate;
    pDateTime->xTime = *pSrcTime;
    pDateTime->nValid = IsValidAisSysDate(&(pDateTime->xDate)) && IsValidAisSysTime(&(pDateTime->xTime));
    return pDateTime->nValid;
}

BOOL CAisLib::IsTimeStampValid(int nTimeStamp)
{
    return (0 <= nTimeStamp && nTimeStamp < 60);
}

//============================================================================
void CAisLib::FTOA(double x,        /* the number to be converted */
          int f,        /* number of digits to follow decimal point */
          char *str)    /* output string */
{
    //---------------------------------------------------------------
    //* - 프로그램상에서 제공되지 않는 ftoa() 함수를 만들어서 사용함.
    //---------------------------------------------------------------
    double scale;       /* scale factor */
    int i,              /* copy of f, and # digits before decimal point */
        d;              /* a digit */

    if( x < 0.0 ) {
        *str++ = '-' ;
        x = -x ;
    }
    i = f ;
    scale = 2.0 ;
    while ( i-- )
        scale *= 10.0 ;
    x += 1.0 / scale ;
    /* count places before decimal & scale the number */
    i = 0 ;
    scale = 1.0 ;
    while ( x >= scale ) {
        scale *= 10.0 ;
        i++ ;
    }
    if ( i == 0 )
        *str++ = '0';

    while ( i-- ) {
        /* output digits before decimal */
        scale = floor(0.5 + scale * 0.1 ) ;
        d = ( x / scale ) ;
        *str++ = d + '0' ;
        x -= (double)d * scale ;
    }
    if ( f <= 0 ) {
        *str = 0;
        return ;
    }
    *str++ = '.' ;
    while ( f-- ) {
        /* output digits after decimal */
        x *= 10.0 ;
        d = x;
        *str++ = d + '0' ;
        x -= d ;
    }
    *str = 0;
}

WORD CAisLib::FrameMapSlotIdAdd(INT16 nFrSlotID1, INT16 nFrSlotID2)
{
    //-----------------------------------------------------------------------------------------------------------------------
    // nSlotID : NUM_SLOT_FRAMEMAP (NUM_SLOT_PER_FRAME * NUM_FRAME_FRAMEMAP) 개의 슬롯을 관리하는 전체 슬롯맵에서의 slot ID
    //-----------------------------------------------------------------------------------------------------------------------
    int nSlotID = (int)nFrSlotID1;
    int nInc = (int)nFrSlotID2;

    nSlotID += nInc;
    if(abs(nSlotID) >= NUM_SLOT_FRAMEMAP)
        nSlotID %= NUM_SLOT_FRAMEMAP;
    if(nSlotID < 0)
        nSlotID += NUM_SLOT_FRAMEMAP;

    return (WORD)nSlotID;
}

INT16 CAisLib::GetFrameMapSlotID(INT16 nFrameID, INT16 nSlotID)
{
    //-----------------------------------------------------------------------------------------------------------------
    // nSlotID : 프레임맵의 nFrameID 번째 프레임 상의 slot ID
    // 출력 : 프레임맵의 nFrameID 번째 프레임 상의 slot ID 를 전체 슬롯맵에서의 슬롯 ID 로 계산한 값
    //-----------------------------------------------------------------------------------------------------------------
    return (nFrameID * NUM_SLOT_PER_FRAME + nSlotID) % NUM_SLOT_FRAMEMAP;
}

DWORD CAisLib::GetDiffDword(DWORD dwOldValue, DWORD dwNewValue, DWORD dwMaxValue)
{
    if (dwNewValue >= dwOldValue)
        return (dwNewValue - dwOldValue);
    return ((dwMaxValue - dwOldValue) + dwNewValue + 1);
}

DWORD CAisLib::GetDiffDwordMax(DWORD dwOldValue, DWORD dwNewValue)
{
    return GetDiffDword(dwOldValue, dwNewValue, (DWORD)0xFFFFFFFF);
}

WORD CAisLib::GetOneFrameDiffSlotID(WORD wOldSlotID, WORD wNewSlotID)
{
    return GetDiffDword((DWORD)wOldSlotID, (DWORD)wNewSlotID, (DWORD)SLOTID_MAX);
}

WORD CAisLib::FrameMapGetDiffSlotID(WORD wOldSlotID, WORD wNewSlotID)
{
    return GetDiffDword((DWORD)wOldSlotID, (DWORD)wNewSlotID, (DWORD)MAX_SLOT_FRAMEMAP);
}

int CAisLib::GetFrMapElapTimeSlot(int nOldSlotID, int nNewSlotID)
{
    int nDiffSlot = FrameMapGetDiffSlotID(nOldSlotID, nNewSlotID);
    if(nDiffSlot > MAX_SLOT_FRAMEMAP_HALF)
        nDiffSlot = FrameMapGetDiffSlotID(nNewSlotID, nOldSlotID);
    return nDiffSlot;
}

/**
 * @brief Calculate number of slots to shift
 * @param nOldFrameID Previous frame ID
 * @param nOldSlotId Previous slot ID
 * @param nNewFrameID Current frame ID
 * @param nNewSlotId Current slot ID
 * @return Slot shift
 */
int CAisLib::GetSlotShift(int nOldFrameID, int nOldSlotId, int nNewFrameID, int nNewSlotId)
{
    #define SLOT_SHIFT_THRESHOLD    1

    int nOldFrMapSlotID = GetFrameMapSlotID(nOldFrameID, nOldSlotId);
    int nNewFrMapSlotID = GetFrameMapSlotID(nNewFrameID, nNewSlotId);

    int nDiffSlot1 = FrameMapGetDiffSlotID(nOldFrMapSlotID, nNewFrMapSlotID);
    int nDiffSlot2 = FrameMapGetDiffSlotID(nNewFrMapSlotID, nOldFrMapSlotID);
    int nShiftSlot = nDiffSlot1;
    if(nDiffSlot2 < nDiffSlot1)
        nShiftSlot = -nDiffSlot2;

    if(abs(nShiftSlot) <= SLOT_SHIFT_THRESHOLD)
        nShiftSlot = 0;

    return nShiftSlot;
}

int CAisLib::GetNumSlotFromSec(int nElapSec)
{
    return (int)(nElapSec * 37.5f);
}

BOOL CAisLib::CheckValidSlotID(WORD wSlotID)
{
    return (SLOTID_MIN <= wSlotID && wSlotID <= SLOTID_MAX);
}

BOOL CAisLib::CheckValidFrameMapSlotID(WORD wSlotID)
{
    return (0 <= wSlotID && wSlotID <= MAX_SLOT_FRAMEMAP);
}

int CAisLib::GetHdgAisFieldData()
{
    if(cShip::getOwnShipInst()->xDynamicData.nHDG == NMEA_HDG_NULL)
        return AIS_HDG_VDL_NULL;
    return cShip::getOwnShipInst()->xDynamicData.nHDG;
}

int CAisLib::GetRotAisFieldData()
{
    //--------------------
    // IEC-61993-2 19.5.8
    //--------------------
    int nROT = cShip::getOwnShipInst()->xDynamicData.nROT;
    int nRotFieldData = AIS_ROT_VDL_NULL;

     if(!CSensorMgr::getInst()->IsRotLost() && nROT != NMEA_ROT_NULL)
    {
        if(CSensorMgr::getInst()->IsRotSensorOtherSrc())
        {
            if(abs(nROT) >= 10 * NMEA_SCALE_ROT)
                nRotFieldData = (nROT > 0 ? AIS_ROT_VDL_BELOW_10DEG_LEFT : AIS_ROT_VDL_ABOVE_10DEG_LEFT);
            else
                nRotFieldData = 0;
        }
        else
        {
            //---------------------------
            // ITU-R-M.1371-5 Annex 8
            // ROT 값 : 4.733 * SQRT(ROT)
            //---------------------------
            #define ROT_DATA_MIN    -(708 * NMEA_SCALE_ROT)
            #define ROT_DATA_MAX    (708 * NMEA_SCALE_ROT)

            if(nROT >= ROT_DATA_MAX)
                nRotFieldData = AIS_ROT_VDL_MAX;
            else if(nROT <= ROT_DATA_MIN)
                nRotFieldData = AIS_ROT_VDL_MIN;
            else
            {
                BOOL bMinus = FALSE;
                if(nROT < 0)
                {
                    nROT = -nROT;
                    bMinus = TRUE;
                }
                nRotFieldData = (int)ceil((4.733 * sqrt((double)nROT / NMEA_SCALE_ROT)));
                if(bMinus)
                    nRotFieldData = -nRotFieldData;

                nRotFieldData = MAX(nRotFieldData, AIS_ROT_VDL_MIN);
                nRotFieldData = MIN(nRotFieldData, AIS_ROT_VDL_MAX);
            }
        }
    }

    return nRotFieldData;
}

int CAisLib::GetRotDataFromAisFieldData(int nRotFieldData)
{
    int nROT = 0;

    if(nRotFieldData == AIS_ROT_VDL_NULL)
        return NMEA_ROT_NULL;

    if(nRotFieldData == AIS_ROT_VDL_BELOW_10DEG_LEFT || nRotFieldData == AIS_ROT_VDL_ABOVE_10DEG_LEFT)
        return nRotFieldData;

    if(nRotFieldData != 0)
    {
        //---------------------------
        // ITU-R-M.1371-5 Annex 8
        // ROT 값 : 4.733 * SQRT(ROT)
        //---------------------------
        BOOL bMinus = FALSE;
        if(nRotFieldData < 0)
        {
            bMinus = TRUE;
            nRotFieldData = -nRotFieldData;
        }
        nROT = (int)(pow(nRotFieldData / 4.733, 2) * NMEA_SCALE_ROT);
        if(bMinus)
            nROT = -nROT;
    }
    return nROT;
}

int CAisLib::MemCmp(BYTE *pSrc, BYTE *pTrt, int nSize)
{
    if(nSize <= 0)
        return -1;

    for(int nCnt = 0 ; nCnt < nSize ; nCnt++)
    {
        if(*pSrc != *pTrt)
            return -1;
        pSrc++;
        pTrt++;
    }
    return 0;
}

float CAisLib::GetDiffYaw(float rDegNew, float rDegOld)
{
    // output : 0 <= output <= 180 or -180 <= output <= 0
    float rTmpDegNew = rDegNew;

    if(rDegNew == NMEA_HDG_NULL || rDegOld == NMEA_HDG_NULL)
        return NMEA_HDG_NULL;

    if(rDegNew >= rDegOld)
    {
        if(rDegNew - rDegOld <= 180)
            return (rDegNew - rDegOld);
        return -(rDegOld + 360 - rDegNew);
    }
    rTmpDegNew += 360;
    if(fabs(rDegNew - rDegOld) < fabs(rTmpDegNew - rDegOld))
        return (rDegNew - rDegOld);
    return (rTmpDegNew - rDegOld);
}

double CAisLib::GetDiffAbsYaw(double rDegNew, double rDegOld)
{
    // always return value >= 0
    double rDiff = GetDiffYaw(rDegNew, rDegOld);
    if(rDiff < 0)
        rDiff = -rDiff;
    return rDiff;
}

BOOL CAisLib::CheckWordDataExisting(WORD *pwList, int nSizeList, WORD wData)
{
    for(int i = 0 ; i < nSizeList ; i++)
    {
        if(pwList[i] == wData)
            return TRUE;
    }
    return FALSE;
}

int CAisLib::HexaStrToData(char *pstrHexa, BYTE *pData, BOOL bCapital)
{
    int nLenData = 0;
    int nLenHexaStr = strlen(pstrHexa);

    for(int i = 0 ; i < nLenHexaStr ; i += 2)
        pData[nLenData++] = (BYTE)HexStrToByte(&pstrHexa[i]);
    return nLenData;
}

BOOL CAisLib::IsValidAisFloatNumStr(char* pstrFloatNum, BOOL bCheckPlus)
{
    int nLen = strlen(pstrFloatNum);
    BOOL bFoundPoint = FALSE;

    for(int i = 0 ; i < nLen ; i++)
    {
        if(pstrFloatNum[i] == '-' || pstrFloatNum[i] == '+')
        {
            if(i > 0 || bCheckPlus)
                return FALSE;
        }
        else if(pstrFloatNum[i] == '.')
        {
            if(bFoundPoint)
                return FALSE;
            bFoundPoint = TRUE;
        }
        else if(pstrFloatNum[i] < '0' || pstrFloatNum[i] > '9')
            return FALSE;
    }
    return TRUE;
}

BOOL CAisLib::IsValidAisIntNumStr(char* pstrFloatNum, BOOL bCheckPlus)
{
    int nLen = strlen(pstrFloatNum);

    for(int i = 0 ; i < nLen ; i++)
    {
        if(pstrFloatNum[i] == '-' && (i > 0 || bCheckPlus))
            return FALSE;
        else if(pstrFloatNum[i] < '0' || pstrFloatNum[i] > '9')
            return FALSE;
    }
    return TRUE;
}

//=========================================================================================
//----------------------------------------------------------------------------------------
// **  seperator 문자로 구분되는 Sub-Data를 얻는다                                  **
// **  수신되어진 field가 Null 이면 sub_data[0]=NULL을 넣어서 return                 **
// **  수신되어진 field가 String(문자열)이면 sub_data[end]=NULL을 넣어서 return     **
//----------------------------------------------------------------------------------------
void CAisLib::GetDataSentence(char *data,        // NMEA sentence, '$','*'사이의 데이터($, * 포함 안됨)
                            int   order,         // 몇번째 데이터를 얻을지.. 0부터 시작됨..
                            char seperator,
                            char *sub_data,    // buffer for sub-data
                            int   nSubSize)    // 읽을 sub_data의 length
{
    int length;
    int i, j;
    int seperator_num = 0;

    memset(sub_data, 0x00, nSubSize);

    if (nSubSize > 1)
        --nSubSize;

    sub_data[nSubSize] = '\0';    // inserted NULL : 입력 Data Length 오류에 대비해서 강제로 마지막 index에 NULL 삽입.

    length = strlen(data);
    if(order == 0)
    {
        for(i = 0 ; i < length ; i++)
        {
            if(data[i] == seperator)
                break;
            if(i < nSubSize)
                sub_data[i] = data[i];
        }

        if(i <= nSubSize)
            sub_data[i] = '\0';    // inserted NULL
    }
    else
    {
        for(i = 0 ; i < length ; i++)
        {
            if(data[i] == seperator)
                seperator_num++;

            if(seperator_num == order)
                break;
        }

        if(i == length)
            return;            // When non-detected, return

        j = 0;
        for(i += 1 ; i < length ; i++)
        {
            if(data[i] == seperator || (seperator != '*' && data[i] == '*'))
                break;

            if(j < nSubSize)
                sub_data[j++] = data[i];
        }

        if(j <= nSubSize)
            sub_data[j] = '\0';    // inserted NULL
    }
}

/******************************************************************************/
BYTE CAisLib::GetROSSourceCharToInfo(char src_char)
{
    switch(src_char)
    {
    case 'A':    return    ROS_SRC_MSG_ADDRD;
    case 'B':    return    ROS_SRC_MSG_BROAD;
    case 'C':    return    ROS_SRC_PI;
    case 'D':    return    ROS_SRC_DSC;
    case 'M':    return    ROS_SRC_MKD;
    }
    return ROS_SRC_DFLT;
}

/******************************************************************************/
char CAisLib::GetROSSourceInfoToChar(BYTE ros_src)
{
    switch(ros_src)
    {
    case ROS_SRC_MSG_ADDRD: return 'A';
    case ROS_SRC_MSG_BROAD: return 'B';
    case ROS_SRC_PI:        return 'C';
    case ROS_SRC_DSC:       return 'D';
    case ROS_SRC_MKD:       return 'M';
    }
    return '0';
}

//----------------------------------------------------------------------------------------
// * - "도" 단위로 저장된 위도와 경도를 "도분.하위분(dddmm.mm)"으로 변환
// * - ACA와 LR Sentnece 만들때 사용됨.
//----------------------------------------------------------------------------------------
void CAisLib::ConvertPosDataToDMM(int pos_data, UINT *pos_deg, UINT *pos_min, UINT *pos_sub_min, int unit)
{
    double temp;
    double dpos_data;

    dpos_data = AIS_TO_POS(pos_data);

    if(dpos_data < 0)
        dpos_data *= -1;    // 양수화

    *pos_deg = (UINT)dpos_data;
    temp     = (dpos_data - *pos_deg)*60*unit;    // 소수점 이하를 60*10000으로 곱하고, 네자리수까지 올림.
    temp     += 0.5;

    *pos_min     = temp/unit;
    *pos_sub_min = (UINT)(temp)%unit;
}

//----------------------------------------------------------------------------------------
//* - 지정한 년도의 첫날 부터 지정한 월의 지정한 날 까지의 날짜 수를 계산.
//----------------------------------------------------------------------------------------
int CAisLib::GetDaysFromYearFirst(int day, int month, int year)
{
    int i, sum_days;

    sum_days = day;                             // month의 날수를 미리 더한다.
    for(i=1; i<month; i++)
        sum_days += GetDaysInMonth(year, i); // 1월달 ~ month-1달까지

    return sum_days;
}

//----------------------------------------------------------------------------------------
// * - 1. yield the difference between *A and *B, in seconds, ignoring leap seconds
// *   2. Unix SMB/Netbios implementation. Version 1.9.의 tm_diff()를 일부 수정하여 구현
// *   3. 항상 현재의 시간값이 크기 때문에 양수의 second를 return.
//----------------------------------------------------------------------------------------
int CAisLib::DiffTime(SYS_DATE_TIME *c_time, SYS_DATE_TIME *p_time)
{
    int ay = c_time->xDate.nYear + (TM_YEAR_BASE - 1);
    int by = p_time->xDate.nYear + (TM_YEAR_BASE - 1);
    int intervening_leap_days = (ay/4 - by/4) - (ay/100 - by/100) + (ay/400 - by/400);
    int years = ay - by;
    int days = 365*years + intervening_leap_days + (GetDaysFromYearFirst(c_time->xDate.nDay, c_time->xDate.nMon, c_time->xDate.nYear)
        - GetDaysFromYearFirst(p_time->xDate.nDay, p_time->xDate.nMon, p_time->xDate.nYear));
    int hours = 24*days + (c_time->xTime.nHour - p_time->xTime.nHour);
    int minutes = 60*hours + (c_time->xTime.nMin - p_time->xTime.nMin);
    int seconds = 60*minutes + (c_time->xTime.nSec - p_time->xTime.nSec);

    return seconds;
}

/******************************************************************************/
BOOL CAisLib::IsNMEAChar( BYTE c )
{
    if( ( 0x20 <= c ) && ( 0x7F >= c ) )
    {
        switch( c )
        {
        case 0x21:
        case 0x24:
        case 0x2A:
        case 0x2C:
        case 0x5C:
        case 0x5E:
        case 0x7E:
        case 0x7F:
            return FALSE;
        default:
            return TRUE;
        }
    }
    else if( ( 0x0A == c ) || ( 0x0D == c ) )
        return TRUE;
    return FALSE;
}

//----------------------------------------------------------------------------------------************/
BOOL CAisLib::CheckFieldSeqNum( char *nTxSeqNum )
{
    switch( nTxSeqNum[0] )
    {
    case '0' :
    case '1' :
    case '2' :
    case '3' :
    case '4' :
    case '5' :
    case '6' :
    case '7' :
    case '8' :
    case '9' :
        return TRUE;
    }
    return FALSE;
}

//----------------------------------------------------------------------------------------************/
BOOL CAisLib::CheckFieldStatus( char *status, char a, char b )
{
    return (( a == status[0] ) || ( b == status[0] ));
}

/******************************************************************************/
BOOL CAisLib::ToIEC61162Chars( char *src, char *target, size_t length)
{
    size_t    i = 0;
    size_t    src_idx = 0;
    size_t    res_idx = 0;
    char    result[128];
    char    buffer[4];

    memset(result, 0, sizeof(result));
    while((src_idx < strlen(src) ) && ( (res_idx + 3) < length))
    {
        if(IsNMEAChar(src[src_idx]))
        {
            result[res_idx] = src[src_idx];
            res_idx++;
        }
        else
        {
            memset( buffer, 0, sizeof(buffer) );
            sprintf( buffer, "^%02X", src[src_idx] );
            for( i = 0; i < 3; i++ )
            {
                result[res_idx + i] = buffer[i];
            }
            res_idx += 3;
        }
        src_idx++;
    }
    result[res_idx] = '\0';
    strncpy( target, result, strlen(result) );
    return TRUE;
}

double CAisLib::GetLongitude(char *data, char dir_flag)
{
    //---------------------------------------------------------------------
    // **  ASCII로 수신된 경도를 Double형(+/-)의 경도 Data Type으로 변환
    // data : GNR: dddmm.xxxx(x), ROS:dddmm.m(m)
    // dir_flag : 'E' : East, 'W': West
    //---------------------------------------------------------------------
    double pos_long=0;
    UINT16 nDataLen;
    char  datai[10], dataf[10];

    if(strlen(data) <= 0)
    {
        return AIS_FLOAT_LON_NULL_VAL;
    }

    GetDataSentence(data, 0, '.', datai, sizeof(datai));
    GetDataSentence(data, 1, '.', dataf, sizeof(dataf));

    nDataLen = strlen((char*)dataf);

    pos_long = (atoi(datai)/100) + (double)((atoi(datai)%100)+((double)atoi(dataf)/(pow(10,nDataLen))))/60;
    pos_long += 0.00000005;    // 소수점 8번째자리에서 반올림.

    if(dir_flag == 'W')
        pos_long *= (-1);

    return (pos_long);
}

//----------------------------------------------------------------------------------------
//**  ASCII로 수신된 위도를 Double형(+/-)의 위도 Data Type으로 변환                **
//----------------------------------------------------------------------------------------
double CAisLib::GetLatitude(char *data, char dir_flag)
{
    // GNR: ddmm.xxxx(x), ROS: ddmm.m(m)
    // 'N' : North, 'S': South

    double pos_lat=0;
    UINT16 nDataLen;
    char  datai[10], dataf[10];

    if(strlen(data) <= 0)
    {
        return AIS_FLOAT_LAT_NULL_VAL;
    }

    GetDataSentence(data, 0, '.', datai, sizeof(datai));
    GetDataSentence(data, 1, '.', dataf, sizeof(dataf));

    nDataLen = strlen((char*)dataf);

    pos_lat = (atoi(datai)/100) + (double)((atoi(datai)%100)+((double)atoi(dataf)/(pow(10,nDataLen))))/60;
    pos_lat += 0.00000005;    // 소수점 8번째자리에서 반올림.

    if(dir_flag == 'S')        pos_lat *= (-1);
    else;

    return (pos_lat);
}

BOOL CAisLib::GetMonthNumToStr(int mon_num, char *mon_str)
{
#define NUM_MONTH    12
    const char *pMonthTbl[NUM_MONTH] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
    if(1 <= mon_num && mon_num <= 12)
    {
        strcpy(mon_str, pMonthTbl[mon_num-1]);
        return TRUE;
    }
    strcpy(mon_str, "---");
    return FALSE;
}

BOOL CAisLib::IsDefaultTime(SYS_DATE_TIME *sys_time)
{
    return (sys_time->xTime.nHour == 24 && sys_time->xTime.nMin == 60 && sys_time->xTime.nSec == 60);
}

BOOL CAisLib::IsValidDimension(xANTPOS *ant_pos)
{
    if(ant_pos->wA == 0 || ant_pos->wB == 0 || ant_pos->wC == 0 || ant_pos->wD == 0)
        return FALSE;
    return TRUE;
}

int CAisLib::CheckValidChar(char *pstrData, int nDataLen)
{
    //------------------------------------------------------------------------------------
    // 끝에 붙은 '@' 은 지운다.
    // 중간에 나타나는 문자가 사용될수 있는 문자의 범위를 벗어나면, ' '(space)로 채움.
    // 정상적인 문자열 중간에 사용되는 '@'문자는 실제 Data일 수도 있다. Ex)AAA@@@BBB
    //------------------------------------------------------------------------------------
    int i;
    int str_len = nDataLen;

    for(i = 0 ;  i < nDataLen ; i++)
    {
        if(pstrData[i] != '\0' && (pstrData[i] < 0x20 || pstrData[i] > 0x5F))
            pstrData[i] = ' ';
    }

    // 문자열의 맨 뒤부터 '@'문자를 지워나간다.맨 뒤에 붙는 '@'는 null문자로 인식.
    for(i = nDataLen-1 ; i >= 0; i--)
    {
        if(pstrData[i] != '@')
            break;

        pstrData[i] = 0x00;
    }
    return str_len;
}

char* CAisLib::FillDataStrFixedSize(char *pstrData, int nDataLen)
{
    int nStrLen = MIN(strlen(pstrData), nDataLen);
    memset(&pstrData[nStrLen], '@', nDataLen-nStrLen);
    return pstrData;
}

INT16 CAisLib::GetSlotAppDataLength(BYTE app_type, int nTxMsg, BOOL bDestIndicator, BOOL bBinaryFlag, UINT16 nNumDataBits)
{
#define __MAX_SLOT_3SLOT__
    //---------------------------------------------------------------------------------------------
    // 메시지 6, 12, 8, 14, 25, 26 의 송신을 위한 필요슬롯 수 계산
    // bDestIndicator : TRUE => addressed, FALSE => broadcast
    // bBinaryFlag  0 = unstructured binary data (no Application Identifier bits used)
    //                1 = binary data coded as defined by using the 16-bit Application identifier
    //---------------------------------------------------------------------------------------------
    if(nTxMsg == AIS_MSG_NO_ID_25)
    {
        if(bDestIndicator)
        {
            if(nNumDataBits <= MAX_TX_BITS_MSG25)    return MSG_SLOT1;
        }
        else
        {
            if(nNumDataBits <= 96)    return MSG_SLOT1;
        }
    }
    else if(nTxMsg == AIS_MSG_NO_ID_26)
    {
        if(bDestIndicator)
        {
            if(bBinaryFlag)
            {
                     if(nNumDataBits <= 56)        return MSG_SLOT1;
                else if(nNumDataBits <= 280)    return MSG_SLOT2;
                else if(nNumDataBits <= 504)    return MSG_SLOT3;
#ifndef __MAX_SLOT_3SLOT__
                else if(nNumDataBits <= 728)    return MSG_SLOT4;
                else if(nNumDataBits <= 952)    return MSG_SLOT5;
#endif
            }
            else
            {
                     if(nNumDataBits <= 72)        return MSG_SLOT1;
                else if(nNumDataBits <= 296)    return MSG_SLOT2;
                else if(nNumDataBits <= 520)    return MSG_SLOT3;
#ifndef __MAX_SLOT_3SLOT__
                else if(nNumDataBits <= 744)    return MSG_SLOT4;
                else if(nNumDataBits <= 968)    return MSG_SLOT5;
#endif
            }
        }
        else
        {
            if(bBinaryFlag)
            {
                     if(nNumDataBits <= 88)        return MSG_SLOT1;
                else if(nNumDataBits <= 312)    return MSG_SLOT2;
                else if(nNumDataBits <= 536)    return MSG_SLOT3;
#ifndef __MAX_SLOT_3SLOT__
                else if(nNumDataBits <= 760)    return MSG_SLOT4;
                else if(nNumDataBits <= 984)    return MSG_SLOT5;
#endif
            }
            else
            {
                     if(nNumDataBits <= 104)    return MSG_SLOT1;
                else if(nNumDataBits <= 328)    return MSG_SLOT2;
                else if(nNumDataBits <= 552)    return MSG_SLOT3;
#ifndef __MAX_SLOT_3SLOT__
                else if(nNumDataBits <= 776)    return MSG_SLOT4;
                /*
                else if(nNumDataBits <= 1000)    return MSG_SLOT5;
                */
                // 이반한테 질문
                // ITU-R-1371-5 Table 83 에는 1000 으로 되어있고 본문에는 왜 1004 인지?
                // 3.24 Message 26: Multiple slot binary message with communications state
                // This multiple slot binary message can contain up to 1004 data-bits (using 5 slots) depending on the coding method used for the contents,
                // and the destination indication of broadcast or addressed.
                else if(nNumDataBits <= 1004)    return MSG_SLOT5;
#endif
            }
        }
    }
    else
    {
        if(app_type == TYPE_ABM)
        {
            if       (nNumDataBits <= MAX_ABM1_BIT)    return MSG_SLOT1;
            else if(nNumDataBits <= MAX_ABM2_BIT)    return MSG_SLOT2;
            else if(nNumDataBits <= MAX_ABM3_BIT)    return MSG_SLOT3;
#ifndef __MAX_SLOT_3SLOT__
            else if(nNumDataBits <= MAX_ABM4_BIT)    return MSG_SLOT4;
            else if(nNumDataBits <= MAX_ABM5_BIT)    return MSG_SLOT5;
#endif
        }
        else
        {
            if       (nNumDataBits <= MAX_BBM1_BIT)    return MSG_SLOT1;
            else if(nNumDataBits <= MAX_BBM2_BIT)    return MSG_SLOT2;
            else if(nNumDataBits <= MAX_BBM3_BIT)    return MSG_SLOT3;
#ifndef __MAX_SLOT_3SLOT__
            else if(nNumDataBits <= MAX_BBM4_BIT)    return MSG_SLOT4;
            else if(nNumDataBits <= MAX_BBM5_BIT)    return MSG_SLOT5;
#endif
        }
    }
    return MSG_SLOT_OVER;
}

int CAisLib::HexaStrToDecimal(char *hexa_str, int digit_num)
{
    int i, ret_val=0;

    for(i = 0 ;  i<digit_num; i++)
    {
        if((hexa_str[i] >= 'a') && (hexa_str[i] <= 'f'))        // 소문자
            ret_val= 16*ret_val+(hexa_str[i]-87);
        else if((hexa_str[i] >= 'A') && (hexa_str[i] <= 'F'))    // 대문자
            ret_val= 16*ret_val+(hexa_str[i]-55);
        else if((hexa_str[i] >= '0') && (hexa_str[i] <= '9'))
            ret_val= 16*ret_val+(hexa_str[i]-'0');
        else
            return -1;
    }

    return ret_val;
}


//----------------------------------------------------------------------------------------*****/
BYTE CAisLib::ASCII8ToBin6bit(BYTE ascii_8)
{
    if(ascii_8 < 0x30)
        return 255;    // Data Error Recovery (61993-2,p.110)
    else;

    if(ascii_8 > 0x77)
        return 255;    // Data Error Recovery (61993-2,p.110)
    else;

    if(ascii_8 > 0x57)
    {
        if(ascii_8 < 0x60)
            return 255;    // 0x58, 0x59: Data Error Recovery
        else;
    }
    else;

    ascii_8 += 0x28;
    if(ascii_8 > 0x80)
        ascii_8 += 0x20;
    else
        ascii_8 += 0x28;

    return(ascii_8 & 0x3F);
}

//----------------------------------------------------------------------------------------
// **  - IEC 61162-1의 Valid ASCII 문자를 6bit binary field로 변환                            **
// **  - Addr Msg bit수를 저장하고 있기 때문에 fill bit가 0으로 채워진다고 해도 상관없다.  **
//----------------------------------------------------------------------------------------
BOOL CAisLib::ConvertASCIICodeTo6BitBinary(char *buff_8bit, char *buff_6bit)    // buff_8bit: vhf binary data를 담을 buff
{                                                                        // buff_6bit: encapsulated data(6bit ASCII문자)
    int   i;
    int   mog, namugi;
    BYTE bin_6bit;
    int      data_len_6bit = strlen((char*)buff_6bit);


    for(i = 0 ;  i < data_len_6bit; i++)
    {
        bin_6bit = ASCII8ToBin6bit(buff_6bit[i]);
        if(bin_6bit == 255)
        {
            return FALSE;    // data Error Recovery (회복절차없이 수신Data 전체를 삭제)
        }

        mog    = (i*6) / 8;
        namugi = (i*6) % 8;

        switch(namugi)
        {
        case 0 :
            buff_8bit[mog] = bin_6bit << 2;
            break;
        case 2 :
            buff_8bit[mog] = (buff_8bit[mog] & 0xC0) | bin_6bit;
            break;
        case 4 :
            buff_8bit[mog]   = (buff_8bit[mog] & 0xF0) | bin_6bit >> 2;
            buff_8bit[mog+1] = bin_6bit << 6;
            break;
        case 6 :
            buff_8bit[mog]   = (buff_8bit[mog] & 0xFC) | bin_6bit >> 4;
            buff_8bit[mog+1] = bin_6bit << 4;
            break;
        default :
            break;
        }
    }
    return TRUE;
}

/**********************************************************************************
**  Description:                                                                 **
**  입력된 NMEA 0183 Sentence를 ',' 문자로 구분되는 field로 나누어 저장
**  마지막 필드가 *로 끝나는 정상적인 Sentence일 경우 필드 개수를 리턴
**  비정상적인 Sentence일 경우 0을 리턴
**********************************************************************************/
UINT8 CAisLib::SplitNMEASentence(char *data, char *fields[], size_t length)
{
    UINT8 count = 0;                        /* number of fields */
    char   *p = data;                       /* beginning of first field */
    char   *t = data;

    if(NULL == p)                           /* wrong sentence */
        return 0;

    p[length-1] = '\0';                     /* Forced insertion of null character just for safety */
    p[length-2] = '\0';                     /* Forced insertion of null character just for safety */

    while( count < 32 )
        fields[count++] = NULL;

    while(isprint(*t) && (*t != '*'))
        ++t;
    if(*t == '*')
        *t++ = ',';                         /* otherwise we drop the last field */
    *t = '\0';
    count = 0;

    /* while there is a search string and we haven't run off the buffer... */
    while((p != NULL)  && (p <= t))
    {
        fields[count] = p;                  /* we have a field. record it */
        if((p = strchr(p, ',')) != NULL)    /* search for the next delimiter */
        {
            *p = '\0';                      /* replace it with a NULL */
            count++;                        /* bump the counters and continue */
            p++;                            /* skip ',' and pointer to beginning of next field */
        }
    }
    return count;
}

BOOL CAisLib::IsSameAisStr(const char *pstr1, const char *pstr2)
{
    char pstrTmp1[128];
    char pstrTmp2[128];

    strcpy(pstrTmp1, pstr1);
    strcpy(pstrTmp2, pstr2);
    RightTrimStr(pstrTmp1, '@');
    RightTrimStr(pstrTmp2, '@');
    return !strcmp(pstrTmp1, pstrTmp2);
}
