#include "SyncMgr.h"
#include "ReportRateMgr.h"
#include "LayerPhysical.h"
#include "LayerNetwork.h"
#include "SetupMgr.h"
#include "MKD.h"
#include "Timer.h"
#include "SysLog.h"
#include "Ship.h"
#include "Txt.h"
#include "SysOpStatus.h"
#include "RosMgr.h"

CROSMgr::CROSMgr(void)
{
    m_vAllRosDATA = (xROSDATA*)SysAllocMemory(sizeof(xROSDATA) * (MAX_ROS_DATA_SIZE+1));
    m_vAllRoaDATA = (xROADATA*)SysAllocMemory(sizeof(xROADATA) * (MAX_ROS_DATA_SIZE+1));
    ClearRosData();

    memset(&m_sRosData, 0, sizeof(m_sRosData));
    memset(&m_sChSetup, 0, sizeof(m_sChSetup));

    m_dwTRxModeStartSec = 0;

    m_nLowPowerModeByNavStatus = POWER_MODE_DEFAULT;

    m_bCheckTrxModeByAddrMsg22 = false;
    m_bTrxModeByAddrMsg22Done  = false;
    m_dwTRxModeStartSecByAddrMsg22 = 0;

    // Calculate ROA data from ROS data except for Default
    CalcAllRoaDataByROS();
}

CROSMgr::~CROSMgr(void)
{
}

/**
 * @brief Initialize ROS setup
 */
void CROSMgr::InitRosSetup(void)
{
    //----------------------------------------------------------------------
    // ITU-R 1371-5 Ann2
    // 4.1.7 Resumption of operation after power on
    // After power on, a mobile station should resume operation using 
    // the default settings, unless the own position is within any of 
    // the stored regions. In this case, the mobile station should operate 
    // using the stored operating settings of that identified region.
    //----------------------------------------------------------------------

    // system seconds when the ROS was received is not valid after reboot
    for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        m_vAllRosDATA[i].dwRcvSysSec = 0;
    }

    ROS_DATA sRosData;
    sRosData.nRosMode   = ROS_MODE_HIGH_SEA_ZONE;
    sRosData.nRosIdx    = ROS_IDX_HIGHSEA;
    sRosData.nRosAdjIdx = ROS_IDX_HIGHSEA;

    sRosData.sChSetup.uTxPower      = m_vAllRosDATA[sRosData.nRosIdx].bTxPower;
    sRosData.sChSetup.uChannelIdA   = m_vAllRosDATA[sRosData.nRosIdx].wChannelNoA;
    sRosData.sChSetup.uBandwidthA   = m_vAllRosDATA[sRosData.nRosIdx].bBandwidthA;
    sRosData.sChSetup.uChannelIdB   = m_vAllRosDATA[sRosData.nRosIdx].wChannelNoB;
    sRosData.sChSetup.uBandwidthB   = m_vAllRosDATA[sRosData.nRosIdx].bBandwidthB;
    sRosData.sChSetup.nTxRxMode     = m_vAllRosDATA[sRosData.nRosIdx].bTxRxMode;
    sRosData.sChSetup.nTxRxModeBy   = GetTrxModeByFromRosSetup(m_vAllRosDATA[sRosData.nRosIdx].bTxRxMode, m_vAllRosDATA[sRosData.nRosIdx].bRosSource);

    SetRosData(&sRosData);
    SetChSetupData(&(sRosData.sChSetup), TRUE);
    CLayerNetwork::getInst()->InitChSetupNetLayer(&m_sChSetup);
}

/**
 * @brief Clear all ROS data
 */
void CROSMgr::ClearAllRosData(void)
{
    for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        ClearOneRosData(i);
    }
}

/**
 * @brief Clear one ROS data
 * @param nPos ROS index to clear
 */
void CROSMgr::ClearOneRosData(int nPos)
{
    if(!IsRosIndexValid_Ext(nPos))
    {
        return;
    }

    ClearOneRosData(&m_vAllRosDATA[nPos], &m_vAllRoaDATA[nPos]);
}

/**
 * @brief Clear one ROS data
 * @param pRosData ROS data to clear
 */
void CROSMgr::ClearOneRosData(xROSDATA *pRosData)
{
    memset(pRosData, 0x00, sizeof(xROSDATA));

    CAisLib::ClearHalfPosToNULL(&pRosData->xPointNE);
    CAisLib::ClearHalfPosToNULL(&pRosData->xPointSW);
}

/**
 * @brief Clear one ROA data
 * @param pRoaData ROA data to clear
 */
void CROSMgr::ClearOneRoaData(xROADATA *pRoaData)
{
    memset(pRoaData, 0x00, sizeof(xROADATA));
}

/**
 * @brief Clear one ROS data
 * @param pRosData ROS data to clear
 * @param pRoaData ROA data to clear
 */
void CROSMgr::ClearOneRosData(xROSDATA *pRosData, xROADATA *pRoaData)
{
    memset(pRosData, 0x00, sizeof(xROSDATA));
    memset(pRoaData, 0x00, sizeof(xROADATA));

    CAisLib::ClearHalfPosToNULL(&pRosData->xPointNE);
    CAisLib::ClearHalfPosToNULL(&pRosData->xPointSW);
}

/**
 * @brief Check if ROS index is valid
 * @param nRosIndex ROS index to check
 * @return TRUE if valid, FALSE otherwise
 */
bool CROSMgr::IsRosIndexValid_Ext(int nRosIndex)
{
    return (0 <= nRosIndex && nRosIndex < MAX_ROS_DATA_SIZE);
}

bool CROSMgr::IsRosIndexValid_Int(int nRosIndex)
{
    return (0 <= nRosIndex && nRosIndex <= ROS_IDX_HIGHSEA);
}

/**
 * @brief Check if ROS is in use
 * @param nRosIndex ROS index to check
 * @return TRUE if in use, FALSE otherwise
 */
bool CROSMgr::IsRosInUse(int nRosIndex)
{
    return (nRosIndex == m_nRosInUse);
}

/**
 * @brief Set ROS data
 * @param psRosData ROS data to set
 */
void CROSMgr::SetRosData(ROS_DATA *psRosData)
{
    DEBUG_LOG("SetRosData] (%d,%d,%d) -> (%d,%d,%d) TRX: %d(%d) -> %d(%d), pwr:%d -> %d, chA:%d -> %d, chB:%d -> %d, s:%d\r\n",
        m_sRosData.nRosMode, m_sRosData.nRosIdx, m_sRosData.nRosAdjIdx,
        psRosData->nRosMode, psRosData->nRosIdx, psRosData->nRosAdjIdx,
        m_sChSetup.nTxRxMode, m_sChSetup.nTxRxModeBy,
        psRosData->sChSetup.nTxRxMode, psRosData->sChSetup.nTxRxModeBy,
        m_sChSetup.uTxPower, psRosData->sChSetup.uTxPower,
        m_sChSetup.uChannelIdA, psRosData->sChSetup.uChannelIdA,
        m_sChSetup.uChannelIdB, psRosData->sChSetup.uChannelIdB,
        cTimerSys::getInst()->GetCurTimerSec());

    memcpy(&m_sRosData, psRosData, sizeof(ROS_DATA));
}

/**
 * @brief Set TRX mode and channel data
 * @param nTxRxMode TRX mode to set
 * @param nTxRxModeBy TRX mode by to set
 * @param nChA Channel A to set
 * @param nChB Channel B to set
 */
void CROSMgr::SetTrxModeChData(int nTxRxMode, int nTxRxModeBy, int nChA, int nChB)
{
    DEBUG_LOG("-------------------------- SetTrxModeChData] TRXmode: %d(%d) -> %d(%d), chA:%d->%d, chB:%d->%d, TRmode: %d, s:%d\r\n",
        m_sChSetup.nTxRxMode, m_sChSetup.nTxRxModeBy, nTxRxMode, nTxRxModeBy,
        m_sChSetup.uChannelIdA, nChA, m_sChSetup.uChannelIdB, nChB, IsTrModeRunning(), cTimerSys::getInst()->GetCurTimerSec());

    m_sChSetup.nTxRxMode    = nTxRxMode;
    m_sChSetup.nTxRxModeBy    = nTxRxModeBy;
    m_sChSetup.uChannelIdA    = nChA;
    m_sChSetup.uChannelIdB    = nChB;
}

/**
 * @brief Set channel setup data
 * @param psChSetup Channel setup data to set
 * @param bSetTrxMode Set TRX mode
 */
void CROSMgr::SetChSetupData(CH_SETUP *psChSetup, bool bSetTrxMode)
{
    int nOrgTrxMode = m_sChSetup.nTxRxMode;
    int nOrgTrxModeBy = m_sChSetup.nTxRxModeBy;

    memcpy(&m_sChSetup, psChSetup, sizeof(CH_SETUP));
    if(!bSetTrxMode)
    {
        m_sChSetup.nTxRxMode   = nOrgTrxMode;
        m_sChSetup.nTxRxModeBy = nOrgTrxModeBy;
    }
}

/**
 * @brief Serialize ROS data
 * @param pBackData Buffer to store serialized data
 * @return Size of serialized data
 */
int CROSMgr::SerializeRosData(UCHAR *pBackData)
{
    int    nSize;
    UCHAR  *pTemp = pBackData;

    // Data header
    memcpy(pTemp, SETUP_HEADER, SETUP_HEADER_LEN);
    pTemp += SETUP_HEADER_LEN;

    for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        memmove(pTemp, &m_vAllRosDATA[i].bValidMode,    sizeof(m_vAllRosDATA[i].bValidMode)); 
        pTemp += sizeof(m_vAllRosDATA[i].bValidMode);
        memmove(pTemp, &m_vAllRosDATA[i].bTxRxMode,     sizeof(m_vAllRosDATA[i].bTxRxMode)); 
        pTemp += sizeof(m_vAllRosDATA[i].bTxRxMode);
        memmove(pTemp, &m_vAllRosDATA[i].bTxPower,      sizeof(m_vAllRosDATA[i].bTxPower)); 
        pTemp += sizeof(m_vAllRosDATA[i].bTxPower);
        memmove(pTemp, &m_vAllRosDATA[i].bTrZoneSize,   sizeof(m_vAllRosDATA[i].bTrZoneSize)); 
        pTemp += sizeof(m_vAllRosDATA[i].bTrZoneSize);
        memmove(pTemp, &m_vAllRosDATA[i].bRosSource,    sizeof(m_vAllRosDATA[i].bRosSource)); 
        pTemp += sizeof(m_vAllRosDATA[i].bRosSource);
        memmove(pTemp, &m_vAllRosDATA[i].bBandwidthA,   sizeof(m_vAllRosDATA[i].bBandwidthA)); 
        pTemp += sizeof(m_vAllRosDATA[i].bBandwidthA);
        memmove(pTemp, &m_vAllRosDATA[i].bBandwidthB,   sizeof(m_vAllRosDATA[i].bBandwidthB)); 
        pTemp += sizeof(m_vAllRosDATA[i].bBandwidthB);
        memmove(pTemp, &m_vAllRosDATA[i].wChannelNoA,   sizeof(m_vAllRosDATA[i].wChannelNoA)); 
        pTemp += sizeof(m_vAllRosDATA[i].wChannelNoA);
        memmove(pTemp, &m_vAllRosDATA[i].wChannelNoB,   sizeof(m_vAllRosDATA[i].wChannelNoB)); 
        pTemp += sizeof(m_vAllRosDATA[i].wChannelNoB);
        memmove(pTemp, &m_vAllRosDATA[i].dSrcMMSI,      sizeof(m_vAllRosDATA[i].dSrcMMSI)); 
        pTemp += sizeof(m_vAllRosDATA[i].dSrcMMSI);
        memmove(pTemp, &m_vAllRosDATA[i].xPointNE.xPosL,sizeof(m_vAllRosDATA[i].xPointNE.xPosL)); 
        pTemp += sizeof(m_vAllRosDATA[i].xPointNE.xPosL);
        memmove(pTemp, &m_vAllRosDATA[i].xPointSW.xPosL,sizeof(m_vAllRosDATA[i].xPointSW.xPosL)); 
        pTemp += sizeof(m_vAllRosDATA[i].xPointSW.xPosL);
        memmove(pTemp, &m_vAllRosDATA[i].xRcvTime,      sizeof(m_vAllRosDATA[i].xRcvTime)); 
        pTemp += sizeof(m_vAllRosDATA[i].xRcvTime);
    }

    // Checksum
    DWORD *pdwChecksum = (DWORD*)pTemp;
    nSize = pTemp - pBackData;
    *pdwChecksum = GetCrc32(pBackData, nSize);
    nSize += SETUP_CHECKSUM_LEN;

    return nSize;
}

/**
 * @brief Load ROS configuration data
 * @param pBackData Buffer to store serialized data
 * @return true if data is loaded successfully, false otherwise
 */
bool CROSMgr::LoadRosConfigData(UCHAR *pBackData)
{
    int    i;
    UCHAR  *pTemp;

    pTemp = pBackData;
    pTemp += SETUP_HEADER_LEN;

    for (i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        memmove(&m_vAllRosDATA[i].bValidMode     , pTemp, sizeof(m_vAllRosDATA[i].bValidMode     )); pTemp += sizeof(m_vAllRosDATA[i].bValidMode     );
        memmove(&m_vAllRosDATA[i].bTxRxMode      , pTemp, sizeof(m_vAllRosDATA[i].bTxRxMode      )); pTemp += sizeof(m_vAllRosDATA[i].bTxRxMode      );
        memmove(&m_vAllRosDATA[i].bTxPower       , pTemp, sizeof(m_vAllRosDATA[i].bTxPower       )); pTemp += sizeof(m_vAllRosDATA[i].bTxPower       );
        memmove(&m_vAllRosDATA[i].bTrZoneSize    , pTemp, sizeof(m_vAllRosDATA[i].bTrZoneSize    )); pTemp += sizeof(m_vAllRosDATA[i].bTrZoneSize    );
        memmove(&m_vAllRosDATA[i].bRosSource     , pTemp, sizeof(m_vAllRosDATA[i].bRosSource     )); pTemp += sizeof(m_vAllRosDATA[i].bRosSource     );
        memmove(&m_vAllRosDATA[i].bBandwidthA    , pTemp, sizeof(m_vAllRosDATA[i].bBandwidthA    )); pTemp += sizeof(m_vAllRosDATA[i].bBandwidthA    );
        memmove(&m_vAllRosDATA[i].bBandwidthB    , pTemp, sizeof(m_vAllRosDATA[i].bBandwidthB    )); pTemp += sizeof(m_vAllRosDATA[i].bBandwidthB    );
        memmove(&m_vAllRosDATA[i].wChannelNoA    , pTemp, sizeof(m_vAllRosDATA[i].wChannelNoA    )); pTemp += sizeof(m_vAllRosDATA[i].wChannelNoA    );
        memmove(&m_vAllRosDATA[i].wChannelNoB    , pTemp, sizeof(m_vAllRosDATA[i].wChannelNoB    )); pTemp += sizeof(m_vAllRosDATA[i].wChannelNoB    );
        memmove(&m_vAllRosDATA[i].dSrcMMSI       , pTemp, sizeof(m_vAllRosDATA[i].dSrcMMSI       )); pTemp += sizeof(m_vAllRosDATA[i].dSrcMMSI       );
        memmove(&m_vAllRosDATA[i].xPointNE.xPosL , pTemp, sizeof(m_vAllRosDATA[i].xPointNE.xPosL )); pTemp += sizeof(m_vAllRosDATA[i].xPointNE.xPosL );
        memmove(&m_vAllRosDATA[i].xPointSW.xPosL , pTemp, sizeof(m_vAllRosDATA[i].xPointSW.xPosL )); pTemp += sizeof(m_vAllRosDATA[i].xPointSW.xPosL );
        memmove(&m_vAllRosDATA[i].xRcvTime       , pTemp, sizeof(m_vAllRosDATA[i].xRcvTime       )); pTemp += sizeof(m_vAllRosDATA[i].xRcvTime       );
    }

    VerifyRosData();
    CalcAllRoaDataByROS();

    SetDefaultRosData(&m_vAllRosDATA[ROS_IDX_HIGHSEA], &m_vAllRoaDATA[ROS_IDX_HIGHSEA]);
    m_nRosInUse = ROS_IDX_HIGHSEA;
    CAisLib::SetDefaultSysDateTime(&m_xRosInUseTime);

    return true;
}

/**
 * @brief Verify ROS data
 */
void CROSMgr::VerifyRosData(void)
{
    for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_OFF ||
            RangeCheckBack08(&m_vAllRosDATA[i].bValidMode, MODE_VAL_OFF, MODE_VAL_ON, MODE_VAL_OFF) ||
            RangeCheckBack08(&m_vAllRosDATA[i].bTxRxMode, TRXMODE_MIN, TRXMODE_MAX, TRXMODE_DFLT) ||
            RangeCheckBack08(&m_vAllRosDATA[i].bTxPower, AIS_TX_POWER_HIGH, AIS_TX_POWER_LOW, AIS_TX_POWER_HIGH) ||
            RangeCheckBack08(&m_vAllRosDATA[i].bTrZoneSize, TRZONE_SIZE_MIN, TRZONE_SIZE_MAX, TRZONE_SIZE_DFLT) ||
            RangeCheckBack08(&m_vAllRosDATA[i].bRosSource, ROS_SRC_MIN, ROS_SRC_MAX, ROS_SRC_DFLT) ||
            m_vAllRosDATA[i].bBandwidthA != AIS_CH_BW_25_0_KHZ || m_vAllRosDATA[i].bBandwidthB != AIS_CH_BW_25_0_KHZ ||
            !CAisLib::GetAisFreqByChannelNo(m_vAllRosDATA[i].wChannelNoA) || !CAisLib::GetAisFreqByChannelNo(m_vAllRosDATA[i].wChannelNoB) ||
            !CAisLib::IsValidAisLowPOS(&m_vAllRosDATA[i].xPointNE.xPosL) || !CAisLib::IsValidAisLowPOS(&m_vAllRosDATA[i].xPointSW.xPosL))
        {
            if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
            {
                INFO_LOG("VerifyRosData] Wrong ROS setup data and reset, #%d(%d), validF: %d, from:%d, %09d, NE:%.2f,%.2f(%d,%d) SW:%.2f,%.2f(%d,%d), ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d, %d-%d-%d,%02d:%02d:%02d\r\n",
                        i, IsRosInUse(i), m_vAllRosDATA[i].bValidMode,
                        m_vAllRosDATA[i].bRosSource, m_vAllRosDATA[i].dSrcMMSI,
                        m_vAllRosDATA[i].xPointNE.xPosF.fLON, m_vAllRosDATA[i].xPointNE.xPosF.fLAT, m_vAllRosDATA[i].xPointNE.xPosL.nLON, m_vAllRosDATA[i].xPointNE.xPosL.nLAT,
                        m_vAllRosDATA[i].xPointSW.xPosF.fLON, m_vAllRosDATA[i].xPointSW.xPosF.fLAT, m_vAllRosDATA[i].xPointSW.xPosL.nLON, m_vAllRosDATA[i].xPointSW.xPosL.nLAT,
                        m_vAllRosDATA[i].wChannelNoA, m_vAllRosDATA[i].wChannelNoB,
                        m_vAllRosDATA[i].bTxRxMode, m_vAllRosDATA[i].bTxPower, m_vAllRosDATA[i].bTrZoneSize,
                        m_vAllRosDATA[i].xRcvTime.xDate.nYear, m_vAllRosDATA[i].xRcvTime.xDate.nMon, m_vAllRosDATA[i].xRcvTime.xDate.nDay,
                        m_vAllRosDATA[i].xRcvTime.xTime.nHour, m_vAllRosDATA[i].xRcvTime.xTime.nMin, m_vAllRosDATA[i].xRcvTime.xTime.nSec);
            }

            ClearOneRosData(i);
        }

        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
        	CAisLib::CalcFullPosByLow(&m_vAllRosDATA[i].xPointNE);
        	CAisLib::CalcFullPosByLow(&m_vAllRosDATA[i].xPointSW);
        }
    }
}

/**
 * @brief Clear all ROS data
 */
void  CROSMgr::ClearRosData(void)
{
    ClearAllRosData();
    SetDefaultRosData(&m_vAllRosDATA[ROS_IDX_HIGHSEA], &m_vAllRoaDATA[ROS_IDX_HIGHSEA]);
    m_nRosInUse = ROS_IDX_HIGHSEA;
    CAisLib::SetDefaultSysDateTime(&m_xRosInUseTime);
}

/**
 * @brief Set default ROA data
 */
void CROSMgr::SetDefaultRoaData(xROADATA *pRoaData)
{
    ClearOneRoaData(pRoaData);
}

/**
 * @brief Set default ROS data
 */
void CROSMgr::SetDefaultRosData(xROSDATA *pRosData)
{
    memset(pRosData, 0x00, sizeof(xROSDATA));

    pRosData->bValidMode  = MODE_VAL_ON;

    pRosData->bTxRxMode   = TRXMODE_TXARXA_TXBRXB;
    pRosData->bTxPower    = AIS_TX_POWER_HIGH;
    pRosData->bTrZoneSize = TRZONE_SIZE_DFLT;
    pRosData->bRosSource  = ROS_SRC_DFLT;

    pRosData->bBandwidthA = AIS_CH_BW_25_0_KHZ;
    pRosData->bBandwidthB = AIS_CH_BW_25_0_KHZ;

    pRosData->wChannelNoA = AIS1_DEFAULT_CH_NUM;
    pRosData->wChannelNoB = AIS2_DEFAULT_CH_NUM;

    pRosData->dSrcMMSI    = AIS_AB_MMSI_NULL;

    CAisLib::ClearHalfPosToNULL(&pRosData->xPointNE);
    CAisLib::ClearHalfPosToNULL(&pRosData->xPointSW);

    CAisLib::SetDefaultSysDateTime(&pRosData->xRcvTime);
}

/**
 * @brief Set default ROS data
 */
void CROSMgr::SetDefaultRosData(xROSDATA *pRosData, xROADATA *pRoaData)
{
    SetDefaultRosData(pRosData);
    SetDefaultRoaData(pRoaData);
}

/**
 * @brief Check if ROS data is default setting
 * @param psRosData ROS data to check
 * @return TRUE if default setting, FALSE otherwise
 */
bool CROSMgr::IsRosDataDefaultSetting(xROSDATA *psRosData)
{
    return ((psRosData->bTxRxMode == m_vAllRosDATA[ROS_IDX_HIGHSEA].bTxRxMode) &&
            (psRosData->bTxPower == m_vAllRosDATA[ROS_IDX_HIGHSEA].bTxPower) &&
            (psRosData->bTrZoneSize == m_vAllRosDATA[ROS_IDX_HIGHSEA].bTrZoneSize) &&
            (psRosData->wChannelNoA == m_vAllRosDATA[ROS_IDX_HIGHSEA].wChannelNoA) &&
            (psRosData->wChannelNoB == m_vAllRosDATA[ROS_IDX_HIGHSEA].wChannelNoB) &&
            (psRosData->xPointNE.xPosG.nLON == m_vAllRosDATA[ROS_IDX_HIGHSEA].xPointNE.xPosG.nLON) &&
            (psRosData->xPointNE.xPosG.nLAT == m_vAllRosDATA[ROS_IDX_HIGHSEA].xPointNE.xPosG.nLAT) &&
            (psRosData->xPointSW.xPosG.nLON == m_vAllRosDATA[ROS_IDX_HIGHSEA].xPointSW.xPosG.nLON) &&
            (psRosData->xPointSW.xPosG.nLAT == m_vAllRosDATA[ROS_IDX_HIGHSEA].xPointSW.xPosG.nLAT));
}

/**
 * @brief Get current ROS index
 * @return Current ROS index
 */
int CROSMgr::GetCurrRosNo(void)
{
    return(m_sRosData.nRosIdx);
}

/**
 * @brief Get current ROS data pointer
 * @return Current ROS data pointer
 */
xROSDATA *CROSMgr::GetCurrRosDataPtr(void)
{
    return(&m_vAllRosDATA[m_sRosData.nRosIdx]);
}

/**
 * @brief Get current ROA data pointer
 * @return Current ROA data pointer
 */
xROADATA *CROSMgr::GetCurrRoaDataPtr(void)
{
    return(&m_vAllRoaDATA[m_sRosData.nRosIdx]);
}

/**
 * @brief Get current ROA run mode
 * @return Current ROA run mode
 */
int CROSMgr::GetRoaRunMode(void)
{
    return(m_sRosData.nRosMode);
}

/**
 * @brief Check if current ROA run mode is TR zone
 * @return true if current ROA run mode is TR zone, false otherwise
 */
bool CROSMgr::CheckInsideRectangleXX(POS_GRID *pTestPnt, POS_GRID *pNE, POS_GRID *pSW)
{
    return (CGps::CheckLatInGridLatRange(pTestPnt->nLAT, pSW->nLAT, pNE->nLAT, 1) &&
            CGps::CheckLonInGridLonRange(pTestPnt->nLON, pSW->nLON, pNE->nLON, 1));
}
bool CROSMgr::CheckInsideRectangleXY(POS_GRID *pTestPnt, int nLonNE, int nLatNE, int nLonSW, int nLatSW)
{
    return (CGps::CheckLatInGridLatRange(pTestPnt->nLAT, nLatSW, nLatNE, 1) &&
            CGps::CheckLonInGridLonRange(pTestPnt->nLON, nLonSW, nLonNE, 1));
}
bool CROSMgr::CheckInsideRectangleXZ(POS_GRID *pTestPnt, POS_GRID *pRectNE, POS_GRID *pRectSW)
{
    return (CGps::CheckLatInGridLatRange(pTestPnt->nLAT, pRectSW->nLAT, pRectNE->nLAT, 0) &&
            CGps::CheckLonInGridLonRange(pTestPnt->nLON, pRectSW->nLON, pRectNE->nLON, 0));
}

/**
 * @brief Calculate ROA data from ROS data
 */
void CROSMgr::CalcAllRoaDataByROS(void)
{
    for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            CalcOneRoaDataByROS(&m_vAllRosDATA[i], &m_vAllRoaDATA[i]);
        }
    }
}

/**
 * @brief Calculate ROA data from ROS data
 * @param pROS ROS data
 * @param pROA ROA data
 */
void CROSMgr::CalcOneRoaDataByROS(xROSDATA *pROS, xROADATA *pROA)
{
    // Set Transition Region X
    pROA->xRectNE = pROS->xPointNE;
    pROA->xRectSW = pROS->xPointSW;

    pROA->xRectNW.xPosL.nLON = pROS->xPointSW.xPosL.nLON;
    pROA->xRectNW.xPosL.nLAT = pROS->xPointNE.xPosL.nLAT;
    pROA->xRectSE.xPosL.nLON = pROS->xPointNE.xPosL.nLON;
    pROA->xRectSE.xPosL.nLAT = pROS->xPointSW.xPosL.nLAT;

    CAisLib::CalcFullPosByLow(&pROA->xRectNE);
    CAisLib::CalcFullPosByLow(&pROA->xRectNW);
    CAisLib::CalcFullPosByLow(&pROA->xRectSE);
    CAisLib::CalcFullPosByLow(&pROA->xRectSW);

    DEBUG_LOG("CalcROS] NE:%.2f,%.2f SW:%.2f,%.2f, NW:%.2f,%.2f SE:%.2f,%.2f\r\n",
        pROA->xRectNE.xPosF.fLON, pROA->xRectNE.xPosF.fLAT,
        pROA->xRectSW.xPosF.fLON, pROA->xRectSW.xPosF.fLAT,
        pROA->xRectNW.xPosF.fLON, pROA->xRectNW.xPosF.fLAT,
        pROA->xRectSE.xPosF.fLON, pROA->xRectSE.xPosF.fLAT);

    // Set inner and outer zone
    SetInnerOuterZoneByROA(pROA, pROS->bTrZoneSize);
}

/**
 * @brief Set inner and outer zone by ROA data
 * @param pROA ROA data
 * @param nTrZoneSizeNM TR zone size in NM
 */
void CROSMgr::SetInnerOuterZoneByROA(xROADATA *pROA, int nTrZoneSizeNM)
{
    //------------------------------------------------------------------------
    // IALA Volume I Part II : Transition zone size
    // Outer: 5 nautical miles
    // Inner: 1 to 8 nautical miles in steps of 1 nautical mile

    LREAL rDiffDeg = nTrZoneSizeNM / 60.0;  // 1 NM == 1/60 degree
    pROA->xInnerNE.xPosF.fLON = CAisLib::LonToLon(pROA->xRectNE.xPosF.fLON - rDiffDeg);
    pROA->xInnerNE.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectNE.xPosF.fLAT - rDiffDeg);

    pROA->xInnerNW.xPosF.fLON = CAisLib::LonToLon(pROA->xRectNW.xPosF.fLON + rDiffDeg);
    pROA->xInnerNW.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectNW.xPosF.fLAT - rDiffDeg);

    pROA->xInnerSE.xPosF.fLON = CAisLib::LonToLon(pROA->xRectSE.xPosF.fLON - rDiffDeg);
    pROA->xInnerSE.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectSE.xPosF.fLAT + rDiffDeg);

    pROA->xInnerSW.xPosF.fLON = CAisLib::LonToLon(pROA->xRectSW.xPosF.fLON + rDiffDeg);
    pROA->xInnerSW.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectSW.xPosF.fLAT + rDiffDeg);

    // 1 NM == 1/60 degree, outer zone size is fixed to 5NM
    rDiffDeg = TRZONE_SIZE_DFLT / 60.0;
    pROA->xOuterNE.xPosF.fLON = CAisLib::LonToLon(pROA->xRectNE.xPosF.fLON + rDiffDeg);
    pROA->xOuterNE.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectNE.xPosF.fLAT + rDiffDeg);

    pROA->xOuterNW.xPosF.fLON = CAisLib::LonToLon(pROA->xRectNW.xPosF.fLON - rDiffDeg);
    pROA->xOuterNW.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectNW.xPosF.fLAT + rDiffDeg);

    pROA->xOuterSE.xPosF.fLON = CAisLib::LonToLon(pROA->xRectSE.xPosF.fLON + rDiffDeg);
    pROA->xOuterSE.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectSE.xPosF.fLAT - rDiffDeg);

    pROA->xOuterSW.xPosF.fLON = CAisLib::LonToLon(pROA->xRectSW.xPosF.fLON - rDiffDeg);
    pROA->xOuterSW.xPosF.fLAT = CAisLib::LatToLat(pROA->xRectSW.xPosF.fLAT - rDiffDeg);

    CAisLib::CalcGridLowPosByFLOAT(&pROA->xOuterNE);
    CAisLib::CalcGridLowPosByFLOAT(&pROA->xOuterNW);
    CAisLib::CalcGridLowPosByFLOAT(&pROA->xOuterSE);
    CAisLib::CalcGridLowPosByFLOAT(&pROA->xOuterSW);

    CAisLib::CalcGridLowPosByFLOAT(&pROA->xInnerNE);
    CAisLib::CalcGridLowPosByFLOAT(&pROA->xInnerNW);
    CAisLib::CalcGridLowPosByFLOAT(&pROA->xInnerSE);
    CAisLib::CalcGridLowPosByFLOAT(&pROA->xInnerSW);

    DEBUG_LOG("InnerOuter-end] inner, NE:%.2f,%.2f SW:%.2f,%.2f, NW:%.2f,%.2f SE:%.2f,%.2f\r\n",
        pROA->xInnerNE.xPosF.fLON, pROA->xInnerNE.xPosF.fLAT,
        pROA->xInnerSW.xPosF.fLON, pROA->xInnerSW.xPosF.fLAT,
        pROA->xInnerNW.xPosF.fLON, pROA->xInnerNW.xPosF.fLAT,
        pROA->xInnerSE.xPosF.fLON, pROA->xInnerSE.xPosF.fLAT);

    DEBUG_LOG("InnerOuter-end] outer, NE:%.2f,%.2f SW:%.2f,%.2f, NW:%.2f,%.2f SE:%.2f,%.2f\r\n",
        pROA->xOuterNE.xPosF.fLON, pROA->xOuterNE.xPosF.fLAT,
        pROA->xOuterSW.xPosF.fLON, pROA->xOuterSW.xPosF.fLAT,
        pROA->xOuterNW.xPosF.fLON, pROA->xOuterNW.xPosF.fLAT,
        pROA->xOuterSE.xPosF.fLON, pROA->xOuterSE.xPosF.fLAT);
}

/**
 * @brief Check if ROA is inside 3 corner
 * @param pAllPosP ROA corner position
 * @return true if ROA is inside 3 corner, false otherwise
 */
bool CROSMgr::IsROAInside3Corner(POS_ALLH *pAllPosP)
{
    int nCnt = 0;
    for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            // Check if one corner of the new ROA is within 8 NM of two or more corners of existing ROAs
            if(CGps::GetDistanceByFLOAT(pAllPosP->xPosF.fLAT, pAllPosP->xPosF.fLON, m_vAllRoaDATA[i].xRectNE.xPosF.fLAT, m_vAllRoaDATA[i].xRectNE.xPosF.fLON, DIST_UNIT_NM) <= 8.0f) nCnt++;
            if(CGps::GetDistanceByFLOAT(pAllPosP->xPosF.fLAT, pAllPosP->xPosF.fLON, m_vAllRoaDATA[i].xRectNW.xPosF.fLAT, m_vAllRoaDATA[i].xRectNW.xPosF.fLON, DIST_UNIT_NM) <= 8.0f) nCnt++;
            if(CGps::GetDistanceByFLOAT(pAllPosP->xPosF.fLAT, pAllPosP->xPosF.fLON, m_vAllRoaDATA[i].xRectSE.xPosF.fLAT, m_vAllRoaDATA[i].xRectSE.xPosF.fLON, DIST_UNIT_NM) <= 8.0f) nCnt++;
            if(CGps::GetDistanceByFLOAT(pAllPosP->xPosF.fLAT, pAllPosP->xPosF.fLON, m_vAllRoaDATA[i].xRectSW.xPosF.fLAT, m_vAllRoaDATA[i].xRectSW.xPosF.fLON, DIST_UNIT_NM) <= 8.0f) nCnt++;
        }

        if(nCnt >= 2)
        {
            DEBUG_LOG("IsROAInside3Corner] overwrap with %d, cnt:%d, newNE:%.2f,%.2f newSW:%.2f,%.2f, oldNE:%.2f,%.2f oldSW:%.2f,%.2f\r\n",
                i, nCnt,
                pAllPosP->xPosF.fLON, pAllPosP->xPosF.fLAT,
                pAllPosP->xPosF.fLON, pAllPosP->xPosF.fLAT,
                m_vAllRosDATA[i].xPointNE.xPosF.fLON, m_vAllRosDATA[i].xPointNE.xPosF.fLAT,
                m_vAllRosDATA[i].xPointSW.xPosF.fLON, m_vAllRosDATA[i].xPointSW.xPosF.fLAT);
            return true;
        }
    }
    return false;
}

/**
 * @brief Check if ROA 8nm rule is violated
 * @param pROA ROA data to check
 * @return true if rule is violated, false otherwise
 */
bool CROSMgr::CheckROA8nmRule(xROADATA *pROA)
{
    //------------------------------------------------------------------------------------
    // refer to ITU-R M.1371-5 Annex2 4.1.5
    // The mobile AIS station should ignore any channel management command,
    // when there are three different regional operating settings with adjacent
    // regional operating areas, their corners within eight nautical miles to each other.
    return (!IsROAInside3Corner(&pROA->xRectNE) &&
            !IsROAInside3Corner(&pROA->xRectNW) &&
            !IsROAInside3Corner(&pROA->xRectSE) &&
            !IsROAInside3Corner(&pROA->xRectSW));
}

/**
 * @brief Check if ROA size is valid (within 20NM ~ 200NM)
 * @param pRoaData ROA data to check
 */
int CROSMgr::CheckRoaSizeIsValid(xROADATA *pRoaData)
{
    //---------------------------------------------------------------------------------------------------------
    // refer to ITU-R M.1371-5 Annex2 4.1.5
    // Regions should be as large as possible. For practical purposes, in order to provide safe transitions
    // between regions, these should be no smaller than 20 NM but not larger than 200 NM on any boundary side.

    FLOAT fDistX;
    FLOAT fDistY;

    fDistX = CGps::GetDistanceByFLOAT(pRoaData->xRectNE.xPosF.fLAT, pRoaData->xRectNE.xPosF.fLON, pRoaData->xRectNW.xPosF.fLAT, pRoaData->xRectNW.xPosF.fLON, DIST_UNIT_NM);
    fDistY = CGps::GetDistanceByFLOAT(pRoaData->xRectNE.xPosF.fLAT, pRoaData->xRectNE.xPosF.fLON, pRoaData->xRectSE.xPosF.fLAT, pRoaData->xRectSE.xPosF.fLON, DIST_UNIT_NM);

    // Check if region is smaller than 20NM
    if(fDistX <  20.0f || fDistY <  20.0f)
    {
        DEBUG_LOG("CheckROSbyPI] ignore : invalid ROS size too small, NE:%.2f,%.2f SW:%.2f,%.2f, distX:%.2f, distY:%.2f\r\n",
                pRoaData->xRectNE.xPosF.fLON, pRoaData->xRectNE.xPosF.fLAT,
                pRoaData->xRectSW.xPosF.fLON,  pRoaData->xRectSW.xPosF.fLAT,
                fDistX, fDistY);

        return(ROS_ERR_TOO_SMALL_ROA);
    }

    // Check if region is larger than 200NM
    if(fDistX > 200.0f || fDistY > 200.0f)
    {
        DEBUG_LOG("CheckROSbyPI] ignore : invalid ROS size too long, NE:%.2f,%.2f SW:%.2f,%.2f, distX:%.2f, distY:%.2f\r\n",
                pRoaData->xRectNE.xPosF.fLON, pRoaData->xRectNE.xPosF.fLAT,
                pRoaData->xRectSW.xPosF.fLON, pRoaData->xRectSW.xPosF.fLAT,
                fDistX, fDistY);
        return(ROS_ERR_TOO_LARGE_ROA);
    }

    return(ROS_ERR_NO_ERR);
}

bool CROSMgr::CheckSameRoaExist(xROSDATA *pRosData, xROADATA *pRoaData)
{
    for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            if(ROAOverlapRule1(&m_vAllRoaDATA[i], &pRoaData->xRectNE, &pRoaData->xRectSW, &pRoaData->xRectNW, &pRoaData->xRectSE))
                return true;
        }
    }
    return false;
}

/**
 * @brief Remove overlapped ROA
 * @param pRosData ROS data to check
 * @param pRoaData ROA data to check
 */
void CROSMgr::RemoveOverlappedROA(xROSDATA *pRosData, xROADATA *pRoaData)
{
    for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            // --------------------------------------------------------------------------------------------------------
            // ITU-R M.1371-5 Annex2 4.1.8
            // If the regional operating area of the new, accepted regional operating setting overlaps in part or 
            // in total or matches the regional operating areas of one or more older regional operating settings, 
            // this or these older regional operating settings should be erased from the memory. 
            // The regional operating area of the new, accepted regional operating setting may be neighbouring tightly 
            // and may thus have the same boundaries as older regional operating settings. This should not lead to 
            // the erasure of the older regional operating settings.
            if(CheckROAOverlapMatch(&m_vAllRoaDATA[i], pRoaData))
            {
                DEBUG_LOG("RemoveOverwrapROS] overlapped with #%d, newNE:%d,%d(%.2f,%.2f) newSW:%d,%d(%.2f,%.2f), oldNE:%d,%d(%.2f,%.2f) oldSW:%d,%d(%.2f,%.2f)\r\n",
                    i,
                    pRoaData->xRectNE.xPosL.nLON, pRoaData->xRectNE.xPosL.nLAT, pRoaData->xRectNE.xPosF.fLON, pRoaData->xRectNE.xPosF.fLAT,
                    pRoaData->xRectSW.xPosL.nLON, pRoaData->xRectSW.xPosL.nLAT, pRoaData->xRectSW.xPosF.fLON, pRoaData->xRectSW.xPosF.fLAT,
                    m_vAllRoaDATA[i].xRectNE.xPosL.nLON, m_vAllRoaDATA[i].xRectNE.xPosL.nLAT, m_vAllRoaDATA[i].xRectNE.xPosF.fLON, m_vAllRoaDATA[i].xRectNE.xPosF.fLAT,
                    m_vAllRoaDATA[i].xRectSW.xPosL.nLON, m_vAllRoaDATA[i].xRectSW.xPosL.nLAT, m_vAllRoaDATA[i].xRectSW.xPosF.fLON, m_vAllRoaDATA[i].xRectSW.xPosF.fLAT);
                ClearOneRosData(i);
            }
        }
    }
}

/**
 * @brief Get number of valid ROS
 * @return Number of valid ROS
 */ 
int CROSMgr::GetNumValidROS(void)
{
    int  nCounter = 0;
    for(int idx = 0; idx < MAX_ROS_DATA_SIZE; idx++)
    {
        if(m_vAllRosDATA[idx].bValidMode == MODE_VAL_ON)
            ++nCounter;
    }
    return(nCounter);
}

bool CROSMgr::ROAOverlapRule1(xROADATA *pROA, POS_ALLH *pNE, POS_ALLH *pSW, POS_ALLH *pNW, POS_ALLH *pSE)
{
      // -------------------------------------------------------------------------------------------------------
      // 기존 ROS의 ROA와 수신되는 ROS의 ROA가 완전히 동일한지 검사
      // -------------------------------------------------------------------------------------------------------
      return ((pNE->xPosL.nLON == pROA->xRectNE.xPosL.nLON && pNE->xPosL.nLAT == pROA->xRectNE.xPosL.nLAT) &&
              (pSW->xPosL.nLON == pROA->xRectSW.xPosL.nLON && pSW->xPosL.nLAT == pROA->xRectSW.xPosL.nLAT) &&
              (pNW->xPosL.nLON == pROA->xRectNW.xPosL.nLON && pNW->xPosL.nLAT == pROA->xRectNW.xPosL.nLAT) &&
              (pSE->xPosL.nLON == pROA->xRectSE.xPosL.nLON && pSE->xPosL.nLAT == pROA->xRectSE.xPosL.nLAT));
}
bool CROSMgr::ROAOverlapRule2(xROADATA *pROA, POS_ALLH *pNE, POS_ALLH *pSW, POS_ALLH *pNW, POS_ALLH *pSE)
{
    // -------------------------------------------------------------------------------------------------------
    // new ROA의 4개의 꼭지점이 하나라도 old ROA의 영역내에 위치하게 될 때.
    // old ROA의 4개의 꼭지점이 하나라도 new ROA의 영역내에 위채하게 될 때.
    // -------------------------------------------------------------------------------------------------------

    if(CheckInsideRectangleXZ(&pNE->xPosG, &pROA->xRectNE.xPosG, &pROA->xRectSW.xPosG) ||
        CheckInsideRectangleXZ(&pSW->xPosG, &pROA->xRectNE.xPosG, &pROA->xRectSW.xPosG) ||
        CheckInsideRectangleXZ(&pNW->xPosG, &pROA->xRectNE.xPosG, &pROA->xRectSW.xPosG) ||
        CheckInsideRectangleXZ(&pSE->xPosG, &pROA->xRectNE.xPosG, &pROA->xRectSW.xPosG))
        return TRUE;

    if(CheckInsideRectangleXZ(&pROA->xRectNE.xPosG, &pNE->xPosG, &pSW->xPosG) ||
        CheckInsideRectangleXZ(&pROA->xRectSW.xPosG, &pNE->xPosG, &pSW->xPosG) ||
        CheckInsideRectangleXZ(&pROA->xRectNW.xPosG, &pNE->xPosG, &pSW->xPosG) ||
        CheckInsideRectangleXZ(&pROA->xRectSE.xPosG, &pNE->xPosG, &pSW->xPosG))
        return TRUE;

    return FALSE;
}
bool CROSMgr::ROAOverlapRule3(xROADATA *pROA, POS_ALLH *pNE, POS_ALLH *pSW, POS_ALLH *pNW, POS_ALLH *pSE)
{
    // -------------------------------------------------------------------------------------------------------
    // new ROA의 두 경도가 old ROA의 두경도 사이에 있고, 동시에 old ROA의 위도가 new ROA의 위도사이에 사이에 있을 때.
    // old ROA의 두 경도가 new ROA의 두경도 사이에 있고, 동시에 new ROA의 위도가 old ROA의 위도사이에 사이에 있을 때.
    // -------------------------------------------------------------------------------------------------------

    if(CGps::CheckLonInGridLonRange(pNE->xPosG.nLON, pROA->xRectSW.xPosG.nLON, pROA->xRectNE.xPosG.nLON, 1) &&
        CGps::CheckLonInGridLonRange(pSW->xPosG.nLON, pROA->xRectSW.xPosG.nLON, pROA->xRectNE.xPosG.nLON, 1) &&
        CGps::CheckLatInGridLatRange(pROA->xRectNE.xPosG.nLAT, pSW->xPosG.nLAT, pNE->xPosG.nLAT         , 1) &&
        CGps::CheckLatInGridLatRange(pROA->xRectSW.xPosG.nLAT, pSW->xPosG.nLAT, pNE->xPosG.nLAT         , 1))
        return TRUE;

    if(CGps::CheckLonInGridLonRange(pROA->xRectSW.xPosG.nLON, pSW->xPosG.nLON, pNE->xPosG.nLON         , 1) &&
        CGps::CheckLonInGridLonRange(pROA->xRectNE.xPosG.nLON, pSW->xPosG.nLON, pNE->xPosG.nLON         , 1) &&
        CGps::CheckLatInGridLatRange(pNE->xPosG.nLAT, pROA->xRectSW.xPosG.nLAT, pROA->xRectNE.xPosG.nLAT, 1) &&
        CGps::CheckLatInGridLatRange(pSW->xPosG.nLAT, pROA->xRectSW.xPosG.nLAT, pROA->xRectNE.xPosG.nLAT, 1))
        return TRUE;
    return FALSE;
}
bool CROSMgr::ROAOverlapRule4(xROADATA *pOldROA, xROADATA *pNewROA)
{
    // -------------------------------------------------------------------------------------------------------
    // old ROA가 new ROA 영역 안에 있을 때
    // 이 때는 old ROA는 메모리에서 삭제.
    // -------------------------------------------------------------------------------------------------------
    return (CheckInsideRectangleXZ(&pOldROA->xRectNE.xPosG, &pNewROA->xRectNE.xPosG, &pNewROA->xRectSW.xPosG) &&
            CheckInsideRectangleXZ(&pOldROA->xRectSW.xPosG, &pNewROA->xRectNE.xPosG, &pNewROA->xRectSW.xPosG));
}

bool CROSMgr::CheckROAOverlapMatch(xROADATA *pOldROA, xROADATA *pNewROA)
{
    // -------------------------------------------------------------------------------------------------------
    // 저장된 ROS의 ROA와 새 ROS의 ROA와 겹치거나 동일한지 여부 검사.
    // -------------------------------------------------------------------------------------------------------

    return (ROAOverlapRule1(pOldROA, &pNewROA->xRectNE, &pNewROA->xRectSW, &pNewROA->xRectNW, &pNewROA->xRectSE) ||
            ROAOverlapRule2(pOldROA, &pNewROA->xRectNE, &pNewROA->xRectSW, &pNewROA->xRectNW, &pNewROA->xRectSE) ||
            ROAOverlapRule3(pOldROA, &pNewROA->xRectNE, &pNewROA->xRectSW, &pNewROA->xRectNW, &pNewROA->xRectSE) ||
            ROAOverlapRule4(pOldROA, pNewROA));
}

/**
 * @brief Check if ROS should be ignored by PI
 * @param pROS ROS data to check
 * @param pROA ROA data to check
 * @return ROS_ERR_NO_ERR if no error, ROS_ERR_ROA_OVERLAP_2HOURS if overlap within 2 hours
 */
int CROSMgr::CheckIgnoreRosByPI(xROSDATA *pROS, xROADATA *pROA)
{
    // ---------------------------------------------------------------------------------------------------------------------
    // ITU-R M.1371-5 Annex2 4.1.8
    // The mobile AIS station should not accept a new regional operating setting which was input to it
    // from a shipborne system command, i.e. via the Presentation Interface, if the regional operating area
    // of this new regional operating setting partly or totally overlaps or matches the regional operating
    // area of any of the stored regional operating settings, which were received from a base station either
    // by Message 22 or by DSC telecommand within the last two hours.

    int nDiffSec;
    int i;

    if(pROS->bRosSource == ROS_SRC_PI)
    {
        for(i = 0; i < MAX_ROS_DATA_SIZE ; i++)
        {
            if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
            {
                if(m_vAllRosDATA[i].bRosSource == ROS_SRC_MSG_BROAD ||
                    m_vAllRosDATA[i].bRosSource == ROS_SRC_MSG_ADDRD ||
                    m_vAllRosDATA[i].bRosSource == ROS_SRC_DSC)
                {
                    if(CheckROAOverlapMatch(&(m_vAllRoaDATA[i]), pROA))
                    {
                        nDiffSec = CAisLib::GetDiffTimeSeconds(&pROS->xRcvTime, &m_vAllRosDATA[i].xRcvTime);
                        if(nDiffSec <= (DTTM_ONE_HOUR_SECONDS * 2))
                        {
                            DEBUG_LOG("CheckROSbyPI] ignore by 2H rule, overwrap:%d, newNE:%.2f,%.2f newSW:%.2f,%.2f, oldNE:%.2f,%.2f oldSW:%.2f,%.2f\r\n",
                                    i,
                                    pROS->xPointNE.xPosF.fLON, pROS->xPointNE.xPosF.fLAT,
                                    pROS->xPointSW.xPosF.fLON,   pROS->xPointSW.xPosF.fLAT,
                                    m_vAllRosDATA[i].xPointNE.xPosF.fLON, m_vAllRosDATA[i].xPointNE.xPosF.fLAT,
                                    m_vAllRosDATA[i].xPointSW.xPosF.fLON, m_vAllRosDATA[i].xPointSW.xPosF.fLAT);
                            return ROS_ERR_ROA_OVERLAP_2HOURS;
                        }
                    }
                }
            }
        }
    }
    return ROS_ERR_NO_ERR;
}

bool CROSMgr::CheckROADataOver500NM(void)
{
    // -------------------------------------------------------------------------------------------------------------------
    // ITU-R 1371-5 Ann2 4.1.8
    // The mobile AIS station should constantly check, if the nearest boundary of the regional operating
    // area of any stored regional operating setting is more than 500 nautical miles away from the current
    // position of own station, or if any stored regional operating setting was older than 24 hours. Any
    // stored regional operating setting which fulfils any one of these conditions should be erased from the memory.
    // -------------------------------------------------------------------------------------------------------------------
    bool bChgSetup = FALSE;
    REAL rDist = 0;

    for(int i = 0 ; i < MAX_ROS_DATA_SIZE; i++)
    {
        rDist = 0;

        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            if((rDist = GetDistanceNearestROABoundary(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, &m_vAllRoaDATA[i])) > 500.0)
            {
                ClearOneRosData(i);
                bChgSetup = true;
            }
        }
    }

    return bChgSetup;
}

/**
 * @brief Check if ROS data is older than 24 hours
 * @param pCurrUTC Current UTC time
 * @return True if ROS data is older than 24 hours, false otherwise
 */
bool CROSMgr::CheckROSData24Hours(SYS_DATE_TIME *pCurrUTC)
{
    int nElapSec = 0;
    for(int i = 0 ; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            // ------------------------------------------------------------------------------------------------------------
            // ITU-R 1371-5 Ann2 4.1.8
            // The mobile AIS station should constantly check, if the nearest boundary of the regional operating
            // area of any stored regional operating setting is more than 500 nautical miles away from the current
            // position of own station, or if any stored regional operating setting was older than 24 hours. Any
            // stored regional operating setting which fulfils any one of these conditions should be erased from the memory.

            nElapSec = cTimerSys::getInst()->GetTimeDiffSec(m_vAllRosDATA[i].dwRcvSysSec);
            if(nElapSec > DTTM_ONE_DAY_SECONDS)   // 24 hours
            {
                DEBUG_LOG("EraseRos] Check24Hours, erase 24H old ROS, #%d, elapSec : %d, v:%d(%d-%d-%d,%02d:%02d:%02d) ~ v:%d(%d-%d-%d,%02d:%02d:%02d) rcvSec:%d, s:%d\r\n",
                        i, nElapSec,
                        m_vAllRosDATA[i].xRcvTime.nValid, m_vAllRosDATA[i].xRcvTime.xDate.nYear, m_vAllRosDATA[i].xRcvTime.xDate.nMon, m_vAllRosDATA[i].xRcvTime.xDate.nDay,
                        m_vAllRosDATA[i].xRcvTime.xTime.nHour, m_vAllRosDATA[i].xRcvTime.xTime.nMin, m_vAllRosDATA[i].xRcvTime.xTime.nSec,
                        pCurrUTC->nValid, pCurrUTC->xDate.nYear, pCurrUTC->xDate.nMon, pCurrUTC->xDate.nDay, pCurrUTC->xTime.nHour, pCurrUTC->xTime.nMin, pCurrUTC->xTime.nSec,
                        m_vAllRosDATA[i].dwRcvSysSec, cTimerSys::getInst()->GetCurTimerSec());

                ClearOneRosData(i);
                return true;
            }
        }
    }
    return false;
}

bool CROSMgr::CheckRemoveObsoleteROSData(void)
{
    // ----------------------------------
    // ITU-R 1371-5 Ann2 4.1.8
    // Priority of channel management commands and clearing of stored regional operating settings

    if(CheckROADataOver500NM() || CheckROSData24Hours(&cShip::getOwnShipInst()->xSysTime))
    {
        CSetupMgr::getInst()->ReserveToSaveRosConfigData();
        CMKD::getInst()->SendAllACAACStoPI();
        return true;
    }
    return false;
}

REAL CROSMgr::GetDistanceBoundary(FLOAT fShipLat, FLOAT fShipLon, POS_ALLH *pROA1, POS_ALLH *pROA2)
{
    // -------------------------------------------------------------------------------------------------------
    // 선박의 현위치에서, 두개의 ROA 좌표로 연결된 선에서 가장 가까운 거리를 계산
    // (ROA_SW) +-----------------+ (ROA_NE)
    //                   |
    //                   |
    //                   x
    // -------------------------------------------------------------------------------------------------------

    LREAL rDistance;
    LREAL rDstX, rDstY, rDstZ;
    LREAL rCrsX, rCrsY, rCrsZ;
    LREAL rDffX, rDffY;
    LREAL rShipLat = (LREAL)fShipLat;
    LREAL rShipLon = (LREAL)fShipLon;

    CGps::GetDistanceAndCourse((LREAL)pROA1->xPosF.fLAT, (LREAL)pROA1->xPosF.fLON, rShipLat, rShipLon, &rDstX, &rCrsX, DIST_UNIT_NM);
    CGps::GetDistanceAndCourse((LREAL)pROA2->xPosF.fLAT, (LREAL)pROA2->xPosF.fLON, rShipLat, rShipLon, &rDstY, &rCrsY, DIST_UNIT_NM);
    CGps::GetDistanceAndCourse((LREAL)pROA1->xPosF.fLAT, (LREAL)pROA1->xPosF.fLON, (LREAL)pROA2->xPosF.fLAT, (LREAL)pROA2->xPosF.fLON, &rDstZ, &rCrsZ, DIST_UNIT_NM);

    rDffX = fabs(rCrsX - rCrsZ);
    if(rDffX > 180.0)
        rDffX = fabs(360.0 - rDffX);

    rDffY = fabs(180.0 - (rCrsY - rCrsZ));
    if(rDffY > 180.0)
        rDffY = fabs(360.0 - rDffY);

    if(rDffX <= 90.0 && rDffY <= 90.0)
        rDistance = rDstX * sin(rDffX * (M_PI_VALUE_D / 180.0));
    else
        rDistance = (rDstX <= rDstY ? rDstX : rDstY);

    return(rDistance);
}

/**
 * @brief Get distance from ship to nearest ROA boundary
 * @param fShipLat Ship latitude
 * @param fShipLon Ship longitude
 * @param pROA ROA data
 * @return Distance from ship to nearest ROA boundary
 */
REAL CROSMgr::GetDistanceNearestROABoundary(FLOAT fShipLat, FLOAT fShipLon, xROADATA *pROA)
{
    //----------------------------------------------------------------------------------
    // Calculate the closest distance from the ship's position to the line connecting two ROA coordinates
    // Calculate the distance from the ship's position to the nearest boundary of the stored ROS Data's ROA information
    // Check boundaries in clockwise direction.

    LREAL rTmpDist;
    LREAL rMinDist;

    rTmpDist = GetDistanceBoundary(fShipLat, fShipLon, &pROA->xRectNE, &pROA->xRectNW);
    rMinDist = rTmpDist;

    rTmpDist = GetDistanceBoundary(fShipLat, fShipLon, &pROA->xRectNE, &pROA->xRectSE);
    if(rMinDist > rTmpDist)
        rMinDist = rTmpDist;

    rTmpDist = GetDistanceBoundary(fShipLat, fShipLon, &pROA->xRectSE, &pROA->xRectSW);
    if(rMinDist > rTmpDist)
        rMinDist = rTmpDist;

    rTmpDist = GetDistanceBoundary(fShipLat, fShipLon, &pROA->xRectSW, &pROA->xRectNW);
    if(rMinDist > rTmpDist)
        rMinDist = rTmpDist;

    return(rMinDist);
}

int CROSMgr::GetNearestROA(POS_GRFP *pShipPos, int nIndexX, int nIndexY)
{
    // -------------------------------------------------------------------------------------------------------
    // 자선에서 두개의 영역중에 가장 가까운 영역의 ros index를 계산
    // 거리를 이용
    // -------------------------------------------------------------------------------------------------------

    LREAL rDstX, rDstY;

    rDstX = GetDistanceNearestROABoundary(pShipPos->xPosF.fLAT, pShipPos->xPosF.fLON, &m_vAllRoaDATA[nIndexX]);
    rDstY = GetDistanceNearestROABoundary(pShipPos->xPosF.fLAT, pShipPos->xPosF.fLON, &m_vAllRoaDATA[nIndexY]);

    if(rDstX <= rDstY)
        return(nIndexX);

    return nIndexY;
}

int CROSMgr::GetNearestROA2(POS_GRFP *pShipPos, int nTrRoaType, int nIndexX, int nIndexY)
{
    // -------------------------------------------------------------------------------------------------------
    // 자선에서 두개의 영역중에 가장 가까운 영역의 ros index를 계산
    // 붙어있는 ROS 영역의 좌표를 이용.
    // -------------------------------------------------------------------------------------------------------

    if (nIndexX != ROS_IDX_NULL)
    {
        if (nTrRoaType == ROA_UP || nTrRoaType == ROA_DOWN)     // 붙어있는 영역의 경도를 비교
        {
            if (CGps::CheckLonInGridLonRange(pShipPos->xPosG.nLON, m_vAllRoaDATA[nIndexX].xRectSW.xPosG.nLON,  m_vAllRoaDATA[nIndexX].xRectNE.xPosG.nLON, 1))
                return(nIndexX);
        }
        if (nTrRoaType == ROA_LEFT || nTrRoaType == ROA_RIGHT)  // 붙어있는 영역의 위도를 비교
        {
            if (CGps::CheckLatInGridLatRange(pShipPos->xPosG.nLAT, m_vAllRoaDATA[nIndexX].xRectSW.xPosG.nLAT, m_vAllRoaDATA[nIndexX].xRectNE.xPosG.nLAT, 1))
                return(nIndexX);
        }
    }

    if (nIndexY != ROS_IDX_NULL)
    {
        if (nTrRoaType == ROA_UP || nTrRoaType == ROA_DOWN)     // 붙어있는 영역의 경도를 비교
        {
            if (CGps::CheckLonInGridLonRange(pShipPos->xPosG.nLON, m_vAllRoaDATA[nIndexY].xRectSW.xPosG.nLON,  m_vAllRoaDATA[nIndexY].xRectNE.xPosG.nLON, 1))
                return(nIndexX);
        }
        if (nTrRoaType == ROA_LEFT || nTrRoaType == ROA_RIGHT)  // 붙어있는 영역의 위도를 비교
        {
            if (CGps::CheckLatInGridLatRange(pShipPos->xPosG.nLAT, m_vAllRoaDATA[nIndexY].xRectSW.xPosG.nLAT, m_vAllRoaDATA[nIndexY].xRectNE.xPosG.nLAT, 1))
                return(nIndexX);
        }
    }

    return(ROS_IDX_HIGHSEA);
}

int CROSMgr::CheckInsideOverlapOuterRegion(POS_GRFP *pShipPos, int nExceptRosIndex)
{
    // -------------------------------------------------------------------------------------------------------
    // Outer영역이 겹치는 부분이 있다면 그 ros index를 return. 만약 없다면 ROS_IDX_HIGHSEA를 return.
    // -------------------------------------------------------------------------------------------------------
    for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if (i != nExceptRosIndex && m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            // 자선이 속해 있는 나머지 Outer Zone을 검사
            if (CheckInsideRectangleXX(&pShipPos->xPosG, &m_vAllRoaDATA[i].xOuterNE.xPosG, &m_vAllRoaDATA[i].xOuterSW.xPosG))
                return(i);
        }
    }

    return(ROS_IDX_HIGHSEA);
}

int CROSMgr::CheckFromInnerZoneToTrZone(POS_GRFP *pShipPos, xROADATA *pROA)
{
    // -------------------------------------------------------------------------------------------------------
    // 선박이 현재 Entering Inner_Zone에 있을 때, Entering Inner_Zone -> Tr_Zone으로 벗어났는 지를 검사.
    // 항상 비교하는 순서에 유의할 것!!
    // -------------------------------------------------------------------------------------------------------

    if(!CheckInsideRectangleXX(&pShipPos->xPosG, &pROA->xInnerNE.xPosG, &pROA->xInnerSW.xPosG) &&
        CheckInsideRectangleXX(&pShipPos->xPosG, &pROA->xRectNE.xPosG , &pROA->xRectSW.xPosG))
        return 1;

    return 0;
}

int CROSMgr::IsInsidePolygon(POS_GRID *pShipPos, POS_GRID *pPolygon, int nPoints)
{
    // -------------------------------------------------------------------------------------------------------
    // 다각형의 꼭지점과 선분에 접하여 있는 경우는 FALSE(한쪽이 음수?) or TRUE(둘다 양수?)
    // -------------------------------------------------------------------------------------------------------

    int  nCounter = 0;
    REAL rQ;
    POS_GRID *pQ1 = &pPolygon[0];
    POS_GRID *pQ2;

    pQ1 = pPolygon;

    for (int i = 1; i <= nPoints; i++)
    {
        pQ2 = pPolygon + (i % nPoints);

        if (pShipPos->nLAT > RUN_MIN_GET_MACRO(pQ1->nLAT, pQ2->nLAT))               // y-intersect
        {
            if (pShipPos->nLAT <= RUN_MAX_GET_MACRO(pQ1->nLAT, pQ2->nLAT))          // y-intersect(=single)
            {
                if (pShipPos->nLON <= RUN_MAX_GET_MACRO(pQ1->nLON, pQ2->nLON))      // x-intersect
                {
                    if (pQ1->nLAT != pQ2->nLAT)                                     // not parallel
                    {
                        rQ = (REAL)(pShipPos->nLAT - pQ1->nLAT) * (REAL)(pQ2->nLON - pQ1->nLON) / (REAL)(pQ2->nLAT - pQ1->nLAT) + (REAL)pQ1->nLON;  // x-intersect;
                        if (pQ1->nLON == pQ2->nLON || pShipPos->nLON <= (LGRID)rQ)  // p->nLON <= RUN_MAX_GET_MACRO(pQ1->nLON, pQ2->nLON)
                            nCounter++;
                    }
                }
            }
        }

        pQ1 = pQ2;
    }

    if ((nCounter % 2) == 0)
        return 0;

    return 1;
}

/**
 * @brief Check if current region is adjacent to another region
 * @param pShipPos Ship position
 * @param nCurrROS Current ROS index
 * @param nTrRoaType TR zone type
 * @return Adjacent ROS index
 */
int CROSMgr::CheckCurRegionToAdjacentRegion(POS_GRFP *pShipPos, int nCurrROS, int nTrRoaType)
{
    // -------------------------------------------------------------------------------------------------------
    // Find the closest Region adjacent to the current Region and return its index.
    // 1. Find the ROS area most adjacent to that side.
    // 2. For UP/DOWN, compare the ship's longitude; for LEFT/RIGHT, compare the ship's latitude.
    //
    // There can be up to 2 areas adjacent to the current Region.
    // This function is always called from either TR_ZONE or INNER_ZONE. 
    // Therefore, we need to check if the m_sRosData.nRosIdx area crosses the 0-degree line.
    // (Important) When comparing latitudes and longitudes, use '>=' and '<=' to correctly identify 
    // when two areas are exactly adjacent.
    // -------------------------------------------------------------------------------------------------------

    int  nAdjRosX = ROS_IDX_NULL;
    int  nAdjRosY = ROS_IDX_NULL;

    if (nTrRoaType == ROA_UP)
    {
        for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
        {
            if (i != nCurrROS)
            {
                if (m_vAllRoaDATA[i].xRectSW.xPosG.nLAT == m_vAllRoaDATA[nCurrROS].xRectNE.xPosG.nLAT)
                {
                    if (CGps::CheckLonInGridLonRange(m_vAllRoaDATA[i].xRectSW.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectNW.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectNE.xPosG.nLON, 1) ||
                        CGps::CheckLonInGridLonRange(m_vAllRoaDATA[i].xRectSE.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectNW.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectNE.xPosG.nLON, 1))
                    {
                        if(nAdjRosX == ROS_IDX_NULL)
                            nAdjRosX = i;
                        else
                            nAdjRosY = i;
                    }
                }
            }
        }
    }

    if (nTrRoaType == ROA_DOWN)
    {
        for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
        {
            if (i != nCurrROS)
            {
                if (m_vAllRoaDATA[i].xRectNE.xPosG.nLAT == m_vAllRoaDATA[nCurrROS].xRectSW.xPosG.nLAT)
                {
                    if (CGps::CheckLonInGridLonRange(m_vAllRoaDATA[i].xRectNW.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectSW.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectSE.xPosG.nLON, 1) ||
                        CGps::CheckLonInGridLonRange(m_vAllRoaDATA[i].xRectNE.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectSW.xPosG.nLON, m_vAllRoaDATA[nCurrROS].xRectSE.xPosG.nLON, 1))
                    {
                        if(nAdjRosX == ROS_IDX_NULL)
                            nAdjRosX = i;
                        else
                            nAdjRosY = i;
                    }
                }
            }
        }
    }

    if (nTrRoaType == ROA_RIGHT)
    {
        for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
        {
            if (i != nCurrROS)
            {
                if (m_vAllRoaDATA[i].xRectSW.xPosG.nLON == m_vAllRoaDATA[nCurrROS].xRectNE.xPosG.nLON)
                {
                    if (CGps::CheckLatInGridLatRange(m_vAllRoaDATA[i].xRectNW.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectSE.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectNE.xPosG.nLAT, 1) ||
                        CGps::CheckLatInGridLatRange(m_vAllRoaDATA[i].xRectSW.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectSE.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectNE.xPosG.nLAT, 1))
                    {
                        if(nAdjRosX == ROS_IDX_NULL)
                            nAdjRosX = i;
                        else
                            nAdjRosY = i;
                    }
                }
            }
        }
    }

    if (nTrRoaType == ROA_LEFT)
    {
        for (int i = 0; i < MAX_ROS_DATA_SIZE; i++)
        {
            if (i != nCurrROS)
            {
                if (m_vAllRoaDATA[i].xRectNE.xPosG.nLON == m_vAllRoaDATA[nCurrROS].xRectSW.xPosG.nLON)
                {
                    if (CGps::CheckLatInGridLatRange(m_vAllRoaDATA[i].xRectNE.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectSW.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectNW.xPosG.nLAT, 1) ||
                        CGps::CheckLatInGridLatRange(m_vAllRoaDATA[i].xRectSE.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectSW.xPosG.nLAT, m_vAllRoaDATA[nCurrROS].xRectNW.xPosG.nLAT, 1))
                    {
                        if(nAdjRosX == ROS_IDX_NULL)
                            nAdjRosX = i;
                        else
                            nAdjRosY = i;
                    }
                }
            }
        }
    }

    if (nAdjRosX != ROS_IDX_NULL || nAdjRosY != ROS_IDX_NULL)
        return(GetNearestROA2(pShipPos, nTrRoaType, nAdjRosX, nAdjRosY));

    return(ROS_IDX_HIGHSEA);
}

/**
 * @brief Check if ship is inside TR zone
 * @param pShipPos Ship position
 * @param pROA ROA data
 * @return TR zone type
 */
int CROSMgr::CheckInsideTrZoneType(POS_GRFP *pShipPos, xROADATA *pROA)
{
    // -------------------------------------------------------------------------------------------------------
    // Check if the ship is inside Tr_Zone, and check if the ship is in UP, DOWN, RIGHT, LEFT.
    // The shape of Tr_Zone is a ladder.
    // -------------------------------------------------------------------------------------------------------
    POS_GRID vPolygon[4];

    if(CheckInsideRectangleXY(&pShipPos->xPosG, pROA->xRectNE.xPosG.nLON, pROA->xRectNE.xPosG.nLAT, pROA->xRectSW.xPosG.nLON, pROA->xInnerNW.xPosG.nLAT))
    {
        // polygon data를 Set하는 순서: ROA_NW, ROA_NE, INN_NE, INN_NW
        vPolygon[0].nLON = pROA->xRectNW.xPosG.nLON;   vPolygon[0].nLAT = pROA->xRectNW.xPosG.nLAT;
        vPolygon[1].nLON = pROA->xRectNE.xPosG.nLON;   vPolygon[1].nLAT = pROA->xRectNE.xPosG.nLAT;
        vPolygon[2].nLON = pROA->xInnerNE.xPosG.nLON;  vPolygon[2].nLAT = pROA->xInnerNE.xPosG.nLAT;
        vPolygon[3].nLON = pROA->xInnerNW.xPosG.nLON;  vPolygon[3].nLAT = pROA->xInnerNW.xPosG.nLAT;

        if(IsInsidePolygon(&pShipPos->xPosG, vPolygon, 4))
            return(ROA_UP);
    }

    if(CheckInsideRectangleXY(&pShipPos->xPosG, pROA->xRectSE.xPosG.nLON, pROA->xInnerSE.xPosG.nLAT, pROA->xRectSW.xPosG.nLON, pROA->xRectSW.xPosG.nLAT))
    {
        vPolygon[0].nLON = pROA->xInnerSW.xPosG.nLON;  vPolygon[0].nLAT = pROA->xInnerSW.xPosG.nLAT;
        vPolygon[1].nLON = pROA->xInnerSE.xPosG.nLON;  vPolygon[1].nLAT = pROA->xInnerSE.xPosG.nLAT;
        vPolygon[2].nLON = pROA->xRectSE.xPosG.nLON;   vPolygon[2].nLAT = pROA->xRectSE.xPosG.nLAT;
        vPolygon[3].nLON = pROA->xRectSW.xPosG.nLON;   vPolygon[3].nLAT = pROA->xRectSW.xPosG.nLAT;

        if(IsInsidePolygon(&pShipPos->xPosG, vPolygon, 4))
            return(ROA_DOWN);
    }

    DEBUG_LOG("CheckTRtype] checkLeft, NE:%.2f,%.2f SW:%.2f,%.2f pos:%.2f,%.2f, NE:%d,%d SW:%d,%d pos:%d,%d\r\n",
            pROA->xInnerNW.xPosF.fLON, pROA->xRectNW.xPosF.fLAT, pROA->xRectSW.xPosF.fLON, pROA->xRectSW.xPosF.fLAT,
            pShipPos->xPosF.fLON, pShipPos->xPosF.fLAT,
            pROA->xInnerNW.xPosG.nLON, pROA->xRectNW.xPosG.nLAT, pROA->xRectSW.xPosG.nLON, pROA->xRectSW.xPosG.nLAT,
            pShipPos->xPosG.nLON, pShipPos->xPosG.nLAT);

    if(CheckInsideRectangleXY(&pShipPos->xPosG, pROA->xInnerNW.xPosG.nLON, pROA->xRectNW.xPosG.nLAT, pROA->xRectSW.xPosG.nLON, pROA->xRectSW.xPosG.nLAT))
    {
        vPolygon[0].nLON = pROA->xRectNW.xPosG.nLON;   vPolygon[0].nLAT = pROA->xRectNW.xPosG.nLAT;
        vPolygon[1].nLON = pROA->xInnerNW.xPosG.nLON;  vPolygon[1].nLAT = pROA->xInnerNW.xPosG.nLAT;
        vPolygon[2].nLON = pROA->xInnerSW.xPosG.nLON;  vPolygon[2].nLAT = pROA->xInnerSW.xPosG.nLAT;
        vPolygon[3].nLON = pROA->xRectSW.xPosG.nLON;   vPolygon[3].nLAT = pROA->xRectSW.xPosG.nLAT;

        DEBUG_LOG("CheckTRtype] checkLeft-1, [0]:%d,%d [1]:%d,%d [2]:%d,%d [3]:%d,%d\r\n",
                vPolygon[0].nLON, vPolygon[0].nLAT, vPolygon[1].nLON, vPolygon[1].nLAT, vPolygon[2].nLON, vPolygon[2].nLAT, vPolygon[3].nLON, vPolygon[3].nLAT);

        if(IsInsidePolygon(&pShipPos->xPosG, vPolygon, 4))
            return(ROA_LEFT);
    }

    DEBUG_LOG("CheckTRtype] checkRight, NE:%.2f,%.2f SW:%.2f,%.2f pos:%.2f,%.2f, NE:%d,%d SW:%d,%d pos:%d,%d\r\n",
            pROA->xRectNE.xPosF.fLON, pROA->xRectNE.xPosF.fLAT, pROA->xInnerSE.xPosF.fLON, pROA->xRectSE.xPosF.fLAT,
            pShipPos->xPosF.fLON, pShipPos->xPosF.fLAT,
            pROA->xRectNE.xPosG.nLON, pROA->xRectNE.xPosG.nLAT, pROA->xInnerSE.xPosG.nLON, pROA->xRectSE.xPosG.nLAT,
            pShipPos->xPosG.nLON, pShipPos->xPosG.nLAT);

    if(CheckInsideRectangleXY(&pShipPos->xPosG, pROA->xRectNE.xPosG.nLON, pROA->xRectNE.xPosG.nLAT, pROA->xInnerSE.xPosG.nLON, pROA->xRectSE.xPosG.nLAT))
    {
        vPolygon[0].nLON = pROA->xRectNE.xPosG.nLON;   vPolygon[0].nLAT = pROA->xRectNE.xPosG.nLAT;
        vPolygon[1].nLON = pROA->xRectSE.xPosG.nLON;   vPolygon[1].nLAT = pROA->xRectSE.xPosG.nLAT;
        vPolygon[2].nLON = pROA->xInnerSE.xPosG.nLON;  vPolygon[2].nLAT = pROA->xInnerSE.xPosG.nLAT;
        vPolygon[3].nLON = pROA->xInnerNE.xPosG.nLON;  vPolygon[3].nLAT = pROA->xInnerNE.xPosG.nLAT;

        DEBUG_LOG("CheckTRtype] checkRight-1, [0]:%d,%d [1]:%d,%d [2]:%d,%d [3]:%d,%d\r\n",
            vPolygon[0].nLON, vPolygon[0].nLAT, vPolygon[1].nLON, vPolygon[1].nLAT, vPolygon[2].nLON, vPolygon[2].nLAT, vPolygon[3].nLON, vPolygon[3].nLAT);

        if(IsInsidePolygon(&pShipPos->xPosG, vPolygon, 4))
            return(ROA_RIGHT);
    }

    // In case the ship is at a vertex or on the boundary of TrZone or Inner_Zone
    return(ROA_NULL);
}

/**
 * @brief Check if ROA run mode is TR zone
 * @return true if ROA run mode is TR zone, false otherwise
 */
int CROSMgr::IsRoaRunModeTRZone(void)
{
    return (m_sRosData.nRosMode == ROS_MODE_OUTER_TR_ZONE 
            || m_sRosData.nRosMode == ROS_MODE_INNER_TR_ZONE);
}

/**
 * @brief Get distance between two ROA
 * @param pROA1 ROA data 1
 * @param pROA2 ROA data 2
 * @return Distance between two ROA
 */
REAL CROSMgr::GetDistanceROAtoROA(xROADATA *pROA1, xROADATA *pROA2)
{
    LREAL rTmpDist;
    LREAL rMinDist;

    rTmpDist = GetDistanceNearestROABoundary(pROA1->xRectNE.xPosF.fLAT, pROA1->xRectNE.xPosF.fLON, pROA2);
    rMinDist = rTmpDist;

    rTmpDist = GetDistanceNearestROABoundary(pROA1->xRectSE.xPosF.fLAT, pROA1->xRectSE.xPosF.fLON, pROA2);
    if(rMinDist > rTmpDist)
        rMinDist = rTmpDist;

    rTmpDist = GetDistanceNearestROABoundary(pROA1->xRectSW.xPosF.fLAT, pROA1->xRectSW.xPosF.fLON, pROA2);
    if(rMinDist > rTmpDist)
        rMinDist = rTmpDist;

    rTmpDist = GetDistanceNearestROABoundary(pROA1->xRectNW.xPosF.fLAT, pROA1->xRectNW.xPosF.fLON, pROA2);
    if(rMinDist > rTmpDist)
        rMinDist = rTmpDist;

    return(rMinDist);
}

/**
 * @brief Get most distant ROS from own ship
 * @return Most distant ROS index
 */
int CROSMgr::GetMostDistantROSfromOwnShip()
{
    int  nIndex = -1;
    REAL rMaxDist = 0.0;
    REAL rDist = 0.0;

    //----------------------------------------------------------------------------------
    // ITU-R M.1371-5 Annex2 4.1.8
    // If there is no free memory location, the most distant regional operating setting 
    // should be replaced by the new, accepted one.

    if(CAisLib::IsValidAisHighPOS(&(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosH)))
    {
        for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
        {
            if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
            {
                if(i != m_sRosData.nRosIdx)
                {
                    rDist = GetDistanceNearestROABoundary(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
                    									cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON,
														&m_vAllRoaDATA[i]);
                    if(rDist > rMaxDist)
                    {
                        nIndex = i;
                        rMaxDist = rDist;
                    }
                }
            }
        }
    }

    return nIndex;
}

/**
 * @brief Get most distant ROS from new ROS
 * @param pRoa ROA data
 * @return Most distant ROS index
 */
int CROSMgr::GetMostDistantROSfromROS(xROADATA *pRoa)
{
    int  nIndex = -1;
    REAL rMaxDist = 0.0;
    REAL rDist = 0.0;

    //-----------------------------------------------------------------------------------
    // ITU-R M.1371-5 Annex2 4.1.8
    // If the AIS station does not have position it should delete the area most distant 
    // from the position provided in the channel management command.
    for(int i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            if(i != m_sRosData.nRosIdx)
            {
                rDist = GetDistanceROAtoROA(pRoa, &m_vAllRoaDATA[i]);
                if(rDist > rMaxDist)
                {
                    nIndex = i;
                    rMaxDist = rDist;
                }
            }
        }
    }

    return nIndex;
}

/**
 * @brief Check if ROS mode is TR mode
 * @param nRosMode ROS mode to check
 * @return true if ROS mode is TR mode, false otherwise
 */
bool CROSMgr::IsTrMode(int nRosMode)
{
    return ((nRosMode == ROS_MODE_INNER_TR_ZONE) || (nRosMode == ROS_MODE_OUTER_TR_ZONE));
}

/**
 * @brief Check if TR mode is running
 * @return true if TR mode is running, false otherwise
 */
bool CROSMgr::IsTrModeRunning()
{
    return (IsTrMode(m_sRosData.nRosMode) && (CROSMgr::getInst()->m_sChSetup.nTxRxModeBy == TRXMODE_BY_TRZONE));
}

/**
 * @brief Check if ROS rectangle is valid
 * @param pRosData ROS data to check
 * @return true if ROS rectangle is valid, false otherwise
 */
bool CROSMgr::CheckRosRectValid(xROSDATA *pRosData)
{
    if(!CAisLib::IsValidAisLowPOS(&(pRosData->xPointNE.xPosL)) || !CAisLib::IsValidAisLowPOS(&(pRosData->xPointSW.xPosL)))
    {
        WARNING_LOG("CheckROS] pos invalid, %09d, (%d,%d) (%d,%d)\r\n",
            pRosData->dSrcMMSI,
            pRosData->xPointNE.xPosL.nLAT, pRosData->xPointNE.xPosL.nLON,
            pRosData->xPointSW.xPosL.nLAT, pRosData->xPointSW.xPosL.nLON);
        return false;
    }

    if(pRosData->xPointSW.xPosL.nLAT >= pRosData->xPointNE.xPosL.nLAT || pRosData->xPointSW.xPosL.nLON >= pRosData->xPointNE.xPosL.nLON)
    {
        WARNING_LOG("CheckROS] rect invalid, %09d, (%d,%d) (%d,%d)\r\n",
            pRosData->dSrcMMSI,
            pRosData->xPointNE.xPosL.nLAT, pRosData->xPointNE.xPosL.nLON,
            pRosData->xPointSW.xPosL.nLAT, pRosData->xPointSW.xPosL.nLON);
        return false;
    }

    return true;
}

/**
 * @brief Check if ROS data is valid
 * @param pRosData ROS data to check
 * @return true if ROS data is valid, false otherwise
 */
bool CROSMgr::CheckRosDataValid(xROSDATA *pRosData)
{
    if(!CheckRosRectValid(pRosData))
        return FALSE;

    if(!CAisLib::GetAisFreqByChannelNo(pRosData->wChannelNoA)
    	|| !CAisLib::IsAisChanneFreqValidByCH(pRosData->wChannelNoA)
		|| !CAisLib::IsAisChanneBW25ValidByCH(pRosData->wChannelNoA))
    {
        INFO_LOG("CheckROS] primaryCH invalid, %09d, %d,%d\r\n",
            pRosData->dSrcMMSI, pRosData->wChannelNoA, pRosData->wChannelNoB);
        return FALSE;
    }

    if(pRosData->bTxRxMode != TRXMODE_TXARXA_______ &&
        (!CAisLib::GetAisFreqByChannelNo(pRosData->wChannelNoB)
        	|| !CAisLib::IsAisChanneFreqValidByCH(pRosData->wChannelNoB)
			|| !CAisLib::IsAisChanneBW25ValidByCH(pRosData->wChannelNoB)))
    {
        INFO_LOG("CheckROS] secondiaryCH invalid, %09d, %d,%d\r\n",
                pRosData->dSrcMMSI, pRosData->wChannelNoA, pRosData->wChannelNoB);
        return false;
    }

    if(pRosData->bTxRxMode < TRXMODE_MIN || pRosData->bTxRxMode > TRXMODE_MAX)
    {
        INFO_LOG("CheckROS] TRX invalid, %09d, %d\r\n", pRosData->dSrcMMSI, pRosData->bTxRxMode);
        return false;
    }

    if((pRosData->bRosSource == ROS_SRC_MSG_ADDRD || pRosData->bRosSource == ROS_SRC_MSG_BROAD) && pRosData->bTxRxMode > VDL_TRXMODE_MAX)
    {
        INFO_LOG("CheckROS] TRX invalid, from VDL, %09d, %d\r\n", pRosData->dSrcMMSI, pRosData->bTxRxMode);
        return false;
    }

    if(pRosData->bRosSource == ROS_SRC_DSC &&
        (pRosData->bTxRxMode != TRXMODE_TXARXA_TXBRXB && pRosData->bTxRxMode != TRXMODE_TXARXA____RXB &&
        pRosData->bTxRxMode != TRXMODE_TXARXA_______))
    {
        INFO_LOG("CheckROS] TRX invalid, from DSC, %09d, %d\r\n", pRosData->dSrcMMSI, pRosData->bTxRxMode);
        return false;
    }

    if(pRosData->bRosSource == ROS_SRC_PI && pRosData->bTxRxMode > ACA_TRXMODE_MAX)
    {
        INFO_LOG("CheckROS] TRX invalid, from PI, %09d, %d\r\n", pRosData->dSrcMMSI, pRosData->bTxRxMode);
        return false;
    }

    if(!CSetupMgr::getInst()->IsAisBClassCS())
    {
        if(pRosData->bBandwidthA != AIS_CH_BW_25_0_KHZ || pRosData->bBandwidthB != AIS_CH_BW_25_0_KHZ)
        {
            INFO_LOG("CheckROS] bandwidth invalid, %09d, %d,%d\r\n",
                pRosData->dSrcMMSI, pRosData->bBandwidthA, pRosData->bBandwidthB);
            return false;
        }
    }

    return true;
}

/**
 * @brief Get adjacent ROS
 * @param nCurRos Current ROS index
 * @return Adjacent ROS index
 */
int CROSMgr::GetAdjacentRos(int nCurRos)
{
    int nTrZoneType = CheckInsideTrZoneType(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid, &m_vAllRoaDATA[nCurRos]);
    return CheckCurRegionToAdjacentRegion(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid, nCurRos, nTrZoneType);
}

/**
 * @brief Get outer ROS own ship located
 * @param nExceptRos ROS index to exclude
 * @return Outer ROS index
 */
int CROSMgr::GetOuterRosOwnShipLocated(int nExceptRos)
{
    // OUTER_TR 이 겹치는 ROS 들은 최대 2개 존재할수있다
    #define MAX_NUM_OUTER    2

    int nCnt = 0;
    int pRosList[MAX_NUM_OUTER];
    int nNextRosIdx = ROS_IDX_NULL;

    for (int idx = 0; idx < MAX_ROS_DATA_SIZE ; idx++)
    {
        if (nExceptRos != ROS_IDX_NULL && idx == nExceptRos)
            continue;

        if (CheckOwnShipInsideOfOuterTrZone(idx))
        {
            pRosList[nCnt] = idx;
            if(++nCnt >= MAX_NUM_OUTER)
                break;
        }
    }

    if (nCnt > 0)
    {
        //------------------------
        // OUTER_TR 모드로 전환
        //------------------------

        if (nCnt >= 2)
            nNextRosIdx = GetNearestROA(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid, pRosList[0], pRosList[1]);    // 자선이 두개 이상의 OUTER_TR 영역에 속할때 가장 가까운 영역의 ROS 를 취한다.
        else if (nCnt == 1)
            nNextRosIdx = pRosList[0];
    }

    return nNextRosIdx;
}

/**
 * @brief Check if own ship is inside of outer zone
 * @param niRos ROS index
 * @return true if own ship is inside of outer zone, false otherwise
 */
bool CROSMgr::CheckOwnShipInsideOfOuterTrZone(int niRos)
{
    if (m_vAllRosDATA[niRos].bValidMode == MODE_VAL_ON)
    {
        return CheckInsideRectangleXX(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG, 
                                    &m_vAllRoaDATA[niRos].xOuterNE.xPosG, 
                                    &m_vAllRoaDATA[niRos].xOuterSW.xPosG);
    }

    return false;
}

/**
 * @brief Check if own ship is inside of inner zone
 * @param niRos ROS index
 * @return true if own ship is inside of inner zone, false otherwise
 */
bool CROSMgr::CheckOwnShipInsideOfInnerTrZone(int niRos)
{
    if (m_vAllRosDATA[niRos].bValidMode == MODE_VAL_ON)
    {
        return CheckInsideRectangleXX(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG, 
                                    &m_vAllRoaDATA[niRos].xRectNE.xPosG, 
                                    &m_vAllRoaDATA[niRos].xRectSW.xPosG);
    }

    return false;
}

/**
 * @brief Check if own ship is inside of inner zone
 * @param niRos ROS index
 * @return true if own ship is inside of inner zone, false otherwise
 */
bool CROSMgr::CheckOwnShipInsideOfInnerZone(int niRos)
{
    if (m_vAllRosDATA[niRos].bValidMode == MODE_VAL_ON)
    {
        return CheckInsideRectangleXX(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG, 
                                    &m_vAllRoaDATA[niRos].xInnerNE.xPosG, 
                                    &m_vAllRoaDATA[niRos].xInnerSW.xPosG);
    }

    return false;
}

/**
 * @brief Get TRX mode by from ROS setup
 * @param nRosMode ROS mode
 * @param nRosSource ROS source
 * @return TRX mode by
 */
int CROSMgr::GetTrxModeByFromRosSetup(int nRosMode, int nRosSource)
{
    int nTxRxModeBy = TRXMODE_BY_DEFAULT;
    if (nRosMode != ROS_MODE_HIGH_SEA_ZONE)
    {
        if (nRosSource == ROS_SRC_MSG_ADDRD)
            nTxRxModeBy = TRXMODE_BY_MSG22_ADDRSD;
        else
            nTxRxModeBy = TRXMODE_BY_MSG22_BRDCST;
    }

    return nTxRxModeBy;
}

/**
 * @brief Check if two channel setup data are the same
 * @param psRosSetup1 First channel setup data to compare
 * @param psRosSetup2 Second channel setup data to compare
 * @param bCheckTrxMode Check TRX mode
 * @param bCheckTrxModeBy Check TRX mode by
 * @param bCheckPowerMode Check power mode
 * @return true if two channel setup data are the same, false otherwise
 */
bool CROSMgr::IsSameChSetupData(CH_SETUP *psRosSetup1, CH_SETUP *psRosSetup2, bool bCheckTrxMode, bool bCheckTrxModeBy, bool bCheckPowerMode)
{
    return ((psRosSetup1->uChannelIdA == psRosSetup2->uChannelIdA) &&
            (psRosSetup1->uChannelIdB == psRosSetup2->uChannelIdB) &&
            (!bCheckPowerMode || psRosSetup1->uTxPower == psRosSetup2->uTxPower) &&
            (!bCheckTrxMode || psRosSetup1->nTxRxMode == psRosSetup2->nTxRxMode) &&
            (!bCheckTrxModeBy || psRosSetup1->nTxRxModeBy == psRosSetup2->nTxRxModeBy));
}

/**
 * @brief Save new ROS data
 * @param pRosData ROS data to save
 * @param pRoaData ROA data to save
 * @param nRosIndex ROS index to save
 */
void CROSMgr::SaveNewROS(xROSDATA *pRosData, xROADATA *pRoaData, int nRosIndex)
{
    int nUpdatePos = -1;

    if(IsRosIndexValid_Ext(nRosIndex))
    {
        nUpdatePos = nRosIndex;
    }
    else
    {
        // Find empty ROS
        for (int idx = 0; idx < MAX_ROS_DATA_SIZE; idx++)
        {
            if(m_vAllRosDATA[idx].bValidMode == MODE_VAL_OFF)
            {
                nUpdatePos = idx;
                break;
            }
        }

        // If there is no empty ROS
        if(nUpdatePos < 0)
        {
            //-----------------------------------------------------------------------------------
            // ITU-R M.1371-5 Annex2 4.1.8
            // If there is no free memory location, the most distant regional operating setting 
            // should be replaced by the new, accepted one.
            // If the AIS station does not have position it should delete the area most distant 
            // from the position provided in the channel management command.
            if((nUpdatePos = GetMostDistantROSfromOwnShip()) < 0)
                nUpdatePos = GetMostDistantROSfromROS(pRoaData);
        }
    }

    if(nUpdatePos >= 0)
    {
        BOOL bRosInUse = IsRosInUse(nUpdatePos);
        BOOL bRosDataSame = !memcmp(&(m_vAllRosDATA[nUpdatePos]), pRosData, sizeof(xROSDATA));

        // Copy ROS/ROA data
        memmove(&m_vAllRoaDATA[nUpdatePos], pRoaData, sizeof(xROADATA));
        memmove(&m_vAllRosDATA[nUpdatePos], pRosData, sizeof(xROSDATA));

        // Send ACA sentence to MKD and save ROS data
        CMKD::getInst()->SendAllACAACStoPI();
        CSetupMgr::getInst()->ReserveToSaveRosConfigData();

        //---------------------------------------------------------------------------------------------
        // IEC-61993-2 ******** Channel management parameters changed
        // The TXT-sentence, Text Identifier 036, shall be followed by the appropriate ACA sentence(s)
        // to report the affected AIS conditions.
        // The TXT and ACA sentence pair shall be transmitted only once when crossing the boundary
        // of the region, when the parameters in use are changed by a new command.
        if(bRosInUse && !bRosDataSame)
        {
            // TT17_4, to check if the parameters in use are changed by a new command. 
            // If so, it should output ACA and TXT-36
            CMKD::getInst()->SendACAACStoPI(nUpdatePos, true);
            CMKD::getInst()->SendTXTtoPI(TXT_ID_CH_MNG_CHANGE, true);
        }
    }
    else
    {
        WARNING_LOG("SaveROS-end] Fail, No space found, s : %d\r\n", cTimerSys::getInst()->GetCurTimerSec());
    }
}

/**
 * @brief Update ROS data
 * @param pRosData ROS data to update
 * @param nRosIndex ROS index to update
 */
int CROSMgr::UpdateRosData(xROSDATA *pRosData, int nRosIndex)
{
    xROADATA xRoaData;
    xROADATA *pRoaData = &xRoaData;

    // Check if ROS data is valid
    if(!CheckRosDataValid(pRosData))
    {
        WARNING_LOG("UpdateROS] ignore, invalid ROS data from %09d\r\n", pRosData->dSrcMMSI);
        return ROS_ERR_ROS_DATA;
    }

    // Calculate ROA data from ROS data
    CalcOneRoaDataByROS(pRosData, pRoaData);

    // Check if ROA boundary size is 20nm <= ROA <= 200nm
    int nRetCode = CheckRoaSizeIsValid(pRoaData);
    if(nRetCode != ROS_ERR_NO_ERR)
    {
        return nRetCode;
    }

    // --------------------------------------------------------------------------------------------------------
    // ITU-R M.1371-5 Annex2 4.1.8
    // The mobile AIS station should not accept a new regional operating setting which was input to it from 
    // a shipborne system command, i.e. via the Presentation Interface, if the regional operating area of this
    // new regional operating setting partly or totally overlaps or matches the regional operating area of any 
    // of the stored regional operating settings, which were received from a base station either by Message 22 
    // or by DSC telecommand within the last two hours.
    if(CheckIgnoreRosByPI(pRosData, pRoaData) == ROS_ERR_ROA_OVERLAP_2HOURS)
    {
        return ROS_ERR_ROA_OVERLAP_2HOURS;
    }

    // --------------------------------------------------------------------------------------------------------
    // ITU-R M.1371-5 Annex2 4.1.8
    // If the regional operating area of the new, accepted regional operating setting overlaps in part or 
    // in total or matches the regional operating areas of one or more older regional operating settings, 
    // this or these older regional operating settings should be erased from the memory. 
    // The regional operating area of the new, accepted regional operating setting may be neighbouring tightly 
    // and may thus have the same boundaries as older regional operating settings. This should not lead to 
    // the erasure of the older regional operating settings.
    RemoveOverlappedROA(pRosData, pRoaData);

    // Get number of valid ROS
    if(GetNumValidROS() >= 2)
    {
        //------------------------------------------------------------------------------------
        // refer to ITU-R M.1371-5 Annex2 4.1.5
        // The mobile AIS station should ignore any channel management command,
        // when there are three different regional operating settings with adjacent
        // regional operating areas, their corners within eight nautical miles to each other.
        if(!CheckROA8nmRule(pRoaData))
        {
            WARNING_LOG("UpdateROS] ignore, CheckROA8nmRule\r\n");
            return ROS_ERR_ROA_CORNER_INVALID;
        }
    }

    // Save new ROS data
    SaveNewROS(pRosData, pRoaData, nRosIndex);

    return ROS_ERR_NO_ERR;
}

/**
 * @brief Update addressed ROS data
 * @param psRosData ROS data to update
 * @param bCheckCurRosMode Check current ROS mode
 */
void CROSMgr::UpdateAddressedRosData(xROSDATA *psRosData, bool bCheckCurRosMode)
{
    //-----------------------------------------
    // refer to 61993-2 7.4.2
    // Test report TTD_3_Ed2.scn
    //-----------------------------------------
    // ITU-R M.1371-5 Ann2 4.1.8
    //-----------------------------------------

    bool bRosRectDataValid = CheckRosRectValid(psRosData);

    if (!bRosRectDataValid)
    {
        bCheckCurRosMode = TRUE;
    }

    if (bCheckCurRosMode)
    {
        INT8 nCurRosRunMode = GetRoaRunMode();
        if ((GetCurrRosNo() == ROS_IDX_HIGHSEA)
            || ((nCurRosRunMode != ROS_MODE_INNER_ZONE) && (nCurRosRunMode != ROS_MODE_INNER_TR_ZONE)))
        {
            INFO_LOG("UpdateRos-Addr] ignore, ROS mode : %d, ROA mode : %d\r\n", GetCurrRosNo(), nCurRosRunMode);
            return;
        }
    }

    if (!bRosRectDataValid)
    {
        memcpy(&psRosData->xPointNE, &m_vAllRosDATA[m_sRosData.nRosIdx].xPointNE, sizeof(POS_ALLH));
        memcpy(&psRosData->xPointSW, &m_vAllRosDATA[m_sRosData.nRosIdx].xPointSW, sizeof(POS_ALLH));
    }

    UpdateRosData(psRosData);
}

/**
 * @brief Get matched ROS to transit
 * @param pnCurRosMode Current ROS mode
 * @param pnCurRosIdx Current ROS index
 * @param pnAdjRosIdx Adjacent ROS index
 */
void CROSMgr::GetMatchedRosToTransit(INT8 *pnCurRosMode, INT8 *pnCurRosIdx, INT8 *pnAdjRosIdx)
{
    int i = 0;

    *pnCurRosMode= ROS_MODE_HIGH_SEA_ZONE;
    *pnCurRosIdx = ROS_IDX_HIGHSEA;
    *pnAdjRosIdx = ROS_IDX_HIGHSEA;

    for (i = 0; i < MAX_ROS_DATA_SIZE; i++)
    {
        if(m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
        {
            // Check if own ship is inside of inner zone
            if(CheckOwnShipInsideOfInnerZone(i))
            {
                *pnCurRosMode = ROS_MODE_INNER_ZONE;
                *pnCurRosIdx = i;
                *pnAdjRosIdx = ROS_IDX_NULL;

                DEBUG_LOG("GetMatchedRos] set, inner, mode:%d, idx:%d, adj:%d, NE:%.2f,%.2f SW:%.2f,%.2f pos:%.2f,%.2f\r\n",
                        *pnCurRosMode, *pnCurRosIdx, *pnAdjRosIdx,
                        m_vAllRosDATA[*pnCurRosIdx].xPointNE.xPosF.fLON, m_vAllRosDATA[*pnCurRosIdx].xPointNE.xPosF.fLAT,
                        m_vAllRosDATA[*pnCurRosIdx].xPointSW.xPosF.fLON, m_vAllRosDATA[*pnCurRosIdx].xPointSW.xPosF.fLAT,
                        cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT);
                return;
            }

            // Check if own ship is inside of inner TR zone
            if(CheckOwnShipInsideOfInnerTrZone(i))
            {
                // 1. Check if own ship exists within the TR_ZONE(NE~SW) area of the ROA region.
                // 2. If it is in the entire TR_ZONE, check which quadrant of the current ROA region it is located in.
                // 3. Find if there is another ROS region adjacent to that side.
                //    (Determine whether the adjacent area is a TR_ZONE of another ROS region or a Default ROS region)
                // 4. If the current ship's latitude/longitude is within the intersection of the adjacent region's TR_ZONE, change to that region's channel. 
                //    Otherwise, set the Secondary CH to Default ROS.
                *pnCurRosMode = ROS_MODE_INNER_TR_ZONE;
                *pnCurRosIdx = i;
                *pnAdjRosIdx = GetAdjacentRos(i);

                DEBUG_LOG("GetMatchedRos] set, innerTR, mode:%d, idx:%d, adj:%d, NE:%.2f,%.2f SW:%.2f,%.2f pos:%.2f,%.2f\r\n",
                        *pnCurRosMode, *pnCurRosIdx, *pnAdjRosIdx,
                        m_vAllRosDATA[*pnCurRosIdx].xPointNE.xPosF.fLON, m_vAllRosDATA[*pnCurRosIdx].xPointNE.xPosF.fLAT,
                        m_vAllRosDATA[*pnCurRosIdx].xPointSW.xPosF.fLON, m_vAllRosDATA[*pnCurRosIdx].xPointSW.xPosF.fLAT,
                        cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT);
                return;
            }
        }
    }

    // Check if own ship is inside of outer TR zone
    i = GetOuterRosOwnShipLocated(ROS_IDX_NULL);
    if(i != ROS_IDX_NULL)
    {
        DEBUG_LOG("GetMatchedRos] GetNearestOuter : %d, pos:%.2f,%.2f\r\n",
                i, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON);

        // 1. Although Inner_Zone and Tr_Zone cannot overlap, Outer_Zone can overlap (only in cases where areas are adjacent to each other).
        //    Find the closest area and switch the AIS2 channel to that direction.
        // 2. Since we can already identify Inner_Zone and Tr_zone above, overlapping areas within the ROA region have already been checked.
        //    We only need to check the remaining non-overlapping Outer areas and areas where Outer regions overlap with each other.

        int nTmpIndex = CheckInsideOverlapOuterRegion(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid, i);
        DEBUG_LOG("GetMatchedRos] AnotherOuter : %d, pos:%.2f,%.2f\r\n",
                nTmpIndex, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT);
        if(nTmpIndex == ROS_IDX_HIGHSEA)
        {
            *pnCurRosMode = ROS_MODE_OUTER_TR_ZONE;
            *pnCurRosIdx = i;
            *pnAdjRosIdx = ROS_IDX_HIGHSEA;

            DEBUG_LOG("GetMatchedRos] set, outerTR, mode:%d, idx:%d, adj:%d, NE:%.2f,%.2f SW:%.2f,%.2f pos:%.2f,%.2f\r\n",
                *pnCurRosMode, *pnCurRosIdx, *pnAdjRosIdx,
                m_vAllRosDATA[*pnCurRosIdx].xPointNE.xPosF.fLON, m_vAllRosDATA[*pnCurRosIdx].xPointNE.xPosF.fLAT,
                m_vAllRosDATA[*pnCurRosIdx].xPointSW.xPosF.fLON, m_vAllRosDATA[*pnCurRosIdx].xPointSW.xPosF.fLAT,
                cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT);
        }
        else
        {
            nTmpIndex = GetNearestROA(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid, i, nTmpIndex);

            *pnCurRosMode = ROS_MODE_OUTER_TR_ZONE;
            *pnCurRosIdx = nTmpIndex;
            *pnAdjRosIdx = i;

            DEBUG_LOG("GetMatchedRos] set, highsea-1, mode:%d, idx:%d, adj:%d, pos:%.2f,%.2f\r\n",
                *pnCurRosMode, *pnCurRosIdx, *pnAdjRosIdx,
                cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT);
        }
        return;
    }

    DEBUG_LOG("GetMatchedRos] set, highsea-2, mode:%d, idx:%d, adj:%d, pos:%.2f,%.2f\r\n",
            *pnCurRosMode, *pnCurRosIdx, *pnAdjRosIdx,
            cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT);
}

/**
 * @brief Get ROS data that position belongs to
 * @param psRosData ROS data to check
 * @return True if position belongs to ROS data, false otherwise
 */
bool CROSMgr::GetRosDataPosBelongsTo(ROS_DATA *psRosData)
{
    //-----------------------------------------------------------------------------------------------
    // refer to ITU-R M.1371-5 (4.1.1)
    // For channel management when position information is lost during normal operation, the current
    // frequency channel use should be maintained until ordered to change by an addressed channel
    // management message (addressed DSC command or addressed Message 22) or by manual input.

    if(!CAisLib::IsValidAisGridPOS(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG))
        return false;

    GetMatchedRosToTransit(&psRosData->nRosMode, &psRosData->nRosIdx, &psRosData->nRosAdjIdx);

    psRosData->sChSetup.nTxRxMode   = TRXMODE_NONE;
    psRosData->sChSetup.nTxRxModeBy = TRXMODE_BY_UNKNOWN;
    psRosData->sChSetup.uChannelIdA = AIS_CH_NUM_NONE;
    psRosData->sChSetup.uChannelIdB = AIS_CH_NUM_NONE;

    switch(psRosData->nRosMode)
    {
    case ROS_MODE_OUTER_TR_ZONE:
        {
            switch(m_sRosData.nRosMode)
            {
            case ROS_MODE_HIGH_SEA_ZONE:
            case ROS_MODE_INNER_TR_ZONE:
            case ROS_MODE_OUTER_TR_ZONE:
            case ROS_MODE_INNER_ZONE:
                psRosData->sChSetup.uTxPower   = m_vAllRosDATA[psRosData->nRosAdjIdx].bTxPower;
                psRosData->sChSetup.uChannelIdA= m_vAllRosDATA[psRosData->nRosAdjIdx].wChannelNoA;
                psRosData->sChSetup.uBandwidthA= m_vAllRosDATA[psRosData->nRosAdjIdx].bBandwidthA;
                psRosData->sChSetup.uChannelIdB= m_vAllRosDATA[psRosData->nRosIdx].wChannelNoA;
                psRosData->sChSetup.uBandwidthB= m_vAllRosDATA[psRosData->nRosIdx].bBandwidthA;

                psRosData->sChSetup.nTxRxMode  = TRXMODE_TXARXA_TXBRXB;
                if(m_vAllRosDATA[psRosData->nRosAdjIdx].bTxRxMode == TRXMODE____RXA_TXBRXB ||
                    m_vAllRosDATA[psRosData->nRosAdjIdx].bTxRxMode == TRXMODE____RXA____RXB ||
                    m_vAllRosDATA[psRosData->nRosAdjIdx].bTxRxMode == TRXMODE____RXA_______)
                {
                    // If adjacent ROS primary channel is Rx only, select secondary channel!
                    // refer to RS test method page.707
                    psRosData->sChSetup.uChannelIdA= m_vAllRosDATA[psRosData->nRosAdjIdx].wChannelNoB;
                    psRosData->sChSetup.uBandwidthA= m_vAllRosDATA[psRosData->nRosAdjIdx].bBandwidthB;
                }
                if(m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode == TRXMODE____RXA_TXBRXB ||
                    m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode == TRXMODE____RXA____RXB ||
                    m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode == TRXMODE____RXA_______)
                {
                    // If current ROS primary channel is Rx only, select secondary channel!
                    psRosData->sChSetup.uChannelIdB= m_vAllRosDATA[psRosData->nRosIdx].wChannelNoB;
                    psRosData->sChSetup.uBandwidthB= m_vAllRosDATA[psRosData->nRosIdx].bBandwidthB;
                }
                psRosData->sChSetup.nTxRxModeBy    = GetTrxModeByFromRosSetup(psRosData->nRosMode, m_vAllRosDATA[m_sRosData.nRosIdx].bRosSource);
                break;
            }
        }
        break;

    case ROS_MODE_INNER_TR_ZONE:
        {
            switch(m_sRosData.nRosMode)
            {
            case ROS_MODE_INNER_TR_ZONE:
            case ROS_MODE_HIGH_SEA_ZONE:
            case ROS_MODE_OUTER_TR_ZONE:
            case ROS_MODE_INNER_ZONE:
                psRosData->sChSetup.uTxPower   = m_vAllRosDATA[psRosData->nRosIdx].bTxPower;

                psRosData->sChSetup.uChannelIdA= m_vAllRosDATA[psRosData->nRosIdx].wChannelNoA;
                psRosData->sChSetup.uBandwidthA= m_vAllRosDATA[psRosData->nRosIdx].bBandwidthA;
                psRosData->sChSetup.uChannelIdB= m_vAllRosDATA[psRosData->nRosAdjIdx].wChannelNoA;
                psRosData->sChSetup.uBandwidthB= m_vAllRosDATA[psRosData->nRosAdjIdx].bBandwidthA;

                psRosData->sChSetup.nTxRxMode  = TRXMODE_TXARXA_TXBRXB;
                if(m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode == TRXMODE____RXA_TXBRXB ||
                    m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode == TRXMODE____RXA____RXB ||
                    m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode == TRXMODE____RXA_______)
                {
                    // If current ROS primary channel is Rx only, select secondary channel!
                    psRosData->sChSetup.uChannelIdA= m_vAllRosDATA[psRosData->nRosIdx].wChannelNoB;
                    psRosData->sChSetup.uBandwidthA= m_vAllRosDATA[psRosData->nRosIdx].bBandwidthB;
                }
                if(m_vAllRosDATA[psRosData->nRosAdjIdx].bTxRxMode == TRXMODE____RXA_TXBRXB ||
                    m_vAllRosDATA[psRosData->nRosAdjIdx].bTxRxMode == TRXMODE____RXA____RXB ||
                    m_vAllRosDATA[psRosData->nRosAdjIdx].bTxRxMode == TRXMODE____RXA_______)
                {
                    // If adjacent ROS primary channel is Rx only, select secondary channel!
                    // refer to RS test method page.707
                    psRosData->sChSetup.uChannelIdB= m_vAllRosDATA[psRosData->nRosAdjIdx].wChannelNoB;
                    psRosData->sChSetup.uBandwidthB= m_vAllRosDATA[psRosData->nRosAdjIdx].bBandwidthB;
                }
                psRosData->sChSetup.nTxRxModeBy = GetTrxModeByFromRosSetup(psRosData->nRosMode, m_vAllRosDATA[m_sRosData.nRosIdx].bRosSource);
                break;
            }
        }
        break;

    case ROS_MODE_INNER_ZONE:
    case ROS_MODE_HIGH_SEA_ZONE:
        {
            switch(m_sRosData.nRosMode)
            {
            case ROS_MODE_INNER_TR_ZONE:
            case ROS_MODE_HIGH_SEA_ZONE:
            case ROS_MODE_OUTER_TR_ZONE:
            case ROS_MODE_INNER_ZONE:
                psRosData->sChSetup.uTxPower   = m_vAllRosDATA[psRosData->nRosIdx].bTxPower;
                psRosData->sChSetup.uChannelIdA= m_vAllRosDATA[psRosData->nRosIdx].wChannelNoA;
                psRosData->sChSetup.uBandwidthA= m_vAllRosDATA[psRosData->nRosIdx].bBandwidthA;
                psRosData->sChSetup.uChannelIdB= m_vAllRosDATA[psRosData->nRosIdx].wChannelNoB;
                psRosData->sChSetup.uBandwidthB= m_vAllRosDATA[psRosData->nRosIdx].bBandwidthB;
                psRosData->sChSetup.nTxRxMode  = m_vAllRosDATA[psRosData->nRosIdx].bTxRxMode;
                psRosData->sChSetup.nTxRxModeBy= GetTrxModeByFromRosSetup(psRosData->nRosMode, m_vAllRosDATA[m_sRosData.nRosIdx].bRosSource);
                break;
            }
        }
        break;
    }

    return true;
}

/**
 * @brief Process ROS mode transition
 */
void CROSMgr::ProcessRosModeTransit(void)
{
    //------------------------------------------------------------------------------------------------
    // At Power On, it operates with Default channel - AIS1(2087)/AIS2(2088).
    // When first entering AutoMode or after 1 minute (when own slot has been established by Timeout), 
    // the initial m_sRosData.nRosMode is determined.
    //
    // Initially, the vessel is located in one of inner zone, transition zone, or outer zone.
    // Search priority: inner zone -> transition zone (including overlapping area check) 
    //                  -> outer zone (including overlapping area check) -> high sea

    ROS_DATA sNewRosData;
    bool bRosChged = false;
    bool bPowerModeChged = false;
    bool bRunChgChSetupOK= false;

    if (!CLayerNetwork::getInst()->IsRosChgOpAvailable())
        return;

    // Check if own ship position belongs to any ROS
    if (!GetRosDataPosBelongsTo(&sNewRosData))
        return;

    ROS_DATA sOldRosData;
    memcpy(&sOldRosData, &m_sRosData, sizeof(ROS_DATA));

    if ((bRosChged = CAisLib::MemCmp((BYTE*)&sNewRosData, (BYTE*)&m_sRosData, sizeof(ROS_DATA))))
    {
        //--------------------------------------------------------------------------------------------
        // IEC-61993-2 ********.2
        // the EUT ignores the assignment by Message 23 and the setting of Message 22 takes
        // precedence for 10 min; after 10 min the Tx/Rx mode changes to Tx/Rx mode 1 according
        // to the Message 23 setting
        m_bCheckTrxModeByAddrMsg22 = (sNewRosData.sChSetup.nTxRxModeBy == TRXMODE_BY_MSG22_ADDRSD);
        m_bTrxModeByAddrMsg22Done = false;
        m_dwTRxModeStartSecByAddrMsg22 = cTimerSys::getInst()->GetCurTimerSec();
    }

    CH_SETUP sNewChSetup;
    memcpy(&sNewChSetup, &sNewRosData.sChSetup, sizeof(CH_SETUP));

    GetNewTrxModeByPriority(sNewRosData.nRosMode, &sNewChSetup, &sNewChSetup.nTxRxMode, &sNewChSetup.nTxRxModeBy);

    bool bSameChSetup = IsSameChSetupData(&sNewChSetup, &m_sChSetup, true, true, TRUE);

    DEBUG_LOG("ProcRosModeJmp] ROS check, TRX: %d(%d) -> %d(%d), pwr:%d -> %d(%d,%d,%d), chA:%d -> %d, chB:%d -> %d, s:%d\r\n",
        m_sChSetup.nTxRxMode, m_sChSetup.nTxRxModeBy, sNewChSetup.nTxRxMode, sNewChSetup.nTxRxModeBy,
        m_sChSetup.uTxPower, sNewChSetup.uTxPower, cShip::getOwnShipInst()->xNavData.uShipType, cShip::getOwnShipInst()->xNavData.uNavStatus, cShip::getOwnShipInst()->xDynamicData.nSOG,
        m_sChSetup.uChannelIdA, sNewChSetup.uChannelIdA, m_sChSetup.uChannelIdB, sNewChSetup.uChannelIdB, cTimerSys::getInst()->GetCurTimerSec());

    if (bRosChged)
    {
        SetRosData(&sNewRosData);
    }

    bool bRunChSetup = false;
    if (!bSameChSetup)
    {
        bRunChSetup = !IsSameChSetupData(&sNewChSetup, &m_sChSetup, true, false, false);
        if (bRunChSetup)
        {
            DEBUG_LOG("ProcRosModeJmp] RunChChg, TRX: %d(%d) -> %d(%d), pwr:%d -> %d, chA:%d -> %d, chB:%d -> %d, %d, s:%d\r\n",
                m_sChSetup.nTxRxMode, m_sChSetup.nTxRxModeBy, sNewChSetup.nTxRxMode, sNewChSetup.nTxRxModeBy,
                m_sChSetup.uTxPower, sNewChSetup.uTxPower, m_sChSetup.uChannelIdA, sNewChSetup.uChannelIdA,
                m_sChSetup.uChannelIdB, sNewChSetup.uChannelIdB, CLayerNetwork::getInst()->IsRosChgOpAvailable(), cTimerSys::getInst()->GetCurTimerSec());

            bPowerModeChged = SetTxPowerModeROS(sNewChSetup.uTxPower);

            // ITU-R 1371-5 Annex 2 4.3
            float fNewReportIntervalSec = 10.0f;
            if (OPSTATUS::bFirstFrameOnBootDone)
                fNewReportIntervalSec = CReportRateMgr::getInst()->CheckAutoModeReportInterval(FALSE, FALSE);

            if (CLayerNetwork::getInst()->RunChannelSetup(sNewRosData.nRosMode, sNewChSetup.nTxRxMode, sNewChSetup.nTxRxModeBy, sNewChSetup.uChannelIdA, sNewChSetup.uChannelIdB,
                fNewReportIntervalSec, CLayerNetwork::getInst()->m_nOpMode, CLayerNetwork::getInst()->m_nOpMode, CLayerNetwork::getInst()->m_bAssignedModeBy))
            {
                SetChSetupData(&sNewChSetup, (CLayerNetwork::getInst()->m_nOpPhase != OPPHASE_CH_CHG_PHASE));
                bRunChgChSetupOK = true;

                // Send TXT(42) message every time the channel changes when entering a ROS area
                if (IsRosIndexValid_Ext(m_sRosData.nRosIdx) && m_vAllRosDATA[m_sRosData.nRosIdx].dSrcMMSI != AIS_AB_MMSI_NULL)
                {
                    CLayerNetwork::getInst()->m_uChannelManageModeBaseStMMSI = m_vAllRosDATA[m_sRosData.nRosIdx].dSrcMMSI;
                    CMKD::getInst()->SendTXTtoPI(TXT_ID_CH_MNG_MODE, true);
                }
            }
        }
        else
        {
                bPowerModeChged = SetTxPowerModeROS(sNewChSetup.uTxPower);
                SetChSetupData(&sNewChSetup, true);
        }
    }
}

/**
 * @brief Check if TR mode is available
 * @param nRosMode ROS mode
 * @param psChSetup Channel setup
 * @return true if TR mode is available, false otherwise
 */
bool CROSMgr::IsTrModeAvailable(int nRosMode, CH_SETUP *psChSetup)
{
    //--------------------------------------------------------------------------------------
    // ITU-R M.1371-5 4.1.5
    // Additionally, for multichannel operations as specified in § 4.1.2, 
    // except when the reporting interval has been assigned by Message 16, 
    // when operating in this mode, the reporting interval should be doubled 
    // and shared between the two channels (alternate transmission mode).
    return IsTrMode(nRosMode) && !CLayerNetwork::getInst()->CheckAssignedModeRunningByMsg16();
}

/**
 * @brief Check if low power mode is required
 * @return true if low power mode is required, false otherwise
 */
bool CROSMgr::IsLowPowerModeRequired(void)
{
    //--------------------------------------------------------------------------------------
    // IEC 61993-2(ed3.0) 6.9
    // Additionally, there shall be a low power setting of 1 W which is automatically adopted 
    // when the vessel type is a "tanker" and the NavStatus is "moored" and not moving faster than 3 kn.
    // This is to facilitate low power operation when loading and unloading.
    return (CAisLib::IsShipTypeTanker(cShip::getOwnShipInst()->xNavData.uShipType) &&
            cShip::getOwnShipInst()->xNavData.uNavStatus == AIS_NAV_STATUS_MOORED &&
            (cShip::getOwnShipInst()->xDynamicData.nSOG != AIS_SOG_VDL_NULL 
            && cShip::getOwnShipInst()->xDynamicData.nSOG <= SOG_THRESHOLD_DONTMOVE));
}

/**
 * @brief Set Tx power mode
 * @param uTxPower Tx power mode to set
 * @return true if Tx power mode is changed, false otherwise
 */
bool CROSMgr::SetTxPowerModeROS(UINT8 uTxPower)
{
    bool bChged = (m_sChSetup.uTxPower != uTxPower);

    m_sChSetup.uTxPower = uTxPower;
    CLayerPhysical::getInst()->SetTxPowerMode(uTxPower);

    return bChged;
}

/**
 * @brief Check if same new TRX mode setup
 * @param nNewRosMode New ROS mode
 * @param psNewChSetup New channel setup
 * @param pnTxRxModeToSet TRX mode to set
 * @param pnTRxModeMethodToSet TRX mode by to set
 * @return true if same new TRX mode setup, false otherwise
 */
bool CROSMgr::IsSameNewTrxModeSetup(int nNewRosMode, CH_SETUP *psNewChSetup, INT8 *pnTxRxModeToSet, INT8 *pnTRxModeMethodToSet)
{
    *pnTxRxModeToSet     = m_sChSetup.nTxRxMode;
    *pnTRxModeMethodToSet= m_sChSetup.nTxRxModeBy;

    if (   m_sRosData.nRosMode == nNewRosMode 
        && m_sChSetup.nTxRxMode == psNewChSetup->nTxRxMode 
        && m_sChSetup.nTxRxModeBy == psNewChSetup->nTxRxModeBy)
    {
        return true;
    }

    return false;
}

/**
 * @brief Get new TRX mode by priority
 * @param nNewRosMode New ROS mode
 * @param psNewChSetup New channel setup
 * @param pnTxRxModeToSet TRX mode to set
 * @param pnTRxModeMethodToSet TRX mode by to set
 * @return true if new TRX mode is set, false otherwise
 */
bool CROSMgr::GetNewTrxModeByPriority(int nNewRosMode, CH_SETUP *psNewChSetup, INT8 *pnTxRxModeToSet, INT8 *pnTRxModeMethodToSet)
{
    INT8 nNewTxRxMode = psNewChSetup->nTxRxMode;
    INT8 nNewTxRxModeBy = psNewChSetup->nTxRxModeBy;

    int  nCurRosMode = m_sRosData.nRosMode;
    INT8 nCurTxRxMode = m_sChSetup.nTxRxMode;
    INT8 nCurTxRxModeBy = m_sChSetup.nTxRxModeBy;

    *pnTxRxModeToSet     = nCurTxRxMode;
    *pnTRxModeMethodToSet= nCurTxRxModeBy;

    //--------------------------------------------------------------------------------------------
    // IEC-61993-2 ********.2
    // the EUT ignores the assignment by Message 23 and the setting of Message 22 takes
    // precedence for 10 min; after 10 min the Tx/Rx mode changes to Tx/Rx mode 1 according
    // to the Message 23 setting
    if(m_bCheckTrxModeByAddrMsg22 && !m_bTrxModeByAddrMsg22Done)
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22) >  ROS_TIMEOUT_BY_MSG22)    // 10 min
            m_bTrxModeByAddrMsg22Done = TRUE;
    }

    if(nCurRosMode == nNewRosMode && nCurTxRxMode == nNewTxRxMode && nCurTxRxModeBy == nNewTxRxModeBy)
    {
        DEBUG_LOG("GetNewTRXmode] ignore, same setup RosMode: %d, TRX:%d(%d), startSec:%d, elap:%d, byMsg22Addr:%d,%d,%d,elap:%d, s:%d\r\n",
            nCurRosMode, nCurTxRxMode, nCurTxRxModeBy,
            m_dwTRxModeStartSec, cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSec),
            m_bCheckTrxModeByAddrMsg22, m_bTrxModeByAddrMsg22Done, m_dwTRxModeStartSecByAddrMsg22,
            cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22), cTimerSys::getInst()->GetCurTimerSec());
        return false;
    }

    if(IsTrModeAvailable(nNewRosMode, psNewChSetup))
    {
        *pnTxRxModeToSet        = TRXMODE_TXARXA_TXBRXB;
        *pnTRxModeMethodToSet    = TRXMODE_BY_TRZONE;

        return false;
    }

    DEBUG_LOG("GetNewTRXmode] chk, RosMode: %d -> %d, TRX:%d(%d) -> %d(%d), startSec:%d, elap:%d, byMsg22Addr:%d,%d,elap:%d, s:%d\r\n",
            nCurRosMode, nNewRosMode, nCurTxRxMode, nCurTxRxModeBy, nNewTxRxMode, nNewTxRxModeBy, m_dwTRxModeStartSec, cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSec),
            m_bCheckTrxModeByAddrMsg22, m_bTrxModeByAddrMsg22Done, cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22), cTimerSys::getInst()->GetCurTimerSec());

    if(nCurTxRxModeBy == TRXMODE_BY_MSG23 || nNewTxRxModeBy == TRXMODE_BY_MSG23)
    {
        switch(nCurTxRxModeBy)
        {
        case TRXMODE_BY_MSG22_ADDRSD:
            // If the EUT is in the assigned mode by Message 22 and the setting of Message 22 takes precedence for 10 min, 
            if(m_bCheckTrxModeByAddrMsg22 && !m_bTrxModeByAddrMsg22Done)
            {
                INFO_LOG("GetNewTRXmode] ignore, cur:BY_MSG22_ADDRSD, elapSec:%d\r\n", cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22));
                return false;
            }
            break;

        case TRXMODE_BY_MSG23:
            // If the EUT is in the assigned mode by Message 23, 
            if(CLayerNetwork::getInst()->m_nOpMode == OPMODE_ASSIGNED_RR && CLayerNetwork::getInst()->m_bAssignedModeBy == ASSIGNED_MODE_BY_MSG23)
            {
                if(nNewTxRxModeBy == TRXMODE_BY_MSG22_BRDCST ||
                    (nNewTxRxModeBy == TRXMODE_BY_MSG22_ADDRSD && m_bCheckTrxModeByAddrMsg22 && m_bTrxModeByAddrMsg22Done))
                {
                    INFO_LOG("GetNewTRXmode] ignore, curBy:TRXMODE_BY_MSG23, newBy:%d, AssignedMode:%d,%d, elapSec:%d\r\n",
                            nNewTxRxModeBy, CLayerNetwork::getInst()->m_nOpMode, CLayerNetwork::getInst()->m_bAssignedModeBy, cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22));
                    return false;
                }

                if(nNewTxRxModeBy > nCurTxRxModeBy)
                {
                    INFO_LOG("GetNewTRXmode] ignore, cur:TRXMODE_BY_MSG23, newBy:%d, oldBy:%d, elapSec:%d\r\n",
                            nNewTxRxModeBy, nCurTxRxModeBy, cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22));
                    return false;
                }
            }
            break;

        case TRXMODE_BY_MSG22_BRDCST:
            if(nNewTxRxModeBy > nCurTxRxModeBy)
            {
                INFO_LOG("GetNewTRXmode] ignore, cur:TRXMODE_BY_MSG22_BRDCST, newBy:%d, oldBy:%d, elapSec:%d\r\n",
                        nNewTxRxModeBy, nCurTxRxModeBy, cTimerSys::getInst()->GetTimeDiffSec(m_dwTRxModeStartSecByAddrMsg22));
                return false;
            }
            break;

        case TRXMODE_BY_DEFAULT:
            break;

        default:
            INFO_LOG("GetNewTRXmode] unknown TRX mode value, %d,%d\r\n",
                nCurTxRxMode, nCurTxRxModeBy);
            return false;
        }
    }

    *pnTxRxModeToSet     = nNewTxRxMode;
    *pnTRxModeMethodToSet= nNewTxRxModeBy;

    DEBUG_LOG("GetNewTRXmode] set, RosMode: %d -> %d, TRX:%d(%d) -> %d(%d), s:%d\r\n",
            nCurRosMode, nNewRosMode, nCurTxRxMode, nCurTxRxModeBy, *pnTxRxModeToSet, *pnTRxModeMethodToSet, cTimerSys::getInst()->GetCurTimerSec());
    return true;
}

/**
 * @brief Set TRX mode
 * @param nNewTxRxMode TRX mode to set
 * @param nNewTxRxModeBy TRX mode by to set
 */
void CROSMgr::SetTxRxMode(INT8 nNewTxRxMode, INT8 nNewTxRxModeBy)
{
    if(nNewTxRxMode == m_sChSetup.nTxRxMode && nNewTxRxModeBy == m_sChSetup.nTxRxModeBy)
    {
        return;
    }

    m_sChSetup.nTxRxMode   = nNewTxRxMode;
    m_sChSetup.nTxRxModeBy = nNewTxRxModeBy;
    m_dwTRxModeStartSec    = cTimerSys::getInst()->GetCurTimerSec();

    SetTxRxModeChEnable();
}

/**
 * @brief Set TRX mode channel enable
 */
void CROSMgr::SetTxRxModeChEnable(void)
{
    switch(m_sChSetup.nTxRxMode)
    {
    case TRXMODE_TXARXA____RXB:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(TRUE);
        break;
    case TRXMODE____RXA_TXBRXB:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(TRUE);
        break;
    case TRXMODE_TXARXA_______:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(FALSE);
        break;
    case TRXMODE____RXA____RXB:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(FALSE);
        break;
    case TRXMODE____RXA_______:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(FALSE);
        break;
    case TRXMODE___________RXB:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(TRUE);
        break;
    case TRXMODE_TXARXA_TXBRXB:
    default:
        CLayerNetwork::getInst()->GetChPrimary()->SetTxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChPrimary()->SetRxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTxEnableModeCh(TRUE);
        CLayerNetwork::getInst()->GetChSecondary()->SetRxEnableModeCh(TRUE);
        break;
    }
}

/**
 * @brief Run periodically
 */
void CROSMgr::RunPeriodically(void)
{
    static DWORD dwCheckSecRos = 0;
    static DWORD dwCheckSec5Sec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSecRos) > 1)
    {
        ProcessRosModeTransit();
        dwCheckSecRos = cTimerSys::getInst()->GetCurTimerSec();
    }

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec5Sec) > 5)
    {
        CheckRemoveObsoleteROSData();
        dwCheckSec5Sec = cTimerSys::getInst()->GetCurTimerSec();
    }
}
