#include "SysConst.h"
#include "SysLib.h"
#include "UserDirMgr.h"
#include "RosMgr.h"
#include "LayerNetwork.h"
//#include "ChannelMgr.h"
#include "DSCMgr.h"
#include "LayerPhysical.h"
#include "PI.h"
#include "MKD.h"
#include "LayerTransport.h"

//----------------------
// Externals variables
//----------------------

//----------------------
// Constants
//----------------------

//----------------------
// Prototypes
//----------------------

//----------------------
// Global Variables
//----------------------

//----------------------
// Implementation
//----------------------
CLayerTransport::CLayerTransport()
{
    //CLayerNetwork::getInst();
}

CLayerTransport::~CLayerTransport()
{
}

void CLayerTransport::Initialize()
{
}

void CLayerTransport::InitAllChannels()
{
    CLayerNetwork::getInst()->GetChPrimary()->InitChannel();
    CLayerNetwork::getInst()->GetChSecondary()->InitChannel();

    CROSMgr::getInst()->SetTxPowerModeROS(AIS_TX_POWER_HIGH);    // 12.5W(default)
    CDSCMgr::getInst()->SetRxChannelNumber(DSC_DEFAULT_CH_NUM);
}
