#ifndef __CHANNELMGR_H__
#define __CHANNELMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "ChTxScheduler.h"
#include "FrameMapMgr.h"
#include "VdlTxMgr.h"
#include "VdlRxMgr.h"
#include "RxModem.h"

class CVdlRxMgr;
class CVdlTxMgr;

class CChannelMgr : public CFrameMapMgr, public CChTxScheduler
{
public:
    CChannelMgr(UINT16 uChNum, UINT16 nLongRangeChID, int nHwLocalRcvrID);
    ~CChannelMgr(void);

public:
    void    Initialize(void);
    void    InitChannel(void);
    void    ReloadSetup(void);
    UINT8   GetChOrdinal(void);
    void    SetRxEnableModeCh(BOOL bTxEnable);
    BOOL    IsRxAvailableCh(void);
    void    SetTxEnableModeCh(BOOL bTxEnable);
    BOOL    IsTxAvailableCh(void);
    BOOL    IsTxChHealthy(void);
    void    SetChOpMode(BYTE bOpMode);
    void    SetChOpPhase(BYTE bOpPhase);
    BOOL    SetRxChannelNumber(UINT16 uChNum);
    BOOL    SetTxChannelNumber(UINT16 uChNum);
    void    ResyncChannel(INT16 nShiftSlot);
    void    ProcessSlotChangeTime_Ch(BOOL bChangedFrameID);
    void    RunPeriodicallyCh(void);

public:
    INT8    m_nHwLocalRcvrID;

    UINT16  m_uInitChNum;
    UINT16  m_uChNumTx;                 // channel number(1060 ~ 2088)
    UINT16  m_uChNumRx;                 // channel number(1060 ~ 2088)
    UINT16  m_nLongRangeChID;

    UINT8   m_uBandwidth;               // 25KHZ, 12_5KHZ

    BOOL    m_bRxEnableMode;
    BOOL    m_bTxEnableMode;

    INT8    m_nChOpMode;
    INT8    m_nChOpPhase;
    DWORD   m_dwChOpPhaseStartTick;

    std::shared_ptr<cRxModem> m_pRxModem;
    CVdlRxMgr     *m_pVdlRxMgr;
    CVdlTxMgr     *m_pVdlTxMgr;
};

#endif//__CHANNELMGR_H__
