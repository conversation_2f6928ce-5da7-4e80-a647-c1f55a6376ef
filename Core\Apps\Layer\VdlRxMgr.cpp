#include <string.h>
#include <SartMgr.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "AisMsg.h"
#include "RxModem.h"
#include "ChannelMgr.h"
#include "FrameMapMgr.h"
#include "UserDirMgr.h"
#include "RosMgr.h"
#include "LayerNetwork.h"
#include "ReportRateMgr.h"
#include "AisModem.h"
#include "RtcmLib.h"
#include "GpsBoard.h"
#include "SyncMgr.h"
#include "SensorMgr.h"
#include "SetupMgr.h"
#include "PI.h"
#include "MKD.h"
#include "AlarmMgr.h"
#include "VdlTxMgr.h"
#include "LayerPhysical.h"
#include "GPIOExt.h"
#include "LongRange.h"
#include "GnssInternal.h"
#include "BuiltInTestMgr.h"
#include "TestModeMgr.h"
#include "Ship.h"
#include "Timer.h"
#include "VdlRxMgr.h"


CVdlRxMgr::CVdlRxMgr(CChannelMgr *pChannel)
{
    m_pChannel = pChannel;
}

CVdlRxMgr::~CVdlRxMgr()
{
}

/**
 * @brief Get AIS message id and mmsi field data
 */
void CVdlRxMgr::GetMsg00FieldData(void)
{
    m_xRxAisMsg.xMsg00.wMsgChNO     = m_pChannel->m_uChNumRx;

    m_xRxAisMsg.xMsg00.wMsgFrameNO  = m_pRxRawForm->wMsgFrameNO;
    m_xRxAisMsg.xMsg00.wMsgSlotNO   = m_pRxRawForm->dSlotNoCounter;

    m_xRxAisMsg.xMsg00.wMsgBitsLen  = m_pRxRawForm->wRxBitsSize;
    m_xRxAisMsg.xMsg00.wMsgByteLen  = m_pRxRawForm->wRxByteSize;

    m_xRxAisMsg.xMsg00.dMsgID       = GetAisMsgFieldINT(m_pRxPacketData, 0,  6);
    m_xRxAisMsg.xMsg00.dRptInd      = GetAisMsgFieldINT(m_pRxPacketData, 6,  2);
    m_xRxAisMsg.xMsg00.dSrcMMSI     = GetAisMsgFieldINT(m_pRxPacketData, 8, 30);
}

/**
 * @brief Get AIS message 01/02/03 field data
 */
void CVdlRxMgr::GetMsg01FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg01.dNavStatus           = GetAisMsgFieldINT(m_pRxPacketData,  38,   4);

    m_xRxAisMsg.xMsg01.nROT                 = GetAisMsgFieldINT(m_pRxPacketData,  42,   8);
    if (m_xRxAisMsg.xMsg01.nROT > 127)
        m_xRxAisMsg.xMsg01.nROT = m_xRxAisMsg.xMsg01.nROT - 256;
    m_xRxAisMsg.xMsg01.nROT                 = GetRotDataFromAisFieldData(m_xRxAisMsg.xMsg01.nROT);

    m_xRxAisMsg.xMsg01.dSOG                 = GetAisMsgFieldINT(m_pRxPacketData,  50,  10);
    m_xRxAisMsg.xMsg01.dPosAcc              = GetAisMsgFieldINT(m_pRxPacketData,  60,   1);
    m_xRxAisMsg.xMsg01.xFullPosX.xPosH.nLON = GetAisMsgFieldINT(m_pRxPacketData,  61,  28);        // 1/10000 min
    m_xRxAisMsg.xMsg01.xFullPosX.xPosH.nLAT = GetAisMsgFieldINT(m_pRxPacketData,  89,  27);        // 1/10000 min

    GetAisHighPosDataByMsg(&(m_xRxAisMsg.xMsg01.xFullPosX.xPosH));
    if (!IsValidAisHighPOS(&m_xRxAisMsg.xMsg01.xFullPosX.xPosH))
        ClearFullPosToNULL(&m_xRxAisMsg.xMsg01.xFullPosX);

    m_xRxAisMsg.xMsg01.dCOG                 = GetAisMsgFieldINT(m_pRxPacketData, 116,  12);
    m_xRxAisMsg.xMsg01.dHDG                 = GetAisMsgFieldINT(m_pRxPacketData, 128,   9);
    m_xRxAisMsg.xMsg01.dTimeStamp           = GetAisMsgFieldINT(m_pRxPacketData, 137,   6);
    m_xRxAisMsg.xMsg01.dManoeuvre           = GetAisMsgFieldINT(m_pRxPacketData, 143,   2);
    m_xRxAisMsg.xMsg01.dSpareX              = GetAisMsgFieldINT(m_pRxPacketData, 145,   3);
    m_xRxAisMsg.xMsg01.dRAIM                = GetAisMsgFieldINT(m_pRxPacketData, 148,   1);
    m_xRxAisMsg.xMsg01.dComStateSyncState   = GetAisMsgFieldINT(m_pRxPacketData, 149,   2);
    if (m_xRxAisMsg.xMsg01.xRxCOM.dMsgID != AIS_MSG_NO_ID_03)
    {
        m_xRxAisMsg.xMsg01.dComStateTimeOut = GetAisMsgFieldINT(m_pRxPacketData, 151,   3);
        m_xRxAisMsg.xMsg01.dComStateSubMsg  = GetAisMsgFieldINT(m_pRxPacketData, 154,  14);
    }
    else
    {
        m_xRxAisMsg.xMsg01.dComStateSlotInc = GetAisMsgFieldINT(m_pRxPacketData, 151,  13);
        m_xRxAisMsg.xMsg01.dComStateNoSlots = GetAisMsgFieldINT(m_pRxPacketData, 164,   3);
        m_xRxAisMsg.xMsg01.dComStateKeepFlag= GetAisMsgFieldINT(m_pRxPacketData, 167,   1);
    }

    AisFullPosToFLOAT(&(m_xRxAisMsg.xMsg01.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg01.xFullPosX.xPosF));
    AisFullPosToGRID (&(m_xRxAisMsg.xMsg01.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg01.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message 04 field data
 */
void CVdlRxMgr::GetMsg04FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg04.dYear                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,  14);
    m_xRxAisMsg.xMsg04.dMonth               = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  52,   4);
    m_xRxAisMsg.xMsg04.dDay                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  56,   5);
    m_xRxAisMsg.xMsg04.dHour                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  61,   5);
    m_xRxAisMsg.xMsg04.dMin                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  66,   6);
    m_xRxAisMsg.xMsg04.dSec                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  72,   6);
    m_xRxAisMsg.xMsg04.dPosAcc              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  78,   1);
    m_xRxAisMsg.xMsg04.xFullPosX.xPosH.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  79,  28);
    m_xRxAisMsg.xMsg04.xFullPosX.xPosH.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 107,  27);

    GetAisHighPosDataByMsg(&(m_xRxAisMsg.xMsg04.xFullPosX.xPosH));
    if (!IsValidAisHighPOS(&m_xRxAisMsg.xMsg04.xFullPosX.xPosH))
        ClearFullPosToNULL(&m_xRxAisMsg.xMsg04.xFullPosX);

    m_xRxAisMsg.xMsg04.dEPFD                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 134,   4);
    m_xRxAisMsg.xMsg04.dwTxCtrlLR           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 138,   1);
    m_xRxAisMsg.xMsg04.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 139,   9);
    m_xRxAisMsg.xMsg04.dRAIM                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 148,   1);
    m_xRxAisMsg.xMsg04.dComStateSyncState   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 149,   2);
    m_xRxAisMsg.xMsg04.dComStateTimeOut     = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 151,   3);
    m_xRxAisMsg.xMsg04.dComStateSubMsg      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 154,  14);

    AisFullPosToFLOAT(&(m_xRxAisMsg.xMsg04.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg04.xFullPosX.xPosF));
    AisFullPosToGRID (&(m_xRxAisMsg.xMsg04.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg04.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message 05 field data
 */
void CVdlRxMgr::GetMsg05FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg05.dVersion             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg05.dIMO                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);

    GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg05.vCallSign  ,  70,  42);   m_xRxAisMsg.xMsg05.vCallSign[LEN_MAX_CALLSIGN] = 0x00;
    GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg05.vShipName  , 112, 120);   m_xRxAisMsg.xMsg05.vShipName[LEN_MAX_SHIP_NAME] = 0x00;

    m_xRxAisMsg.xMsg05.dShipType            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 232,   8);
    m_xRxAisMsg.xMsg05.xAntPos.wA           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 240,   9);
    m_xRxAisMsg.xMsg05.xAntPos.wB           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 249,   9);
    m_xRxAisMsg.xMsg05.xAntPos.wC           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 258,   6);
    m_xRxAisMsg.xMsg05.xAntPos.wD           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 264,   6);
    m_xRxAisMsg.xMsg05.dEPFS                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 270,   4);

    m_xRxAisMsg.xMsg05.dETAMonth            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 274,   4);
    m_xRxAisMsg.xMsg05.dETADay              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 278,   5);
    m_xRxAisMsg.xMsg05.dETAHour             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 283,   5);
    m_xRxAisMsg.xMsg05.dETAMinute           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 288,   6);
    m_xRxAisMsg.xMsg05.dDraught             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 294,   8);

    GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg05.vDest      , 302, 120);

    m_xRxAisMsg.xMsg05.dHasDTE              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 422,   1);
}

/**
 * @brief Get AIS message 06 field data
 */
void CVdlRxMgr::GetMsg06FieldData(void)
{
    int   nSize, nPosX;

    GetMsg00FieldData();

    m_xRxAisMsg.xMsg06.dSqncNO              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg06.dDestID              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
    m_xRxAisMsg.xMsg06.dRetransmit          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,   1);
    m_xRxAisMsg.xMsg06.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  71,   1);
    m_xRxAisMsg.xMsg06.dDAC                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  72,  10);
    m_xRxAisMsg.xMsg06.dFID                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  82,   6);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 1008)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 1008;

    nSize = 0;
    nPosX = 82 + 6;
    while (nPosX < m_xRxAisMsg.xMsg00.wMsgBitsLen)
    {
        m_xRxAisMsg.xMsg06.pDataBuff[nSize] = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  nPosX,   8);
        nSize++;
        nPosX += 8;
    }
    m_xRxAisMsg.xMsg06.pDataBuff[nSize]     = 0x00;
    m_xRxAisMsg.xMsg06.dDataSize            = nSize;
}

/**
 * @brief Get AIS message 07 field data
 */
void CVdlRxMgr::GetMsg07FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg07.dDestID2             = 0;
    m_xRxAisMsg.xMsg07.dSqncNO2             = 0;
    m_xRxAisMsg.xMsg07.dDestID3             = 0;
    m_xRxAisMsg.xMsg07.dSqncNO3             = 0;
    m_xRxAisMsg.xMsg07.dDestID4             = 0;
    m_xRxAisMsg.xMsg07.dSqncNO4             = 0;

    m_xRxAisMsg.xMsg07.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg07.dDestID1             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
    m_xRxAisMsg.xMsg07.dSqncNO1             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,   2);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen >  72)
    {
        m_xRxAisMsg.xMsg07.dDestID2         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  72,  30);
        m_xRxAisMsg.xMsg07.dSqncNO2         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 102,   2);
    }
    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 104)
    {
        m_xRxAisMsg.xMsg07.dDestID3         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 104,  30);
        m_xRxAisMsg.xMsg07.dSqncNO3         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 134,   2);
    }
    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 136)
    {
        m_xRxAisMsg.xMsg07.dDestID4         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 136,  30);
        m_xRxAisMsg.xMsg07.dSqncNO4         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 166,   2);
    }
}

/**
 * @brief Get AIS message 08 field data
 */
void CVdlRxMgr::GetMsg08FieldData(void)
{
    int   nSize, nPosX;

    GetMsg00FieldData();

    m_xRxAisMsg.xMsg08.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg08.dDAC                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  10);
    m_xRxAisMsg.xMsg08.dFID                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  50,   6);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 1008)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 1008;

    nSize = 0;
    nPosX = 50 + 6;
    while (nPosX < m_xRxAisMsg.xMsg00.wMsgBitsLen)
    {
        m_xRxAisMsg.xMsg08.pDataBuff[nSize] = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  nPosX,   8);
        nSize++;
        nPosX += 8;
    }
    m_xRxAisMsg.xMsg08.pDataBuff[nSize]     = 0x00;
    m_xRxAisMsg.xMsg08.dDataSize            = nSize;
}

/**
 * @brief Get AIS message 09 field data
 */
void CVdlRxMgr::GetMsg09FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg09.dAltitude            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,  12);
    m_xRxAisMsg.xMsg09.dSOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  50,  10);
    m_xRxAisMsg.xMsg09.dPosAcc              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  60,   1);
    m_xRxAisMsg.xMsg09.xFullPosX.xPosH.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  61,  28);
    m_xRxAisMsg.xMsg09.xFullPosX.xPosH.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  89,  27);

    GetAisHighPosDataByMsg(&(m_xRxAisMsg.xMsg09.xFullPosX.xPosH));
    if (!IsValidAisHighPOS(&m_xRxAisMsg.xMsg09.xFullPosX.xPosH))
        ClearFullPosToNULL(&m_xRxAisMsg.xMsg09.xFullPosX);

    m_xRxAisMsg.xMsg09.dCOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 116,  12);
    m_xRxAisMsg.xMsg09.dTimeStamp           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 128,   6);
    m_xRxAisMsg.xMsg09.dAltSensor           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 134,   1);
    m_xRxAisMsg.xMsg09.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 135,   7);
    m_xRxAisMsg.xMsg09.dDTE                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 142,   1);
    m_xRxAisMsg.xMsg09.dSpareY              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 143,   3);
    m_xRxAisMsg.xMsg09.dAssignedMode        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 146,   1);
    m_xRxAisMsg.xMsg09.dRAIM                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 147,   1);
    m_xRxAisMsg.xMsg09.dComStateSelector    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 148,   1);
    m_xRxAisMsg.xMsg09.dComStateSyncState   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 149,   2);
    if (m_xRxAisMsg.xMsg09.dComStateSelector == COMSTAT_SELECTOR_SOTDMA)
    {
        m_xRxAisMsg.xMsg09.dComStateTimeOut = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 151,   3);
        m_xRxAisMsg.xMsg09.dComStateSubMsg  = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 154,  14);
    }
    else
    {
        m_xRxAisMsg.xMsg09.dComStateSlotInc = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 151,  13);
        m_xRxAisMsg.xMsg09.dComStateNoSlots = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 164,   3);
        m_xRxAisMsg.xMsg09.dComStateKeepFlag= GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 167,   1);
    }

    AisFullPosToFLOAT(&(m_xRxAisMsg.xMsg09.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg09.xFullPosX.xPosF));
    AisFullPosToGRID (&(m_xRxAisMsg.xMsg09.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg09.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message 10 field data
 */
void CVdlRxMgr::GetMsg10FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg10.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg10.dDestID              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
    m_xRxAisMsg.xMsg10.dSpareY              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,   2);
}

/**
 * @brief Get AIS message 12 field data
 */
void CVdlRxMgr::GetMsg12FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg12.dSqncNO              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg12.dDestID              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
    m_xRxAisMsg.xMsg12.dRetransmit          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,   1);
    m_xRxAisMsg.xMsg12.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  71,   1);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 1008)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 1008;

    m_xRxAisMsg.xMsg12.dDataSize            = GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg12.pDataBuff  ,  72, m_xRxAisMsg.xMsg00.wMsgBitsLen - 72);
}

/**
 * @brief Get AIS message 14 field data
 */
void CVdlRxMgr::GetMsg14FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg14.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 1008)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 1008;

    m_xRxAisMsg.xMsg14.dDataSize            = GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg12.pDataBuff  ,  40, m_xRxAisMsg.xMsg00.wMsgBitsLen - 40);
}

/**
 * @brief Get AIS message 15 field data
 */
void CVdlRxMgr::GetMsg15FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg15.dMsgID12             = 0;
    m_xRxAisMsg.xMsg15.dSltOF12             = 0;

    m_xRxAisMsg.xMsg15.dDestID2             = 0;
    m_xRxAisMsg.xMsg15.dMsgID21             = 0;
    m_xRxAisMsg.xMsg15.dSltOF21             = 0;

    m_xRxAisMsg.xMsg15.dSpareW              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg15.dDestID1             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
    m_xRxAisMsg.xMsg15.dMsgID11             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,   6);
    m_xRxAisMsg.xMsg15.dSltOF11             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  76,  12);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen >  88)
    {
        m_xRxAisMsg.xMsg15.dSpareX          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  88,   2);
        m_xRxAisMsg.xMsg15.dMsgID12         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  90,   6);
        m_xRxAisMsg.xMsg15.dSltOF12         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  96,  12);
    }
    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 108)
    {
        m_xRxAisMsg.xMsg15.dSpareY          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 108,   2);
        m_xRxAisMsg.xMsg15.dDestID2         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 110,  30);
        m_xRxAisMsg.xMsg15.dMsgID21         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 140,   6);
        m_xRxAisMsg.xMsg15.dSltOF21         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 146,  12);
        m_xRxAisMsg.xMsg15.dSpareZ          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 158,   2);
    }
}

/**
 * @brief Get AIS message 16 field data
 */
void CVdlRxMgr::GetMsg16FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg16.dDestIDB             = 0;
    m_xRxAisMsg.xMsg16.dOffsetB             = 0;
    m_xRxAisMsg.xMsg16.dIncrementB          = 0;

    m_xRxAisMsg.xMsg16.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg16.dDestIDA             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
    m_xRxAisMsg.xMsg16.dOffsetA             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,  12);
    m_xRxAisMsg.xMsg16.dIncrementA          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  82,  10);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen >  96)
    {
        m_xRxAisMsg.xMsg16.dDestIDB         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  92,  30);
        m_xRxAisMsg.xMsg16.dOffsetB         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 122,  12);
        m_xRxAisMsg.xMsg16.dIncrementB      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 134,  10);
    }
}

/**
 * @brief Get AIS message 17 field data
 */
void CVdlRxMgr::GetMsg17FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg17.dSpareX             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg17.xAllPosX.xPosL.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  18);
    m_xRxAisMsg.xMsg17.xAllPosX.xPosL.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  58,  17);

    GetAisLowPosDataByMsg(&(m_xRxAisMsg.xMsg17.xAllPosX.xPosL));
    if (!IsValidAisLowPOS(&m_xRxAisMsg.xMsg17.xAllPosX.xPosL))
        ClearHalfPosToNULL(&m_xRxAisMsg.xMsg17.xAllPosX);
    CalcFullPosByLow(&(m_xRxAisMsg.xMsg17.xAllPosX));

    m_xRxAisMsg.xMsg17.dSpareY        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  75,   5);

    m_xRxAisMsg.xMsg17.dRtcmMsgType= GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  80,   6);
    m_xRxAisMsg.xMsg17.wZcount        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  96,  13);
    m_xRxAisMsg.xMsg17.bHealth        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 117,   3);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 816)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 816;

    m_xRxAisMsg.xMsg17.dDataSize            = (m_xRxAisMsg.xMsg00.wMsgBitsLen - 80) / 8;
    if (m_xRxAisMsg.xMsg17.dDataSize > 0)
        memmove(m_xRxAisMsg.xMsg17.pDataBuff, m_pRxPacketData + 80 / 8, m_xRxAisMsg.xMsg17.dDataSize);
}

/**
 * @brief Get AIS message 18 field data
 */
void CVdlRxMgr::GetMsg18FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg18.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   8);
    m_xRxAisMsg.xMsg18.dSOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  46,  10);
    m_xRxAisMsg.xMsg18.dPosAcc              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  56,   1);
    m_xRxAisMsg.xMsg18.xFullPosX.xPosH.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  57,  28);
    m_xRxAisMsg.xMsg18.xFullPosX.xPosH.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  85,  27);

    GetAisHighPosDataByMsg(&(m_xRxAisMsg.xMsg18.xFullPosX.xPosH));
    if (!IsValidAisHighPOS(&m_xRxAisMsg.xMsg18.xFullPosX.xPosH))
        ClearFullPosToNULL(&m_xRxAisMsg.xMsg18.xFullPosX);

    m_xRxAisMsg.xMsg18.dCOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 112,  12);
    m_xRxAisMsg.xMsg18.dHDG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 124,   9);
    m_xRxAisMsg.xMsg18.dTimeStamp           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 133,   6);

    m_xRxAisMsg.xMsg18.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 139,   2);
    m_xRxAisMsg.xMsg18.dUnitFlag            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 141,   1);
    m_xRxAisMsg.xMsg18.dDispFlag            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 142,   1);
    m_xRxAisMsg.xMsg18.dDscFlag             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 143,   1);
    m_xRxAisMsg.xMsg18.dBandFlag            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 144,   1);
    m_xRxAisMsg.xMsg18.dMsg22Flag           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 145,   1);
    m_xRxAisMsg.xMsg18.dModeFlag            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 146,   1);
    m_xRxAisMsg.xMsg18.dRAIM                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 147,   1);

    m_xRxAisMsg.xMsg18.dComStateSelector    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 148,   1);
    m_xRxAisMsg.xMsg18.dComStateSyncState   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 149,   2);
    if (m_xRxAisMsg.xMsg18.dComStateSelector == COMSTAT_SELECTOR_SOTDMA)        // 0: SOTDMA Comm State (ITU-R M.1371-5)
    {
        m_xRxAisMsg.xMsg18.dComStateTimeOut = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 151,   3);
        m_xRxAisMsg.xMsg18.dComStateSubMsg  = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 154,  14);
    }
    else
    {
        m_xRxAisMsg.xMsg18.dComStateSlotInc = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 151,  13);
        m_xRxAisMsg.xMsg18.dComStateNoSlots = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 164,   3);
        m_xRxAisMsg.xMsg18.dComStateKeepFlag= GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 167,   1);
    }

    AisFullPosToFLOAT(&(m_xRxAisMsg.xMsg18.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg18.xFullPosX.xPosF));
    AisFullPosToGRID (&(m_xRxAisMsg.xMsg18.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg18.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message 19 field data
 */
void CVdlRxMgr::GetMsg19FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg19.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   8);
    m_xRxAisMsg.xMsg19.dSOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  46,  10);
    m_xRxAisMsg.xMsg19.dPosAcc              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  56,   1);
    m_xRxAisMsg.xMsg19.xFullPosX.xPosH.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  57,  28);
    m_xRxAisMsg.xMsg19.xFullPosX.xPosH.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  85,  27);

    GetAisHighPosDataByMsg(&(m_xRxAisMsg.xMsg19.xFullPosX.xPosH));
    if (!IsValidAisHighPOS(&m_xRxAisMsg.xMsg19.xFullPosX.xPosH))
        ClearFullPosToNULL(&m_xRxAisMsg.xMsg19.xFullPosX);

    m_xRxAisMsg.xMsg19.dCOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 112,  12);
    m_xRxAisMsg.xMsg19.dHDG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 124,   9);
    m_xRxAisMsg.xMsg19.dTimeStamp           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 133,   6);

    m_xRxAisMsg.xMsg19.dSpareY              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 139,   4);
    GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg19.vShipName  , 143, 120);   m_xRxAisMsg.xMsg19.vShipName[LEN_MAX_SHIP_NAME] = 0x00;
    m_xRxAisMsg.xMsg19.dShipType            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 263,   8);
    m_xRxAisMsg.xMsg19.xAntPos.wA           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 271,   9);
    m_xRxAisMsg.xMsg19.xAntPos.wB           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 280,   9);
    m_xRxAisMsg.xMsg19.xAntPos.wC           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 289,   6);
    m_xRxAisMsg.xMsg19.xAntPos.wD           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 295,   6);
    m_xRxAisMsg.xMsg19.dEPFS                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 301,   4);
    m_xRxAisMsg.xMsg19.dRAIM                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 305,   1);
    m_xRxAisMsg.xMsg19.dDTE                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 306,   1);
    m_xRxAisMsg.xMsg19.dModeFlag            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 307,   1);
    m_xRxAisMsg.xMsg19.dSpareZ              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 308,   4);

    AisFullPosToFLOAT(&(m_xRxAisMsg.xMsg19.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg19.xFullPosX.xPosF));
    AisFullPosToGRID (&(m_xRxAisMsg.xMsg19.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg19.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message 20 field data
 */
void CVdlRxMgr::GetMsg20FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg20.dOffsetNo2           = 0;
    m_xRxAisMsg.xMsg20.dNoOfSlot2           = 0;
    m_xRxAisMsg.xMsg20.dTimeOut2            = 0;
    m_xRxAisMsg.xMsg20.dIncrement2          = 0;

    m_xRxAisMsg.xMsg20.dOffsetNo3           = 0;
    m_xRxAisMsg.xMsg20.dNoOfSlot3           = 0;
    m_xRxAisMsg.xMsg20.dTimeOut3            = 0;
    m_xRxAisMsg.xMsg20.dIncrement3          = 0;

    m_xRxAisMsg.xMsg20.dOffsetNo4           = 0;
    m_xRxAisMsg.xMsg20.dNoOfSlot4           = 0;
    m_xRxAisMsg.xMsg20.dTimeOut4            = 0;
    m_xRxAisMsg.xMsg20.dIncrement4          = 0;

    m_xRxAisMsg.xMsg20.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg20.dOffsetNo1           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  12);
    m_xRxAisMsg.xMsg20.dNoOfSlot1           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  52,   4);
    m_xRxAisMsg.xMsg20.dTimeOut1            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  56,   3);
    m_xRxAisMsg.xMsg20.dIncrement1          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  59,  11);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen >  70)
    {
        m_xRxAisMsg.xMsg20.dOffsetNo2       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  70,  12);
        m_xRxAisMsg.xMsg20.dNoOfSlot2       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  82,   4);
        m_xRxAisMsg.xMsg20.dTimeOut2        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  86,   3);
        m_xRxAisMsg.xMsg20.dIncrement2      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  89,  11);
    }

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 100)
    {
        m_xRxAisMsg.xMsg20.dOffsetNo3       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 100,  12);
        m_xRxAisMsg.xMsg20.dNoOfSlot3       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 112,   4);
        m_xRxAisMsg.xMsg20.dTimeOut3        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 116,   3);
        m_xRxAisMsg.xMsg20.dIncrement3      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 119,  11);
    }

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 100)
    {
        m_xRxAisMsg.xMsg20.dOffsetNo4       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 130,  12);
        m_xRxAisMsg.xMsg20.dNoOfSlot4       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 142,   4);
        m_xRxAisMsg.xMsg20.dTimeOut4        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 146,   3);
        m_xRxAisMsg.xMsg20.dIncrement4      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 149,  11);
    }
}

/**
 * @brief Get AIS message 21 field data
 */
void CVdlRxMgr::GetMsg21FieldData(void)
{
    int  nTempX;

    GetMsg00FieldData();

    memset(m_xRxAisMsg.xMsg21.vExtnName, 0x00, sizeof(m_xRxAisMsg.xMsg21.vExtnName));

    m_xRxAisMsg.xMsg21.dAtonType            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   5);
    GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg21.vAtonName  ,  43, 120);      m_xRxAisMsg.xMsg21.vAtonName[LEN_MAX_SHIP_NAME] = 0x00;
    m_xRxAisMsg.xMsg21.dPosAcc              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 163,   1);
    m_xRxAisMsg.xMsg21.xFullPosX.xPosH.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 164,  28);
    m_xRxAisMsg.xMsg21.xFullPosX.xPosH.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 192,  27);

    GetAisHighPosDataByMsg(&(m_xRxAisMsg.xMsg21.xFullPosX.xPosH));
    if (!IsValidAisHighPOS(&m_xRxAisMsg.xMsg21.xFullPosX.xPosH))
        ClearFullPosToNULL(&m_xRxAisMsg.xMsg21.xFullPosX);

    m_xRxAisMsg.xMsg21.xAntPos.wA           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 219,   9);
    m_xRxAisMsg.xMsg21.xAntPos.wB           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 228,   9);
    m_xRxAisMsg.xMsg21.xAntPos.wC           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 237,   6);
    m_xRxAisMsg.xMsg21.xAntPos.wD           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 243,   6);

    m_xRxAisMsg.xMsg21.dEPFS                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 249,   4);
    m_xRxAisMsg.xMsg21.dTimeStamp           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 253,   6);
    m_xRxAisMsg.xMsg21.dOffPosInd           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 259,   1);
    m_xRxAisMsg.xMsg21.dAtonStatus          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 260,   8);
    m_xRxAisMsg.xMsg21.dRAIM                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 268,   1);
    m_xRxAisMsg.xMsg21.dVirtualAton         = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 269,   1);
    m_xRxAisMsg.xMsg21.dModeFlag            = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 270,   1);

    m_xRxAisMsg.xMsg21.dSpareX              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 271,   1);

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 272)
    {
        nTempX = (m_xRxAisMsg.xMsg00.wMsgBitsLen - 272) / 6;
        if (nTempX > 14)
            nTempX = 14;

        GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg21.vExtnName  , 272, nTempX * 6);
    }

    AisFullPosToFLOAT(&(m_xRxAisMsg.xMsg21.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg21.xFullPosX.xPosF));
    AisFullPosToGRID (&(m_xRxAisMsg.xMsg21.xFullPosX.xPosH), &(m_xRxAisMsg.xMsg21.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message 22 field data
 */
void CVdlRxMgr::GetMsg22FieldData(void)
{
    GetMsg00FieldData();

    ClearHalfPosToNULL(&m_xRxAisMsg.xMsg22.xAllPos1);
    ClearHalfPosToNULL(&m_xRxAisMsg.xMsg22.xAllPos2);
    m_xRxAisMsg.xMsg22.dDestID1     = 0;
    m_xRxAisMsg.xMsg22.dDestID2     = 0;

    m_xRxAisMsg.xMsg22.dSpareX      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);
    m_xRxAisMsg.xMsg22.dChannelA    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  12);
    m_xRxAisMsg.xMsg22.dChannelB    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  52,  12);
    m_xRxAisMsg.xMsg22.dTxRxMode    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  64,   4);
    m_xRxAisMsg.xMsg22.dPower       = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  68,   1);

    if (GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 139,   1) == 0)
    {
        m_xRxAisMsg.xMsg22.xAllPos1.xPosL.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  69,  18);
        m_xRxAisMsg.xMsg22.xAllPos1.xPosL.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  87,  17);

        GetAisLowPosDataByMsg(&(m_xRxAisMsg.xMsg22.xAllPos1.xPosL));
        if (!IsValidAisLowPOS(&m_xRxAisMsg.xMsg22.xAllPos1.xPosL))
            ClearHalfPosToNULL(&m_xRxAisMsg.xMsg22.xAllPos1);

        m_xRxAisMsg.xMsg22.xAllPos2.xPosL.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 104,  18);
        m_xRxAisMsg.xMsg22.xAllPos2.xPosL.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 122,  17);

        GetAisLowPosDataByMsg(&(m_xRxAisMsg.xMsg22.xAllPos2.xPosL));
        if (!IsValidAisLowPOS(&m_xRxAisMsg.xMsg22.xAllPos2.xPosL))
            ClearHalfPosToNULL(&m_xRxAisMsg.xMsg22.xAllPos2);

        CalcFullPosByLow(&(m_xRxAisMsg.xMsg22.xAllPos1));
        CalcFullPosByLow(&(m_xRxAisMsg.xMsg22.xAllPos2));
    }
    else
    {
        m_xRxAisMsg.xMsg22.dDestID1 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  69,  30);
        m_xRxAisMsg.xMsg22.dDestID2 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 104,  30);
    }

    m_xRxAisMsg.xMsg22.dBroadOrAddr = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 139,   1);
    m_xRxAisMsg.xMsg22.dBandwidthA  = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 140,   1);
    m_xRxAisMsg.xMsg22.dBandwidthB  = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 141,   1);
    m_xRxAisMsg.xMsg22.dTrZoneSize  = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 142,   3);

    m_xRxAisMsg.xMsg22.dSpareY      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 145,  23);
}

/**
 * @brief Get AIS message 23 field data
 */
void CVdlRxMgr::GetMsg23FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg23.dSpareX             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);

    m_xRxAisMsg.xMsg23.xAllPos1.xPosL.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  18);        // in 1/10 min
    m_xRxAisMsg.xMsg23.xAllPos1.xPosL.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  58,  17);        // in 1/10 min

    GetAisLowPosDataByMsg(&(m_xRxAisMsg.xMsg23.xAllPos1.xPosL));
    if(!IsValidAisLowPOS(&m_xRxAisMsg.xMsg23.xAllPos1.xPosL))
        ClearHalfPosToNULL(&m_xRxAisMsg.xMsg23.xAllPos1);

    m_xRxAisMsg.xMsg23.xAllPos2.xPosL.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  75,  18);        // in 1/10 min
    m_xRxAisMsg.xMsg23.xAllPos2.xPosL.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  93,  17);        // in 1/10 min

    GetAisLowPosDataByMsg(&(m_xRxAisMsg.xMsg23.xAllPos2.xPosL));
    if(!IsValidAisLowPOS(&m_xRxAisMsg.xMsg23.xAllPos2.xPosL))
        ClearHalfPosToNULL(&m_xRxAisMsg.xMsg23.xAllPos2);

    m_xRxAisMsg.xMsg23.dStnType     = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 110,   4);
    m_xRxAisMsg.xMsg23.dShipType    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 114,   8);

    m_xRxAisMsg.xMsg23.dSpareY      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 122,  22);
    m_xRxAisMsg.xMsg23.dTxRxMode    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 144,   2);
    m_xRxAisMsg.xMsg23.dRptInterval = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 146,   4);
    m_xRxAisMsg.xMsg23.dQuietTime   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 150,   4);
    m_xRxAisMsg.xMsg23.dSpareZ      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 154,   6);

    CalcFullPosByLow(&(m_xRxAisMsg.xMsg23.xAllPos1));
    CalcFullPosByLow(&(m_xRxAisMsg.xMsg23.xAllPos2));
}

/**
 * @brief Get AIS message 24 field data
 */
void CVdlRxMgr::GetMsg24FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg24.dPartNumber = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   2);

    if (m_xRxAisMsg.xMsg24.dPartNumber == 0)
    {
        // 0=PART-A
        GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg24.vShipName  ,  40, 120);
        m_xRxAisMsg.xMsg24.vShipName[LEN_MAX_SHIP_NAME] = 0x00;
    }
    else if (m_xRxAisMsg.xMsg24.dPartNumber == 1)
    {
        // 1=PART-B
        m_xRxAisMsg.xMsg24.dShipType    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,   8);

        GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg24.vVendorID  ,  48,  42);
        m_xRxAisMsg.xMsg24.vVendorID[LEN_MAX_VENDORID] = 0x00;

        GetAisMsgFieldSTR((UCHAR *)m_pRxPacketData, (char *)m_xRxAisMsg.xMsg24.vCallSign  ,  90,  42);
        m_xRxAisMsg.xMsg24.vCallSign[LEN_MAX_CALLSIGN] = 0x00;

        m_xRxAisMsg.xMsg24.xAntPos.wA   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 132,   9);
        m_xRxAisMsg.xMsg24.xAntPos.wB   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 141,   9);
        m_xRxAisMsg.xMsg24.xAntPos.wC   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 150,   6);
        m_xRxAisMsg.xMsg24.xAntPos.wD   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 156,   6);
        m_xRxAisMsg.xMsg24.dEPFS        = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 162,   4);

        m_xRxAisMsg.xMsg24.dSpareX      = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, 166,   2);
    }
}

/**
 * @brief Get AIS message 25 field data
 */
void CVdlRxMgr::GetMsg25FieldData(void)
{
    int   nSize, nPosX;

    GetMsg00FieldData();

    m_xRxAisMsg.xMsg25.dDestInd             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   1);
    m_xRxAisMsg.xMsg25.dBinFlag             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  39,   1);

    nPosX = 40;
    if (m_xRxAisMsg.xMsg25.dDestInd == 1)              // 1=addressed
    {
        m_xRxAisMsg.xMsg25.dDestID          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
        nPosX = 72;
    }

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 168)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 168;

    nSize = 0;
    while (nPosX < m_xRxAisMsg.xMsg00.wMsgBitsLen)
    {
        m_xRxAisMsg.xMsg25.pDataBuff[nSize] = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  nPosX,   8);
        nSize++;
        nPosX += 8;
    }
    m_xRxAisMsg.xMsg25.pDataBuff[nSize]     = 0x00;
    m_xRxAisMsg.xMsg25.dDataSize            = nSize;
}

/**
 * @brief Get AIS message 26 field data
 */
void CVdlRxMgr::GetMsg26FieldData(void)
{
    int   nSize, nPosX;

    GetMsg00FieldData();

    m_xRxAisMsg.xMsg26.dDestInd             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   1);
    m_xRxAisMsg.xMsg26.dBinFlag             = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  39,   1);

    nPosX = 40;
    if (m_xRxAisMsg.xMsg26.dDestInd == 1)              // 1=addressed
    {
        m_xRxAisMsg.xMsg26.dDestID          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,  30);
        nPosX = 72;
    }

    if (m_xRxAisMsg.xMsg00.wMsgBitsLen > 1064)
        m_xRxAisMsg.xMsg00.wMsgBitsLen = 1064;

    m_xRxAisMsg.xMsg26.dComStateSelector    = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen - 20,   1);
    m_xRxAisMsg.xMsg26.dComStateSyncState   = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen - 19,   2);
    if (m_xRxAisMsg.xMsg26.dComStateSelector == COMSTAT_SELECTOR_SOTDMA)
    {
        m_xRxAisMsg.xMsg26.dComStateTimeOut = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen - 17,   3);
        m_xRxAisMsg.xMsg26.dComStateSubMsg  = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen - 14,  14);
    }
    else
    {
        m_xRxAisMsg.xMsg26.dComStateSlotInc = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen - 17,  13);
        m_xRxAisMsg.xMsg26.dComStateNoSlots = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen -  4,   3);
        m_xRxAisMsg.xMsg26.dComStateKeepFlag= GetAisMsgFieldINT((UCHAR *)m_pRxPacketData, m_xRxAisMsg.xMsg00.wMsgBitsLen -  1,   1);
    }

    nSize = 0;
    while (nPosX < m_xRxAisMsg.xMsg00.wMsgBitsLen)
    {
        m_xRxAisMsg.xMsg26.pDataBuff[nSize] = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  nPosX,   8);
        nSize++;
        nPosX += 8;
    }
    m_xRxAisMsg.xMsg26.pDataBuff[nSize]     = 0x00;
    m_xRxAisMsg.xMsg26.dDataSize            = nSize;
}

/**
 * @brief Get AIS message 27 field data
 */
void CVdlRxMgr::GetMsg27FieldData(void)
{
    GetMsg00FieldData();

    m_xRxAisMsg.xMsg27.dPosAcc              = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  38,   1);
    m_xRxAisMsg.xMsg27.dRAIM                = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  39,   1);
    m_xRxAisMsg.xMsg27.dNavStatus           = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  40,   4);

    m_xRxAisMsg.xMsg27.xFullPosX.xPosL.nLON = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  44,  18);        // 1/10 min
    m_xRxAisMsg.xMsg27.xFullPosX.xPosL.nLAT = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  64,  17);        // 1/10 min

    m_xRxAisMsg.xMsg27.dSOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  81,   6);
    m_xRxAisMsg.xMsg27.dCOG                 = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  87,   9);
    m_xRxAisMsg.xMsg27.dPosLatency          = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  96,   1);

    AisLowPosToFLOAT(&(m_xRxAisMsg.xMsg27.xFullPosX.xPosL), &(m_xRxAisMsg.xMsg27.xFullPosX.xPosF));
    AisLowPosToGRID (&(m_xRxAisMsg.xMsg27.xFullPosX.xPosL), &(m_xRxAisMsg.xMsg27.xFullPosX.xPosG));
}

/**
 * @brief Get AIS message reserved field data
 */
void CVdlRxMgr::GetMsgReservedFieldData(void)
{
    int   nSize, nPosX;

    GetMsg00FieldData();

    nSize = 0;
    nPosX = 38;
    while (nPosX < m_xRxAisMsg.xMsg00.wMsgBitsLen)
    {
        m_xRxAisMsg.xMsgReserved.pDataBuff[nSize] = GetAisMsgFieldINT((UCHAR *)m_pRxPacketData,  nPosX,   8);
        nSize++;
        nPosX += 8;
    }
    m_xRxAisMsg.xMsgReserved.pDataBuff[nSize]     = 0x00;
    m_xRxAisMsg.xMsgReserved.dDataSize            = nSize;
}

/**
 * @brief Process common AIS message fields
 * @return True if success, false otherwise
 */
bool CVdlRxMgr::ProcessCommon(void)
{
    xAISMSG99 *pMsg = &m_xRxAisMsg;
    xAISMSG00 *pMsg00 = &m_xRxAisMsg.xMsg00;

    INT8 nNumSlots = (pMsg00->wMsgByteLen+2) / NUM_BYTES_PER_SLOT;        // 2 : FCS (2-bytes)
    if((pMsg00->wMsgByteLen+2) % NUM_BYTES_PER_SLOT)
        nNumSlots++;

    DWORD   dwSyncState;
    DWORD   dwTimeOut;
    DWORD   dwSubMsg;

    DWORD   dwSlotInc;
    DWORD   dwNumOfSlots;
    DWORD   dwKeepFlag;

    UINT8   bScheme = TDMA_NONE;
    bool    bAvailUtcIndirectSync = false;

    bool    bUpdateFrameMap = true;

    if(pMsg00->dSrcMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        if(CTestModeMgr::getInst()->IsTestModeRunning())
        {
            if(CTestModeMgr::getInst()->IsRunTxLoopbackTest())
            {
                if(m_pChannel == CLayerNetwork::getInst()->GetChPrimary())
                    CTestModeMgr::getInst()->IncTxLoopbackRcvCntCH1();
                else
                    CTestModeMgr::getInst()->IncTxLoopbackRcvCntCH2();
                CMKD::getInst()->SendOutTxLoopbackRcvCnt();
            }
            return true;
        }

        return false;
    }

    WORD wFrSlotID = GetFrameMapSlotID(pMsg00->wMsgFrameNO, pMsg00->wMsgSlotNO);
    wFrSlotID = m_pChannel->GetShiftedMapSlotID(wFrSlotID);

    // Update user directory data
    CUserDirMgr::getInst()->UpdateDirData(this);

    switch(m_bRxAisMsgNoID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
        bAvailUtcIndirectSync = true;
        bScheme     = TDMA_SOTDMA;
        dwSyncState = pMsg->xMsg01.dComStateSyncState;
        dwTimeOut   = pMsg->xMsg01.dComStateTimeOut;
        dwSubMsg    = pMsg->xMsg01.dComStateSubMsg;
        break;
    case AIS_MSG_NO_ID_03:
        bScheme     = TDMA_ITDMA;
        dwSyncState = pMsg->xMsg01.dComStateSyncState;
        dwSlotInc   = pMsg->xMsg01.dComStateSlotInc;
        dwNumOfSlots= pMsg->xMsg01.dComStateNoSlots;
        dwKeepFlag  = pMsg->xMsg01.dComStateKeepFlag;
        break;
    case AIS_MSG_NO_ID_04:
    case AIS_MSG_NO_ID_11:
        bAvailUtcIndirectSync = true;
        bScheme     = TDMA_SOTDMA;
        dwSyncState = pMsg->xMsg04.dComStateSyncState;
        dwTimeOut   = pMsg->xMsg04.dComStateTimeOut;
        dwSubMsg    = pMsg->xMsg04.dComStateSubMsg;
        break;
    case AIS_MSG_NO_ID_09:
        if(pMsg->xMsg09.dComStateSelector == COMSTAT_SELECTOR_SOTDMA)                // 0: SOTDMA Comm State (ITU-R M.1371-5)
        {
            bScheme     = TDMA_SOTDMA;
            dwSyncState = pMsg->xMsg09.dComStateSyncState;
            dwTimeOut   = pMsg->xMsg09.dComStateTimeOut;
            dwSubMsg    = pMsg->xMsg09.dComStateSubMsg;
        }
        else
        {
            bScheme     = TDMA_ITDMA;
            dwSyncState = pMsg->xMsg09.dComStateSyncState;
            dwSlotInc   = pMsg->xMsg09.dComStateSlotInc;
            dwNumOfSlots= pMsg->xMsg09.dComStateNoSlots;
            dwKeepFlag  = pMsg->xMsg09.dComStateKeepFlag;
        }
        break;
    case AIS_MSG_NO_ID_18:
        if(pMsg->xMsg18.dUnitFlag == CLASS_B_UNIT_SO)
        {
            if(pMsg->xMsg18.dComStateSelector == COMSTAT_SELECTOR_SOTDMA)            // 0: SOTDMA Comm State (ITU-R M.1371-5)
            {
                bScheme    = TDMA_SOTDMA;
                dwSyncState= pMsg->xMsg18.dComStateSyncState;
                dwTimeOut  = pMsg->xMsg18.dComStateTimeOut;
                dwSubMsg   = pMsg->xMsg18.dComStateSubMsg;
            }
            else
            {
                bScheme     = TDMA_ITDMA;
                dwSyncState = pMsg->xMsg18.dComStateSyncState;
                dwSlotInc   = pMsg->xMsg18.dComStateSlotInc;
                dwNumOfSlots= pMsg->xMsg18.dComStateNoSlots;
                dwKeepFlag  = pMsg->xMsg18.dComStateKeepFlag;
            }
        }
        break;
    case AIS_MSG_NO_ID_26:
        if(pMsg->xMsg26.dComStateSelector == COMSTAT_SELECTOR_SOTDMA)                // 0: SOTDMA Comm State (ITU-R M.1371-5)
        {
            bScheme     = TDMA_SOTDMA;
            dwSyncState = pMsg->xMsg26.dComStateSyncState;
            dwTimeOut   = pMsg->xMsg26.dComStateTimeOut;
            dwSubMsg    = pMsg->xMsg26.dComStateSubMsg;
        }
        else
        {
            bScheme     = TDMA_ITDMA;
            dwSyncState = pMsg->xMsg26.dComStateSyncState;
            dwSlotInc   = pMsg->xMsg26.dComStateSlotInc;
            dwNumOfSlots= pMsg->xMsg26.dComStateNoSlots;
            dwKeepFlag  = pMsg->xMsg26.dComStateKeepFlag;
        }
        break;
    }

    switch(bScheme)
    {
    case TDMA_SOTDMA:
        {
            if(bUpdateFrameMap)
            {
                m_pChannel->UpdateFrameMapSOTDMA(pMsg00->dMsgID, pMsg00->wMsgFrameNO, pMsg00->wMsgSlotNO, nNumSlots, pMsg00->dSrcMMSI, dwSyncState, dwTimeOut, dwSubMsg);
            }

            switch(dwTimeOut)
            {
            case 0:
                // UpdateFrameMap 내에서 처리하므로 여기서는 처리안함!
                break;
            case 1:
                if(!CSensorMgr::getInst()->IsPosUtcFixed(SENSORID_0) && m_xRxAisMsg.xMsg00.dSrcMMSI == CSyncMgr::getInst()->m_uSyncSrcMMSI)
                {
                    // UTC time
                    int nHour = dwSubMsg >> 9;                            // bit [13..9]
                    int nMin = (dwSubMsg >> 2) & 0x7F;                    // bit [8..2]
                    CSyncMgr::getInst()->SetUtcTimeFromVdlMsg(m_xRxAisMsg.xMsg00.dSrcMMSI, nHour, nMin);
                }
                break;
            case 2:
            case 4:
            case 6:
                //-----------------------------------------------------------------------------------------------------
                // ITU-R-1371-5 Annex2 3.3.7.2.3 Sub messages Table 19
                // Slot time-out 2, 4, 6 : Slot number used for this transmission (between 0 and 2 249).
                //-----------------------------------------------------------------------------------------------------
                // IEC-61993-2 7.3.3
                // Repeated messages and Class B messages shall not be used in the indirect synchronisation process
                //-----------------------------------------------------------------------------------------------------
                if(bAvailUtcIndirectSync && pMsg00->dRptInd == 0)
                {
                    CSyncMgr::getInst()->ProcessSyncToOtherSt(this, dwSyncState, dwSubMsg);
                }
                break;
            case 3:
            case 5:
            case 7:
                break;
            }
        }
        break;
    case TDMA_ITDMA:
        {
            if(bUpdateFrameMap)
            {
                m_pChannel->UpdateFrameMapITDMA(pMsg00->dMsgID, pMsg00->wMsgFrameNO, pMsg00->wMsgSlotNO, nNumSlots, pMsg00->dSrcMMSI, dwSyncState, dwSlotInc, dwNumOfSlots, dwKeepFlag, AIS_SLOT_STATUS_EXT_EX);

            }
        }
        break;
    case TDMA_RATDMA:
        break;
    case TDMA_FATDMA:
        break;
    }

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_01_02_03(void)
{
    if(!ProcessCommon()) return false;

    switch(m_xRxAisMsg.xMsg00.dMsgID)
    {
    case AIS_MSG_NO_ID_01:
        CSartMgr::getInst()->CheckAndAddSART(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg01.dNavStatus);
        break;
    case AIS_MSG_NO_ID_02:
        break;
    case AIS_MSG_NO_ID_03:
        m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);
        break;
    }
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_04_11(void)
{
    if(!ProcessCommon()) return false;

    //---------------------------------------------------------------------------------------------
    // IEC-61993-2 6.2.1
    // If date and time from the internal GNSS is not available and Message 4 or 11 is being
    // received, the unit shall use date and time from that message, the seconds shall be omitted.
    //---------------------------------------------------------------------------------------------
    //if(!CSensorMgr::getInst()->IsPosUtcFixed(SENSORID_0))
    {
        CSyncMgr::getInst()->SetUtcDateFromVdlMsg(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg04.dYear, m_xRxAisMsg.xMsg04.dMonth, m_xRxAisMsg.xMsg04.dDay);
        CSyncMgr::getInst()->SetUtcTimeFromVdlMsg(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg04.dHour, m_xRxAisMsg.xMsg04.dMin);

        DEBUG_LOG("SyncUtcTime] from msg-%d, %09d, %d-%d-%d, %d:%d\r\n",
                m_xRxAisMsg.xMsg00.dMsgID, m_xRxAisMsg.xMsg00.dSrcMMSI,
                m_xRxAisMsg.xMsg04.dYear, m_xRxAisMsg.xMsg04.dMonth, m_xRxAisMsg.xMsg04.dDay,
                m_xRxAisMsg.xMsg04.dHour, m_xRxAisMsg.xMsg04.dMin);
    }

    if(m_xRxAisMsg.xMsg00.dMsgID == AIS_MSG_NO_ID_04)
    {
        m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);

        // for base station coverage area
        CUserDirMgr::getInst()->SetLongRangeTxCtrlCode(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg04.dwTxCtrlLR);
    }
    else if(m_xRxAisMsg.xMsg00.dMsgID == AIS_MSG_NO_ID_11)
    {
        ProcessRxMSG_SingleSlotAddrACK();
    }
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_SingleSlotAddrACK(void)
{
    //-------------------------------------------------------------------------------------
    // refer to the table "Use of VDL Messages by a Class B "SO" AIS" in IEC-62287-2 and
    // "Use of VDL Messages by a Class B "CS" AIS" in IEC-62287-1
    //-------------------------------------------------------------------------------------
    if(CSetupMgr::getInst()->IsAisBClass())
        return false;
    //-------------------------------------------------------------------------------------

    TDMA_ONESLOT_ADDRMSG *pTxBuff = NULL;
    UINT8 uTxMsgID     = AIS_MSG_NO_ID_UNDEFINED;
    UINT8 uChOrdinal = m_pChannel->GetChOrdinal();
    UINT8 uAckMsgID  = m_xRxAisMsg.xMsg00.dMsgID;
    UINT  uSrcMMSI     = m_xRxAisMsg.xMsg00.dSrcMMSI;

    if(uAckMsgID == AIS_MSG_NO_ID_11)
    {
        uTxMsgID = AIS_MSG_NO_ID_10;

        int nErrorCode = 0;
        if((pTxBuff = m_pChannel->m_pVdlTxMgr->GetOneSlotAddrTxMsgByUniqueID(uTxMsgID, uSrcMMSI)))
        {
            nErrorCode = ABK_ADDRESSED_ACK_OK;
            m_pChannel->m_pVdlTxMgr->ClearOneSlotAddrTxMsgInfo(pTxBuff);                            // Set bReserveTx into false to release buffer
        }
        else
        {
            nErrorCode = ABK_ADDRESSED_ACK_LATE;
        }

        CMKD::getInst()->SendABKtoPI(uSrcMMSI, uChOrdinal, uTxMsgID, 0, nErrorCode);
        return true;
    }
    return false;
}

bool CVdlRxMgr::ProcessRxMSG_05(void)
{
    if(!ProcessCommon()) return false;

    xDIRDATA* pDirData = CUserDirMgr::getInst()->FindDirDataPtr(m_xRxAisMsg.xMsg00.dSrcMMSI);
    if(pDirData)
        pDirData->dwLastMsg5RcvSec = cTimerSys::getInst()->GetCurTimerSec();

    m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);
    CSartMgr::getInst()->CheckTypeOfSART(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg05.vShipName);

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_06(void)
{
    if(!ProcessCommon()) return false;

    if(!CSetupMgr::getInst()->IsAisBClassCS())
        return ProcessRxMSG_Addressed(m_xRxAisMsg.xMsg06.dDestID, AIS_MSG_NO_ID_07, m_xRxAisMsg.xMsg06.dSqncNO);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_Addressed(UINT dwDestMMSI, UINT8 uAckMsgID, DWORD dwSeqNum)
{
    if(CSetupMgr::getInst()->IsAisBClassCS())
        return false;

    //------------------------------------------------------------------------------------
    // 메시지 6, 12 (주소지정 바이너리 및 안전관련 메시지) 수신 처리
    // 메시지가 수신된 채널로 ACK 메시지를 송신할것!
    // sequence number 는 수신된 메시지와 동일하게 할것!
    // 송신측에서는 4초 내에 ACK 메시지 (7, 13) 을 받지못하면 최대 3회까지 재시도한다.
    //------------------------------------------------------------------------------------

    TDMA_UNSCHE_ACKMSG *pTxBuff = NULL;

    if(dwDestMMSI != cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        WARNING_LOG("rcvMSG-ProcAddr] ignore, msg:%d, dest is not own : %09d\r\n", m_xRxAisMsg.xMsg00.dMsgID, dwDestMMSI);
        return false;            // Do not send VDM
    }

    if((pTxBuff = m_pChannel->m_pVdlTxMgr->GetAckMsgEmptyBuff(uAckMsgID)))
    {
        WORD wRespStartSI = GetFrameMapSlotID(m_xRxAisMsg.xMsg00.wMsgFrameNO, m_xRxAisMsg.xMsg00.wMsgSlotNO);
        m_pChannel->m_pVdlTxMgr->SetAckMsgBuffPtr(pTxBuff, wRespStartSI, m_xRxAisMsg.xMsg00.dSrcMMSI, uAckMsgID, dwSeqNum);
    }
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_07_13(void)
{
    //=========================================================================
    // class B 는 7번 MSG 처리 금지
    //=========================================================================
    if(!ProcessCommon()) return false;

    if(CSetupMgr::getInst()->IsAisBClassCS())
        return true;

    ProcessRxMSG_MultiSlotAddrACK(m_xRxAisMsg.xMsg07.dDestID1, m_xRxAisMsg.xMsg07.dSqncNO1);
    ProcessRxMSG_MultiSlotAddrACK(m_xRxAisMsg.xMsg07.dDestID2, m_xRxAisMsg.xMsg07.dSqncNO2);
    ProcessRxMSG_MultiSlotAddrACK(m_xRxAisMsg.xMsg07.dDestID3, m_xRxAisMsg.xMsg07.dSqncNO3);
    ProcessRxMSG_MultiSlotAddrACK(m_xRxAisMsg.xMsg07.dDestID4, m_xRxAisMsg.xMsg07.dSqncNO4);

    return (m_xRxAisMsg.xMsg07.dDestID1 == cShip::getOwnShipInst()->xStaticData.dMMSI ||
            m_xRxAisMsg.xMsg07.dDestID2 == cShip::getOwnShipInst()->xStaticData.dMMSI ||
            m_xRxAisMsg.xMsg07.dDestID3 == cShip::getOwnShipInst()->xStaticData.dMMSI ||
            m_xRxAisMsg.xMsg07.dDestID4 == cShip::getOwnShipInst()->xStaticData.dMMSI);
}

void CVdlRxMgr::ProcessRxMSG_MultiSlotAddrACK(UINT dwDestMMSI, DWORD dwSeqNum)
{
    if(CSetupMgr::getInst()->IsAisBClassCS())
        return;

    TDMA_UNSCHE_ADDRMSG *pTxBuff = NULL;
    UINT8 uTxMsgID     = AIS_MSG_NO_ID_UNDEFINED;
    UINT8 uChOrdinal = m_pChannel->GetChOrdinal();
    UINT8 uAckMsgID  = m_xRxAisMsg.xMsg00.dMsgID;
    UINT  uSrcMMSI     = m_xRxAisMsg.xMsg00.dSrcMMSI;
    int nErrorCode = 0;

    if(uAckMsgID == AIS_MSG_NO_ID_07)
        uTxMsgID = AIS_MSG_NO_ID_06;
    else if(uAckMsgID == AIS_MSG_NO_ID_13)
        uTxMsgID = AIS_MSG_NO_ID_12;
    else
        return;

    if(dwDestMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        if((pTxBuff = m_pChannel->m_pVdlTxMgr->GetAddrMsgTxInfoByUniqueID(uTxMsgID, uSrcMMSI, dwSeqNum)))
        {
            nErrorCode = ABK_ADDRESSED_ACK_OK;
            m_pChannel->m_pVdlTxMgr->ClearBinMsgBuff_Addressed(pTxBuff);        // Set bReserveTx into false to release buffer
        }
        else
        {
            nErrorCode = ABK_ADDRESSED_ACK_LATE;
        }

        CMKD::getInst()->SendABKtoPI(uSrcMMSI, uChOrdinal, uTxMsgID, dwSeqNum, nErrorCode);            // IEC-61993-2 ******* Table 12, An ABK PI message shall be sent to the PI in any case.
    }
}

bool CVdlRxMgr::ProcessRxMSG_08(void)
{
    if(!ProcessCommon()) return false;
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_09(void)
{
    if(!ProcessCommon()) return false;
    m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_10(void)
{
    //-------------------------------------------------------------------------------------
    // refer to the table "Use of VDL Messages by a Class B "SO" AIS" in IEC-62287-2 and
    // "Use of VDL Messages by a Class B "CS" AIS" in IEC-62287-1
    //-------------------------------------------------------------------------------------
    if(CSetupMgr::getInst()->IsAisBClass())
        return false;

    if(!ProcessCommon()) return false;

    if(m_xRxAisMsg.xMsg10.dDestID != cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        WARNING_LOG("rcvMSG-10] ignore, dest is not own : %09d\r\n", m_xRxAisMsg.xMsg10.dDestID);
        return false;      // Do not send VDM
    }

    if(!CVdlTxMgr::IsTxAvailable())
        return true;

    if(!m_pChannel->IsTxAvailableCh())
        return true;

    m_pChannel->m_pVdlTxMgr->SetUnScheTxMsgDataBuff(AIS_MSG_NO_ID_11, true, RATDMA_SI);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_12(void)
{
    if(!ProcessCommon()) return false;

    if(!CSetupMgr::getInst()->IsAisBClassCS())
        return ProcessRxMSG_Addressed(m_xRxAisMsg.xMsg12.dDestID, AIS_MSG_NO_ID_13, m_xRxAisMsg.xMsg12.dSqncNO);

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_14(void)
{
    if(!ProcessCommon()) return false;

    //--------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.11.1 Minimum keyboard and display (MKD)
    // - Testing AIS-SART : confirm that user IDs of Messages 1 and 14 lead with 97, Message 1 NavStatus is 15, Message 14 text is "SART TEST"
    // - Type approval testing AIS-SART: confirm that user IDs of Messages 1 and 14 lead with 97000, Message 1 NavStatus is 15 and Message 14 text is "SART TEST".
    //--------------------------------------------------------------------------------------------------------------------------------------------------------------
    CSartMgr::getInst()->CheckTypeOfSART(m_xRxAisMsg.xMsg00.dSrcMMSI, (char*)m_xRxAisMsg.xMsg14.pDataBuff);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_15()
{
    //-----------------------------------------------------------
    // refer to ITU-R 1371-5 Ann8 3.13
    // Class A : MSG 3, 5, 24 에 대한 interrogation 처리
    // Class B : MSG 18, 24 에 대한 interrogation 처리
    //-----------------------------------------------------------
    //-------------------------------------------------------------------------------------------------
    // IEC61993-2(ed2.0) 14.1.3.2.2 Required results
    // Check that the EUT transmits the appropriate interrogation response message as requested
    // after defined slot offset. Confirm that the EUT transmits the response on the same channel as
    // where interrogation was received.
    //-------------------------------------------------------------------------------------------------

    if(!ProcessCommon()) return false;

    if(!m_pChannel->IsTxAvailableCh())
        return true;

    ProcessRxMSG_15Sub(m_xRxAisMsg.xMsg15.dDestID1, m_xRxAisMsg.xMsg15.dMsgID11, m_xRxAisMsg.xMsg15.dSltOF11);
    ProcessRxMSG_15Sub(m_xRxAisMsg.xMsg15.dDestID1, m_xRxAisMsg.xMsg15.dMsgID12, m_xRxAisMsg.xMsg15.dSltOF12);
    ProcessRxMSG_15Sub(m_xRxAisMsg.xMsg15.dDestID2, m_xRxAisMsg.xMsg15.dMsgID21, m_xRxAisMsg.xMsg15.dSltOF21);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_15Sub(UINT uDestMMSI, int nReqMsgID, int nSlotOff)
{
    //-------------------------------------------------------------
    // refer to ITU-R 1371-5 Ann8 3.13
    // Class A : MSG 3, 5, 24(partA) 에 대한 interrogation 처리
    // Class B : MSG 18, 24(partA/partB) 에 대한 interrogation 처리
    //-------------------------------------------------------------

    if (   (CSetupMgr::getInst()->IsAisAClass() && (nReqMsgID == AIS_MSG_NO_ID_03 || nReqMsgID == AIS_MSG_NO_ID_05 || nReqMsgID == AIS_MSG_NO_ID_24))
        || (CSetupMgr::getInst()->IsAisBClass() && (nReqMsgID == AIS_MSG_NO_ID_18 || nReqMsgID == AIS_MSG_NO_ID_24)))
    {
        if (!nSlotOff)
        {
            if (uDestMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
            {
                if (nReqMsgID == AIS_MSG_NO_ID_05 || nReqMsgID == AIS_MSG_NO_ID_24)
                    CLayerNetwork::getInst()->ReserveTxStaticVoyageMsg(m_pChannel, true, RATDMA_SLOT_TIMEOUT);
                else
                    m_pChannel->m_pVdlTxMgr->SetUnScheTxMsgDataBuff(nReqMsgID, true, RATDMA_SI);
            }
        }
        else
        {
            // In case slot offset is not zero
            if (IsValidMMSI_BaseSt(m_xRxAisMsg.xMsg00.dSrcMMSI))
            {
                bool bFAtoSO = false;
                if (uDestMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
                {
                    bFAtoSO = true;
                }

                int nNumSlotToReserve = 1;
                if (nReqMsgID == AIS_MSG_NO_ID_05 || nReqMsgID == AIS_MSG_NO_ID_24)
                {
                    nNumSlotToReserve = 2;

                    CLayerNetwork::m_pLastTxChMsg5 = m_pChannel;
                    CLayerNetwork::m_dwLastTxSecMsg5 = cTimerSys::getInst()->GetCurTimerSec();
                    CLayerNetwork::getInst()->InitStaticReportSec();
                }

                m_pChannel->UpdateFrameMapFATDMA(false, nReqMsgID, m_xRxAisMsg.xMsg00.wMsgFrameNO, m_xRxAisMsg.xMsg00.wMsgSlotNO,
                                                            m_xRxAisMsg.xMsg00.dSrcMMSI, nSlotOff, nNumSlotToReserve, 0, 0, bFAtoSO);
            }
        }
    }

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_16(void)
{
    //----------------------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex2. 3.3.6 Assigned operation
    // If a mobile station is outside and not entering a transition zone, a station operating in the autonomous mode,
    // may be commanded to operate according to a specific transmission schedule as defined in Message 16 or 23.
    // Assigned mode applies to alternating operation between both channels.
    // => transitional zone 내에 있을때는 메시지 16 및 23 무시할것!
    // => 할당모드는 두 채널 교대동작에 적용된다!
    //----------------------------------------------------------------------------------------------------------------

    if(!ProcessCommon()) return false;

#ifndef __RS_TEST_REPORT__
    // to test TT_16_6_6_2_Ed2.scn of Ivan Test report
    if(!IsValidMMSI_BaseSt(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-16] ignore, not BS, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("[ASS-rcvMSG-16] too distant BS, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }
#endif

    if(!CSetupMgr::getInst()->IsAisBClassCS())
    {
        // 현재 수행중인 모드가 할당모드로 전환할수없는 상태일때는 메시지를 무시한다.
        if(!CLayerNetwork::getInst()->IsAssignedModeOpAvailable())
        {
            WARNING_LOG("[ASS-rcvMSG-16] Can't process Assigned mode, op : %d\r\n", CLayerNetwork::getInst()->m_nOpPhase);
            return true;
        }

        if(CROSMgr::getInst()->IsTrModeRunning())
        {
            //------------------------------------------------------------------------------------------------------
            // refer to IALA Rec.A-124 Appendex 17 - channel management by an AIS service December 2011, Page 9 of 29
            // Within the transitional zone, Class A shipborne mobile AIS stations will ignore any assignment of
            // higher nominal reporting intervals by shore stations. This guarantees that the broadcasts of mobile
            // AIS stations operating in the transition zone will be received at nominal reporting interval for the
            // benefit of other mobile stations in the immediate vicinity of that station.
            //------------------------------------------------------------------------------------------------------
            return true;
        }

        //----------------------------------------------------------------------------------------------------------------
        // refer to 1371-5 Annex2. 3.3.6 Assigned operation
        // The last received assignment should continue or overwrite the previous assignment.
        // This should also be the case, when two assignments are made in the same Message 16 for the same station.
        // => Destination ID A 와 Destination ID B 가 같으면 두번째 할당명령만 수행한다.
        //----------------------------------------------------------------------------------------------------------------
        if(m_xRxAisMsg.xMsg16.dDestIDA != m_xRxAisMsg.xMsg16.dDestIDB)
            ProcessRxMSG_16Sub(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg16.dDestIDA, m_xRxAisMsg.xMsg16.dOffsetA, m_xRxAisMsg.xMsg16.dIncrementA);

        ProcessRxMSG_16Sub(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg16.dDestIDB, m_xRxAisMsg.xMsg16.dOffsetB, m_xRxAisMsg.xMsg16.dIncrementB);
    }

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_16Sub(DWORD dSrcMMSI, DWORD dDstMMSI, DWORD dwOffset, DWORD dwIncrement)
{
    //--------------------------------------------------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex2. 3.3.6
    // Mobile stations, other than Class A should transmit position reports as directed by Message 16 or 23,
    // and the station should not change its reporting interval for changing course and speed.
    // Class A shipborne mobile AIS stations should apply the same rule unless the autonomous mode
    // requires a shorter reporting interval than the reporting interval as directed by Message 16 or 23.
    // Class A 아닌 경우, 메시지 16 or 23 에 지정된 대로 위치보고 수행할것! 이 모드 시 코스와 속도 변화에 따른 자동모드 보고율은 고려하지 않는다!
    // Class A 인 경우, 자동모드 보고주기가 메시지 16 또는 23 의 지정된 것보다 더 짧은 보고간격을 요구한다면, 자동모드로 전환해야한다!
    // refer to IEC-61993-2 *******
    //--------------------------------------------------------------------------------------------------------------------------------------------

    // ITU-R 1371-5 Annex8. 3.14 If a station receives the value 7, the station should disregard this assignment.
    const INT8   MAX_INC_CLASS_A_INDEX = 7;
    const UINT16 pIncClassATbl[MAX_INC_CLASS_A_INDEX] = {0, 1125, 375, 225, 125, 75, 45};

    // ITU-R 1371-5 Annex8. 3.14 Class B mobile AIS stations should not be assigned a reporting interval of less than 2s.
    const INT8   MAX_INC_CLASS_B_INDEX = 6;
    const UINT16 pIncClassBTbl[MAX_INC_CLASS_B_INDEX] = {0, 1125, 375, 225, 125, 75};

    int nAssignRR = 0;
    int nSlotInc = 0;
    float fReportIntervalSec = 0;
    bool bDoProcCmd = true;

    //-------------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex2. 3.3.6
    // To assign a reporting rate for a station, the parameter increment should be set to zero.
    // The parameter offset should then be interpreted as the number of reports in a time interval of 10 min.
    //-------------------------------------------------------------------------------------------------------
    if(dwIncrement == 0)
    {
        if(dDstMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
        {
            //------------------------------------------
            // Assigned mode of reporting interval
            //------------------------------------------
            nAssignRR = GetAssignedRRby10minRR(dwOffset, 10);            // 10분당 report의 수를 Rr로 계산
            if(nAssignRR > 0)
            {
                fReportIntervalSec = (float)RR_TO_INT_SEC(nAssignRR);
                if(fReportIntervalSec >= REPORT_INTERVALSEC_MIN)
                {
                    if(CSetupMgr::getInst()->IsAisBClass())
                    {
                        if(fReportIntervalSec < 2)  // refer to 1371-5 Annex8. 3.14 Class B mobile AIS stations should not be assigned a reporting interval of less than 2 s.
                            return true;
                    }
                    else
                    {
                        bDoProcCmd = CLayerNetwork::getInst()->IsHigherOrEqualAssignedRR(m_pChannel, fReportIntervalSec);
                    }

                    if(bDoProcCmd)
                    {
                        CLayerNetwork::getInst()->StartAssignedMode_ReportRate(m_xRxAisMsg.xMsg00.dSrcMMSI, ASSIGNED_MODE_BY_MSG16, CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, fReportIntervalSec);
                    }
                }
            }
            else
            {
                WARNING_LOG("[ASSR-MSG16-AssignRI] ignore, invalid offset : %d, RR : %d\r\n", dwOffset, nAssignRR);
            }
        }
        else
        {
            WARNING_LOG("[ASSR-MSG16-AssignRI] ignore, Dest is not own station, dst:%09d, own:%09d\r\n", dDstMMSI, cShip::getOwnShipInst()->xStaticData.dMMSI);
        }
    }
    else
    {
        //----------------------------------------
        // Assigned mode of transmission slots
        //----------------------------------------
        if (   CSetupMgr::getInst()->IsAisAClass() && dwIncrement >= MAX_INC_CLASS_A_INDEX
            || CSetupMgr::getInst()->IsAisBClass() && dwIncrement >= MAX_INC_CLASS_B_INDEX)
            return true;

        if(dDstMMSI == cShip::getOwnShipInst()->xStaticData.dMMSI)
        {
            const UINT16 *pIncrementTbl = CSetupMgr::getInst()->IsAisAClass() ? pIncClassATbl : pIncClassBTbl;
            nSlotInc = pIncrementTbl[dwIncrement];

            if(nSlotInc > 0)
            {
                if(bDoProcCmd)
                {
                    CLayerNetwork::getInst()->StartAssignedMode_Slot(m_pChannel, m_xRxAisMsg.xMsg00.dMsgID, m_xRxAisMsg.xMsg00.dSrcMMSI,
                                                        m_xRxAisMsg.xMsg00.wMsgFrameNO, m_xRxAisMsg.xMsg00.wMsgSlotNO, dwOffset, nSlotInc);
                }
            }
            else
            {
                WARNING_LOG("[ASSS-MSG16-AssignSlot] ignore, invalid param, off : %d, inc : %d -> %d\r\n",
                        dwOffset, dwIncrement, nSlotInc);
            }
        }
        else
        {
            m_pChannel->UpdateFrameMapFATDMA(false, m_xRxAisMsg.xMsg00.dMsgID, m_xRxAisMsg.xMsg00.wMsgFrameNO, m_xRxAisMsg.xMsg00.wMsgSlotNO,
                                                        m_xRxAisMsg.xMsg00.dSrcMMSI, dwOffset, 1, TMO_MAX, nSlotInc);

            WARNING_LOG("[ASSR-MSG16-AssignSlot] ignore, Dest is not own station, dst:%09d, own:%09d\r\n", dDstMMSI, cShip::getOwnShipInst()->xStaticData.dMMSI);
        }
    }
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_17(void)
{
    xDIRDATA *pNewBaseStData;
    UINT  uBaseStID = m_xRxAisMsg.xMsg00.dSrcMMSI;

    if(!IsValidMMSI_BaseSt(uBaseStID))
    {
        WARNING_LOG("rcvMSG-17] ignore, not BS, %09d\r\n", uBaseStID);
        return true;
    }

    if(!ProcessCommon())
        return false;

    if(CSetupMgr::getInst()->GetEnableDgnssByMsg17() == MODE_VAL_OFF)
    {
        WARNING_LOG("rcvMSG-17] ignore, Dgnss By Msg17 not enabled, %d\r\n", CSetupMgr::getInst()->GetEnableDgnssByMsg17());
        return true;
    }

    if(m_xRxAisMsg.xMsg00.wMsgBitsLen < 80 || m_xRxAisMsg.xMsg00.wMsgBitsLen > 816)        // Min.80 bits, Max. 816 bits
    {
        WARNING_LOG("rcvMSG-17] ignore, Msg17 total message bits size invalid : %d\r\n", m_xRxAisMsg.xMsg00.wMsgBitsLen);
        return true;
    }

    if(!(pNewBaseStData = CUserDirMgr::getInst()->FindDirDataPtrBS(uBaseStID)))
    {
        WARNING_LOG("rcv-MSG17] error-1, unregistered BS:%09d\r\n", uBaseStID);
        return true;
    }

    CSensorMgr::getInst()->ProcessDgnssDataFromMsg17(uBaseStID, &(m_xRxAisMsg.xMsg17));
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_18(void)
{
    if(!ProcessCommon()) return false;
    m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_19(void)
{
    //------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 3.17 Message 19: Extended class B equipment position report
    // For future equipment: this message is not needed and should not be used.
    // All content is covered by Message 18, Message 24A and 24B.
    // For legacy equipment: this message should be used by Class B shipborne mobile equipment.
    //------------------------------------------------------------------------------------------
    if(!ProcessCommon()) return false;
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_20(void)
{
    //---------------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 *******.1 Fixed access time division multiple access algorithm
    // FATDMA reservations do not apply beyond 120 nautical miles from the reserving base station.
    // All stations may consider these slots as available.
    // A data link management message (Message 20) without a base station report (Message 4) should be ignored.
    //---------------------------------------------------------------------------------------------------------

    if(!IsValidMMSI_BaseSt(m_xRxAisMsg.xMsg00.dSrcMMSI))          // IEC 62287-1, 6.8 (Page: 20)
    {
        WARNING_LOG("rcvMSG-20] ignore, not BS, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }


    if(!ProcessCommon()) return false;

    if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-20] ignore, too distant or pos invalid, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    if(!CUserDirMgr::getInst()->IsBaseStationPosValid(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-20] ignore, BS pos invalid, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    xAISMSG99 *pMsg = &m_xRxAisMsg;
    //---------------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 3.18 Data link management message
    // If interrogated and no data link manamement information is available, only Offset number 1, number of slots 1,
    // time-out 1, and increment 1 should be sent. These fields should all be set to zero.
    //---------------------------------------------------------------------------------------------------------
    if(!pMsg->xMsg20.dOffsetNo1 && !pMsg->xMsg20.dNoOfSlot1 && !pMsg->xMsg20.dTimeOut1 && !pMsg->xMsg20.dIncrement1)
        return true;

    ProcessRxMSG_20Sub(pMsg->xMsg20.dOffsetNo1, pMsg->xMsg20.dNoOfSlot1, pMsg->xMsg20.dTimeOut1, pMsg->xMsg20.dIncrement1);
    if(m_xRxAisMsg.xMsg00.wMsgBitsLen < 80)
        return true;

    ProcessRxMSG_20Sub(pMsg->xMsg20.dOffsetNo2, pMsg->xMsg20.dNoOfSlot2, pMsg->xMsg20.dTimeOut2, pMsg->xMsg20.dIncrement2);
    if(m_xRxAisMsg.xMsg00.wMsgBitsLen < 112)
        return true;

    ProcessRxMSG_20Sub(pMsg->xMsg20.dOffsetNo3, pMsg->xMsg20.dNoOfSlot3, pMsg->xMsg20.dTimeOut3, pMsg->xMsg20.dIncrement3);
    if(m_xRxAisMsg.xMsg00.wMsgBitsLen < 144)
        return true;

    ProcessRxMSG_20Sub(pMsg->xMsg20.dOffsetNo4, pMsg->xMsg20.dNoOfSlot4, pMsg->xMsg20.dTimeOut4, pMsg->xMsg20.dIncrement4);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_20Sub(DWORD dwOffset, DWORD dwNumSlots, DWORD dwTimeOut, DWORD dwIncrement)
{
    xAISMSG00 *pMsg00 = &m_xRxAisMsg.xMsg00;

    DEBUG_LOG("rcvMSG-20] %09d, rcvFrame : %d, rcvSlot : %d, off : %d, numSlot : %d, inc : %d, tmo : %d\r\n",
            pMsg00->dSrcMMSI, pMsg00->wMsgFrameNO, pMsg00->wMsgSlotNO, dwOffset, dwNumSlots, dwIncrement, dwTimeOut);

    if(!dwNumSlots)
        return true;


    if(dwTimeOut == 0 && dwIncrement != 0)
        dwTimeOut = 3;

    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    // 이미 Slot assigned mode 에서 수행 중인 FATDMA 예약 슬롯들은 이 함수 내에서 업데이트 되지 않는다!
    // Slot assigned mode 에서 수행 중인 FATDMA 예약 슬롯들은 Slot assigned mode 시작 시 UpdateAssignedSlotsFATDMAtoSOTDMA 함수 내에서 bProtectAssignedModeSlot 이 true 로 설정되어 있음
    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    m_pChannel->UpdateFrameMapFATDMA(true, pMsg00->dMsgID, pMsg00->wMsgFrameNO, pMsg00->wMsgSlotNO, pMsg00->dSrcMMSI, dwOffset, dwNumSlots, dwTimeOut, (int)dwIncrement);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_21(void)
{
    if(!ProcessCommon()) return false;
    m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_22()
{
    if(!ProcessCommon()) return false;

    if(!IsValidMMSI_BaseSt(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-22] ignore, not BS, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-22] ignore, too distant, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    if(m_xRxAisMsg.xMsg22.dBroadOrAddr == 0)     // broadcast
        ProcessRxMSG_22Broadcast();
    else
        ProcessRxMSG_22Addressed();
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_22Broadcast(void)
{
    xROSDATA xRosData;

    CROSMgr::getInst()->ClearOneRosData(&xRosData);

    xRosData.bRosSource = ROS_SRC_MSG_BROAD;
    xRosData.dSrcMMSI   = m_xRxAisMsg.xMsg00.dSrcMMSI;

    xRosData.xPointNE   = m_xRxAisMsg.xMsg22.xAllPos1;
    xRosData.xPointSW   = m_xRxAisMsg.xMsg22.xAllPos2;

    xRosData.wChannelNoA= m_xRxAisMsg.xMsg22.dChannelA;
    xRosData.wChannelNoB= m_xRxAisMsg.xMsg22.dChannelB;

    xRosData.bTxRxMode  = m_xRxAisMsg.xMsg22.dTxRxMode;
    xRosData.bTxPower   = m_xRxAisMsg.xMsg22.dPower;
    xRosData.bBandwidthA= m_xRxAisMsg.xMsg22.dBandwidthA;
    xRosData.bBandwidthB= m_xRxAisMsg.xMsg22.dBandwidthB;
    xRosData.bTrZoneSize= m_xRxAisMsg.xMsg22.dTrZoneSize + 1;

    xRosData.bValidMode = MODE_VAL_ON;
    xRosData.xRcvTime   = cShip::getOwnShipInst()->xSysTime;
    xRosData.dwRcvSysSec= cTimerSys::getInst()->GetCurTimerSec();

    DEBUG_LOG("rcvMSG-22] Broad, %09d, NE:%.2f,%.2f(%d,%d) SW:%.2f,%.2f(%d,%d), pos:%.2f,%.2f, ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d, %d-%d-%d,%02d:%02d:%02d, %d, %d\r\n",
            m_xRxAisMsg.xMsg00.dSrcMMSI,
            xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT, xRosData.xPointNE.xPosG.nLON, xRosData.xPointNE.xPosG.nLAT,
            xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT, xRosData.xPointSW.xPosG.nLON, xRosData.xPointSW.xPosG.nLAT,
            cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
            xRosData.wChannelNoA, xRosData.wChannelNoB,
            xRosData.bTxRxMode, xRosData.bTxPower, xRosData.bTrZoneSize,
            xRosData.xRcvTime.xDate.nYear, xRosData.xRcvTime.xDate.nMon, xRosData.xRcvTime.xDate.nDay,
            xRosData.xRcvTime.xTime.nHour, xRosData.xRcvTime.xTime.nMin, xRosData.xRcvTime.xTime.nSec,// xRosData.nEditStatByMKD,
            cTimerSys::getInst()->GetCurTimerSec());

    CROSMgr::getInst()->UpdateRosData(&xRosData);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_22Addressed(void)
{
    xROSDATA xRosData;
    xROADATA xRoaData;

    if(m_xRxAisMsg.xMsg22.dDestID1 == cShip::getOwnShipInst()->xStaticData.dMMSI || m_xRxAisMsg.xMsg22.dDestID2 == cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        CROSMgr::getInst()->ClearOneRosData(&xRosData, &xRoaData);

        xRosData.bRosSource = ROS_SRC_MSG_ADDRD;
        xRosData.dSrcMMSI   = m_xRxAisMsg.xMsg00.dSrcMMSI;

        xRosData.wChannelNoA= m_xRxAisMsg.xMsg22.dChannelA;
        xRosData.wChannelNoB= m_xRxAisMsg.xMsg22.dChannelB;

        xRosData.bTxRxMode    = m_xRxAisMsg.xMsg22.dTxRxMode;

        xRosData.bTxPower   = m_xRxAisMsg.xMsg22.dPower;
        xRosData.bBandwidthA= m_xRxAisMsg.xMsg22.dBandwidthA;
        xRosData.bBandwidthB= m_xRxAisMsg.xMsg22.dBandwidthB;

        xRosData.bTrZoneSize= m_xRxAisMsg.xMsg22.dTrZoneSize + 1;

        xRosData.bValidMode = MODE_VAL_ON;
        xRosData.xRcvTime    = cShip::getOwnShipInst()->xSysTime;

        DEBUG_LOG("rcvMSG-22] Addr, curROS:%d, %09d, NE:%.2f,%.2f SW:%.2f,%.2f pos:%.2f,%.2f, ChA:%d, ChB:%d, TRX:%d, pwr:%d, TRsize:%d, %d-%d-%d,%02d:%02d:%02d, %d\r\n",
            CROSMgr::getInst()->m_sRosData.nRosIdx,
            m_xRxAisMsg.xMsg00.dSrcMMSI,
            xRosData.xPointNE.xPosF.fLON, xRosData.xPointNE.xPosF.fLAT,
            xRosData.xPointSW.xPosF.fLON, xRosData.xPointSW.xPosF.fLAT,
            cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLON, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosF.fLAT,
            xRosData.wChannelNoA, xRosData.wChannelNoB,
            xRosData.bTxRxMode, xRosData.bTxPower, xRosData.bTrZoneSize,
            xRosData.xRcvTime.xDate.nYear, xRosData.xRcvTime.xDate.nMon, xRosData.xRcvTime.xDate.nDay,
            xRosData.xRcvTime.xTime.nHour, xRosData.xRcvTime.xTime.nMin, xRosData.xRcvTime.xTime.nSec,
            cTimerSys::getInst()->GetCurTimerSec());

        CROSMgr::getInst()->UpdateAddressedRosData(&xRosData, true);
    }
    else
    {
        WARNING_LOG("rcvMSG-22] Addr, ignore, destination is not own ship! dest1: %09d, dest2: %09d, %d\r\n",
            m_xRxAisMsg.xMsg22.dDestID1, m_xRxAisMsg.xMsg22.dDestID2, cTimerSys::getInst()->GetCurTimerSec());
    }

    return true;
}

int CVdlRxMgr::GetMsg23ReportIntervalSec(UINT8 uFieldData)
{
    CSetupMgr::getInst()->IsAisBClass();

    if (   (CSetupMgr::getInst()->IsAisAClass() && uFieldData > AIS_RI_MODE_02_SEC)
        || (CSetupMgr::getInst()->IsAisBClass() && uFieldData >= AIS_RI_MODE_02_SEC))
    {
        WARNING_LOG("rcvMSG-23] ignore, Invalid RptInt:%d\r\n", uFieldData);
        return 0;
    }

    if((uFieldData == AIS_RI_MODE_NEXT_SHORTER || uFieldData == AIS_RI_MODE_NEXT_LONGER) && CLayerNetwork::getInst()->m_nOpMode != OPMODE_CONTINUOUS)
    {
        WARNING_LOG("rcvMSG-23] ignore, Invalid RptInt:%d, opMode:%d\r\n", uFieldData, CLayerNetwork::getInst()->m_nOpMode);
        return 0;
    }

    switch(uFieldData)
    {
    case AIS_RI_MODE_AUTONOMOUS:    return CReportRateMgr::getInst()->m_fReportIntervalSec;
    case AIS_RI_MODE_10_MIN:        return 600;
    case AIS_RI_MODE_06_MIN:        return 360;
    case AIS_RI_MODE_03_MIN:        return 180;
    case AIS_RI_MODE_01_MIN:        return 60;
    case AIS_RI_MODE_30_SEC:        return 30;
    case AIS_RI_MODE_15_SEC:        return 15;
    case AIS_RI_MODE_10_SEC:        return 10;
    case AIS_RI_MODE_05_SEC:        return 5;
    case AIS_RI_MODE_02_SEC:        return 2;
    case AIS_RI_MODE_NEXT_SHORTER:  return CReportRateMgr::getInst()->GetReportIntSecNextShorter();
    case AIS_RI_MODE_NEXT_LONGER:   return CReportRateMgr::getInst()->GetReportIntSecNextLonger();
    }
    return 0;
}

bool CVdlRxMgr::ProcessRxMSG_23(void)
{
    //-----------------------------------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex2. 3.3.6, Class A 인 경우, 자동모드 보고주기가 메시지 16 또는 23 의 지정된 것보다
    // 더 짧은 보고간격을 요구한다면, 자동모드의 보고간격을 사용해야한다.
    // refer to IEC-61993-2 *******
    //-----------------------------------------------------------------------------------------------------------------------------
    ////return true; 인천지역에서 송신안되는 문제 분석 결과 msg-23 반복수신이 원인인것으로 추정됨.

    int nStnType, nShipType;
    int nTxRxMode, nFieldDataRI;
    int    nRISec;

    if(cTimerSys::getInst()->GetTimeDiffSec(CLayerNetwork::getInst()->m_dwRcvSecMsg23) < 2)
        return true;

    if(!IsValidMMSI_BaseSt(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-23] ignore, not BS, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    if(!ProcessCommon()) return false;

    if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(m_xRxAisMsg.xMsg00.dSrcMMSI))
    {
        WARNING_LOG("rcvMSG-23] ignore, too distant, %09d\r\n", m_xRxAisMsg.xMsg00.dSrcMMSI);
        return true;
    }

    if(CSetupMgr::getInst()->IsAisBClass() && CSetupMgr::getInst()->GetEnableSilentMode())
        return true;

    if(m_xRxAisMsg.xMsg23.dQuietTime > 0)
    {
        return true;
    }

    if(CROSMgr::getInst()->IsTrModeRunning())
    {
        //------------------------------------------------------------------------------------------------------
        // refer to IALA Rec.A-124 Appendex 17 - channel management by an AIS service December 2011, Page 9 of 29
        // Within the transitional zone, Class A shipborne mobile AIS stations will ignore any assignment of
        // higher nominal reporting intervals by shore stations. This guarantees that the broadcasts of mobile
        // AIS stations operating in the transition zone will be received at nominal reporting interval for the
        // benefit of other mobile stations in the immediate vicinity of that station.
        //------------------------------------------------------------------------------------------------------

        WARNING_LOG("rcvMSG-23] ignore, ROS mode:%d\r\n", CROSMgr::getInst()->m_sRosData.nRosMode);
        return true;
    }

    if(!IsValidAisLowPOS(&m_xRxAisMsg.xMsg23.xAllPos1.xPosL) || !IsValidAisLowPOS(&m_xRxAisMsg.xMsg23.xAllPos2.xPosL))
    {
        WARNING_LOG("rcvMSG-23] ignore, invalid POS, (%d, %d), (%d, %d)\r\n",
                m_xRxAisMsg.xMsg23.xAllPos1.xPosL.nLAT, m_xRxAisMsg.xMsg23.xAllPos1.xPosL.nLON,
                m_xRxAisMsg.xMsg23.xAllPos2.xPosL.nLAT, m_xRxAisMsg.xMsg23.xAllPos2.xPosL.nLON);
        return true;
    }

    bool bPosInRegion;
    if(!(bPosInRegion = CROSMgr::getInst()->CheckInsideRectangleXX(&cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG,
                                                            &m_xRxAisMsg.xMsg23.xAllPos1.xPosG, &m_xRxAisMsg.xMsg23.xAllPos2.xPosG)))
    {
        DEBUG_LOG("rcvMSG-23] not in the area, region : (%d, %d), (%d, %d), ownShip : %d, %d, s:%d\r\n",
            m_xRxAisMsg.xMsg23.xAllPos1.xPosG.nLAT, m_xRxAisMsg.xMsg23.xAllPos1.xPosG.nLON,
            m_xRxAisMsg.xMsg23.xAllPos2.xPosG.nLAT, m_xRxAisMsg.xMsg23.xAllPos2.xPosG.nLON,
            cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLON,
            cTimerSys::getInst()->GetCurTimerSec());
    }

    nStnType = m_xRxAisMsg.xMsg23.dStnType;
    nShipType = m_xRxAisMsg.xMsg23.dShipType;
    nTxRxMode = m_xRxAisMsg.xMsg23.dTxRxMode;
    nFieldDataRI = m_xRxAisMsg.xMsg23.dRptInterval;
    nRISec = GetMsg23ReportIntervalSec(nFieldDataRI);

    DEBUG_LOG("rcvMSG-23] param, ST:%d, ship:%d, TRX:%d, RIdata:%d, RIsec:%d\r\n",
            nStnType, nShipType, nTxRxMode, nFieldDataRI, nRISec);

    if(nStnType == AIS_ST_TYPE_BS_COVERAGE_AREA)
    {
        //---------------------------------------------------------------------------------------
        // refer to 1371-5 Annex4. 3.3.3 Automatic identification system shore station qualifier
        //---------------------------------------------------------------------------------------
        //---------------------------------------------------------------------------------------
        // refer to 1371-5 Annex8. 3.21 Group assignment command
        // Station type 10 should be used to define the base station coverage area for control of Message 27
        // transmissions by Class A and Class B "SO" mobile stations. When station type is 10 only the fields
        // latitude, longitude are used, all other fields should be ignored. This information will be relevant
        // until three minutes after the last reception of controlling Message 4 from the same base station (same MMSI).
        //---------------------------------------------------------------------------------------

        DEBUG_LOG("rcvMSG-23] [LR-VDL] BS CoverageArea, %09d, inRegion: %d, s:%d\r\n",
                m_xRxAisMsg.xMsg00.dSrcMMSI, bPosInRegion, cTimerSys::getInst()->GetCurTimerSec());

        CUserDirMgr::getInst()->SetLongRangeTxInRegion(m_xRxAisMsg.xMsg00.dSrcMMSI, bPosInRegion);
        return true;
    }

    if(!bPosInRegion)
    {
        WARNING_LOG("rcvMSG-23] ignore, not in the area, region : (%d, %d), (%d, %d), ownShip : %d, %d, s:%d\r\n",
            m_xRxAisMsg.xMsg23.xAllPos1.xPosG.nLAT, m_xRxAisMsg.xMsg23.xAllPos1.xPosG.nLON,
            m_xRxAisMsg.xMsg23.xAllPos2.xPosG.nLAT, m_xRxAisMsg.xMsg23.xAllPos2.xPosG.nLON,
            cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLAT, cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosG.nLON,
            cTimerSys::getInst()->GetCurTimerSec());
        return true;
    }

    if (CSetupMgr::getInst()->IsAisBClass())
    {
        if(CSetupMgr::getInst()->IsAisBClassCS())
        {
            if(nStnType != AIS_ST_TYPE_ALL_MOBILES && nStnType != AIS_ST_TYPE_CLASS_B_ALL && nStnType != AIS_ST_TYPE_CLASS_B_CSTDMA_ONLY)
                return true;
        }
        else if(CSetupMgr::getInst()->IsAisBClassSO())
        {
            if(nStnType != AIS_ST_TYPE_ALL_MOBILES && nStnType != AIS_ST_TYPE_CLASS_B_ALL && nStnType != AIS_ST_TYPE_CLASS_B_SOTDMA_ONLY)
                return true;
        }
    }
    else
    {
        if(nStnType != AIS_ST_TYPE_ALL_MOBILES && nStnType != AIS_ST_TYPE_CLASS_A_ONLY)
        {
            WARNING_LOG("rcvMSG-23] ignore, not ST type %d\r\n", nStnType);
            return true;
        }
    }

    if(!IsOwnShipTypeBelongsToAssigned(nShipType))
    {
        WARNING_LOG("rcvMSG-23] ignore, not ship type %d\r\n", nShipType);
        return true;
    }

    if(nTxRxMode > TRXMODE_MAX)
    {
        WARNING_LOG("rcvMSG-23] ignore, Invalid TRX mode:%d\r\n", nTxRxMode);
        return true;
    }

    if(cShip::getOwnShipInst()->xNavData.uShipType != AIS_ST_TYPE_BS_COVERAGE_AREA &&
        IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus) &&
        cShip::getOwnShipInst()->xDynamicData.nSOG <= SOG_THRESHOLD_DONTMOVE)            // not faster than 3kn
    {
        //----------------------------------
        // refer to IEC-61993-2 *******
        //----------------------------------
        WARNING_LOG("rcvMSG-23] ignore, shipType:%d, nav:%d, spd:%d\r\n",
            cShip::getOwnShipInst()->xNavData.uShipType, cShip::getOwnShipInst()->xNavData.uNavStatus, cShip::getOwnShipInst()->xDynamicData.nSOG);
        return true;
    }

    if(nRISec <= 0)
    {
        WARNING_LOG("rcvMSG-23] ignore, can't get RI, %d\r\n", nRISec);
        return true;
    }

    INT8 nTxRxModeToSet;
    INT8 nTRxModeMethodToSet;
    CH_SETUP chSetup;
    memcpy(&chSetup, &CROSMgr::getInst()->m_sChSetup, sizeof(CH_SETUP));
    chSetup.nTxRxMode = nTxRxMode;
    chSetup.nTxRxModeBy = TRXMODE_BY_MSG23;
    bool bTrxModeAccept = false;

    // Check if TRX mode setup is the same. It might be a command for assign mode reissue.
    if(CROSMgr::getInst()->IsSameNewTrxModeSetup(CROSMgr::getInst()->m_sRosData.nRosMode, &chSetup, &nTxRxModeToSet, &nTRxModeMethodToSet))
        bTrxModeAccept = true;
    else
        bTrxModeAccept = CROSMgr::getInst()->GetNewTrxModeByPriority(CROSMgr::getInst()->m_sRosData.nRosMode, &chSetup, &nTxRxModeToSet, &nTRxModeMethodToSet);

    if(!bTrxModeAccept)
    {
        WARNING_LOG("rcvMSG-23] ignore all, TRX mode not accepted : %d(%d), cur: %d(%d)\r\n",
            nTxRxModeToSet, nTRxModeMethodToSet, CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy);
        return true;
    }

    if (!CSetupMgr::getInst()->IsAisBClass())
    {
        if(CLayerNetwork::getInst()->m_nOpMode == OPMODE_CONTINUOUS && !CLayerNetwork::getInst()->IsHigherOrEqualAssignedRR(m_pChannel, nRISec))
        {
            WARNING_LOG("rcvMSG-23] ignore, RI is not shorter than or equal to AUTO mode, %d\r\n", nRISec);
            return true;
        }
    }

    CLayerNetwork::getInst()->m_dwRcvSecMsg23 = cTimerSys::getInst()->GetCurTimerSec();

    DEBUG_LOG("rcvMSG-23] StartAssignMode, %d\r\n", cTimerSys::getInst()->GetCurTimerSec());

    CLayerNetwork::getInst()->StartAssignedMode_ReportRate(m_xRxAisMsg.xMsg00.dSrcMMSI, ASSIGNED_MODE_BY_MSG23, nTxRxModeToSet, nTRxModeMethodToSet, (float)nRISec);

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_24(void)
{
    if(!ProcessCommon()) return false;
    m_pChannel->m_pVdlTxMgr->ProcessRespForMsg15(m_xRxAisMsg.xMsg00.dSrcMMSI, m_xRxAisMsg.xMsg00.dMsgID);
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_25(void)
{
    if(!ProcessCommon()) return false;

    //----------------------------------------------------------------------
    // refer to 1371-5 Annex8. 3.23 Message 25: Single slot binary message
    // This message should not be acknowledged by either Message 7 or 13.
    // 메시지 25 수신시 ACK 하면 안된다!
    //----------------------------------------------------------------------

    if(m_xRxAisMsg.xMsg25.dDestInd == 1 && m_xRxAisMsg.xMsg25.dDestID != cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        WARNING_LOG("rcvMSG-25] ignore, dest is not own : %09d\r\n", m_xRxAisMsg.xMsg25.dDestID);
        return false;            // Do not send VDM
    }
    return true;
}

bool CVdlRxMgr::ProcessRxMSG_26(void)
{
    //--------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex8. 3.24 Message 26: Multiple slot binary message with communications state
    // This message should not be acknowledged by either Message 7 or 13.
    // 메시지 26 수신시 ACK 하면 안된다!
    //--------------------------------------------------------------------------------------------------

    if(!ProcessCommon()) return false;

    if(CSetupMgr::getInst()->IsAisBClassCS())
        return false;   // Refer to the table "Use of VDL Messages by a Class B "CS" AIS" in IEC-62287-1

    if(m_xRxAisMsg.xMsg26.dDestInd == 1 && m_xRxAisMsg.xMsg26.dDestID != cShip::getOwnShipInst()->xStaticData.dMMSI)
    {
        WARNING_LOG("rcvMSG-26] ignore, dest is not own : %09d\r\n", m_xRxAisMsg.xMsg25.dDestID);
        return false;            // Do not send VDM
    }

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_27(void)
{
    if(!ProcessCommon()) return false;

    return true;
}

bool CVdlRxMgr::ProcessRxMSG_Reserved(void)
{
    if(!ProcessCommon()) return false;

    return true;
}

bool CVdlRxMgr::ProcessRxOneMSG()
{
    int nRxChNo = m_pChannel->m_uChNumRx;

    switch (m_bRxAisMsgNoID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
    case AIS_MSG_NO_ID_03:
                            GetMsg01FieldData(); return ProcessRxMSG_01_02_03();
     case AIS_MSG_NO_ID_04:
     case AIS_MSG_NO_ID_11:
                            GetMsg04FieldData(); return ProcessRxMSG_04_11();

    case AIS_MSG_NO_ID_05:  GetMsg05FieldData(); return ProcessRxMSG_05();
    case AIS_MSG_NO_ID_06:  GetMsg06FieldData(); return ProcessRxMSG_06();
    case AIS_MSG_NO_ID_07:
    case AIS_MSG_NO_ID_13:
                            GetMsg07FieldData(); return ProcessRxMSG_07_13();
    case AIS_MSG_NO_ID_08:  GetMsg08FieldData(); return ProcessRxMSG_08();
    case AIS_MSG_NO_ID_09:  GetMsg09FieldData(); return ProcessRxMSG_09();
    case AIS_MSG_NO_ID_10:  GetMsg10FieldData(); return ProcessRxMSG_10();
    case AIS_MSG_NO_ID_12:  GetMsg12FieldData(); return ProcessRxMSG_12();
    case AIS_MSG_NO_ID_14:  GetMsg14FieldData(); return ProcessRxMSG_14();
    case AIS_MSG_NO_ID_15:  GetMsg15FieldData(); return ProcessRxMSG_15();
#ifdef __ENABLE_ASSIGNED_MODE__
    case AIS_MSG_NO_ID_16:  GetMsg16FieldData(); return ProcessRxMSG_16();
#endif
    case AIS_MSG_NO_ID_17:  GetMsg17FieldData(); return ProcessRxMSG_17();
    case AIS_MSG_NO_ID_18:  GetMsg18FieldData(); return ProcessRxMSG_18();
    case AIS_MSG_NO_ID_19:  GetMsg19FieldData(); return ProcessRxMSG_19();
    case AIS_MSG_NO_ID_20:  GetMsg20FieldData(); return ProcessRxMSG_20();
    case AIS_MSG_NO_ID_21:  GetMsg21FieldData(); return ProcessRxMSG_21();
    case AIS_MSG_NO_ID_22:  GetMsg22FieldData(); return ProcessRxMSG_22();
#ifdef __ENABLE_ASSIGNED_MODE__
    case AIS_MSG_NO_ID_23:  GetMsg23FieldData(); return ProcessRxMSG_23();
#endif
    case AIS_MSG_NO_ID_24:  GetMsg24FieldData(); return ProcessRxMSG_24();
    case AIS_MSG_NO_ID_25:  GetMsg25FieldData(); return ProcessRxMSG_25();
    case AIS_MSG_NO_ID_26:  GetMsg26FieldData(); return ProcessRxMSG_26();
#ifdef __ENABLE_MSG27_RX_MODE__
    case AIS_MSG_NO_ID_27:  GetMsg27FieldData(); return ProcessRxMSG_27();
#else
    case AIS_MSG_NO_ID_27:  return false;
#endif

    default:
        if(AIS_RXMSGID_MIN <= m_bRxAisMsgNoID && m_bRxAisMsgNoID <= AIS_RXMSGID_MAX)    // IEC-61993-2 7.3.4 Table 12, MSG 0,28~63 : R/P yes
        {
            GetMsgReservedFieldData();
            return ProcessRxMSG_Reserved();
        }
    }
    return false;
}

void CVdlRxMgr::GetRxAisMsgNoID(void)
{
    m_bRxAisMsgNoID = (m_pRxPacketData[0] >> 2) & 0x3f;
    m_bRxAisMsgRPTI = (m_pRxPacketData[0] >> 0) & 0x03;

    if(m_bRxAisMsgNoID < AIS_RXMSGID_MIN || m_bRxAisMsgNoID > AIS_RXMSGID_MAX)
    {
        m_bRxAisMsgNoID             = AIS_MSG_NO_ID_UNDEFINED;
        m_xRxAisMsg.xMsg00.dMsgID = AIS_MSG_NO_ID_UNDEFINED;
    }
}

bool CVdlRxMgr::RunProcessRxMgr()
{
    if(!m_pChannel->IsRxAvailableCh())
        return false;

    m_pRxRawForm = m_pChannel->m_pRxModem->GetFullPacketFromRxRawBuff();
    if(m_pRxRawForm == NULL)
        return false;

    m_wRxPacketBitC = m_pRxRawForm->wRxBitsSize;
    m_wRxPacketSize = m_pRxRawForm->wRxByteSize;
    m_pRxPacketData = m_pRxRawForm->vRxRawData;

    if(m_wRxPacketBitC >= 72)
    {
        GetRxAisMsgNoID();

        bool bValidRcvMsg = ProcessRxOneMSG();

        // IEC-61993-2 6.11.1
        bool bSendVDM = bValidRcvMsg && 
                        (CTestModeMgr::getInst()->IsTestModeRunning() 
                        || !CSartMgr::getInst()->IsTestingSART(m_xRxAisMsg.xMsg00.dSrcMMSI) 
                        || CSetupMgr::getInst()->GetShowTestingSART());

        if(bSendVDM)
        {
            CMKD::getInst()->SendRxMsgToAllPI(m_xRxAisMsg.xMsg00.dMsgID, m_pChannel->GetChOrdinal(), m_pRxPacketData, m_wRxPacketSize);
        }
    }
    return true;
}
