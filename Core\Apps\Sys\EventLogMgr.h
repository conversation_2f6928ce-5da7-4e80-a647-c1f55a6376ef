#ifndef __EVENTLOGMGR_H__
#define __EVENTLOGMGR_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"

class CEventLogMgr
{
public:
    CEventLogMgr();
    ~CEventLogMgr();

    static std::shared_ptr<CEventLogMgr> getInst() {
        static std::shared_ptr<CEventLogMgr> pInst = std::make_shared<CEventLogMgr>();
        return pInst;
    }

public:
    void    ResetEventLogData(EVENTLOG_DATA *pLogData);
    EVENTLOG_DATA *AddEventLogData(int nEventLogID, EVENTLOG_DATA *pLogData, int nIndexToSave=-1);
    BOOL    CheckAvailableLogData(EVENTLOG_DATA *pLogData);

    int     SerializeEventLog(UCHAR *pBackData);
    bool    LoadEventLogData(UCHAR *pBackData);
    void    VerifyEventLogData(void);
    void    ClearEventLogData(void);
    int     FindEventLog(int nEventLogID, SYS_DATE_TIME *pEventStartTime);

    BOOL    SavePowerOffTime();
    BOOL    CheckEventLogValidMMSI();
    BOOL    CheckEventLogRxOnlyMode();
    BOOL    CheckEventLogTxMulFunc();
    BOOL    CheckPowerOffTime(void);
    void    RunPeriodicallyEventLog(void);

public:
    enum _tagEventLogConsts
    {
        EVENTLOG_CHECKTIMESEC = 900,
    };

    EVENTLOG_DATA  *m_pLogData;

    SYS_DATE_TIME   m_sPowerOffTime;
    BOOL            m_bPowerOffTimeUTC;

    SYS_DATE_TIME   m_sMMSINullTime;
    BOOL            m_bMMSINullTimeUTC;

    SYS_DATE_TIME   m_sRxOnlyStartTime;
    BOOL            m_bRxOnlyStartTimeUTC;

    SYS_DATE_TIME   m_sTxMulFuncStartTime;
    BOOL            m_bTxMulFuncStartTimeUTC;

    static BOOL     m_bCheckEventLogDone;
};

#endif//__EVENTLOGMGR_H__
