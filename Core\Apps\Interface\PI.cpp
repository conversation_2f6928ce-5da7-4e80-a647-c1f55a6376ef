#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "main.h"
#include "SysConst.h"
#include "Define_AIS.h"
#include "SysLib.h"
#include "Uart.h"
#include "RosMgr.h"
#include "Nmea.h"
#include "AisMsg.h"
#include "SetupMgr.h"
#include "VdlTxMgr.h"
#include "UserDirMgr.h"
#include "LayerNetwork.h"
#include "ChannelMgr.h"
#include "ReportRateMgr.h"
#include "LayerTransport.h"
#include "AlarmMgr.h"
#include "ExtDisp.h"
#include "Pilot.h"
#include "LongRange.h"
#include "Timer.h"
#include "MKD.h"
#include "SensorMgr.h"
#include "GpsBoard.h"
#include "GnssInternal.h"
#include "TestModeMgr.h"
#include "EventLogMgr.h"
#include "md5.h"
#include "Ship.h"

#include "Vdm.h"
#include "Vdo.h"
#include "Ssd.h"
#include "Vsd.h"
#include "Epv.h"
#include "Abk.h"
#include "Aca.h"
#include "Acs.h"
#include "Alc.h"
#include "Acn.h"
#include "Alr.h"
#include "Alf.h"
#include "Txt.h"
#include "Trl.h"
#include "Air.h"
#include "Ack.h"
#include "Nak.h"
#include "Ver.h"
#include "PI.h"

//-----------------------------------------------------------------------------
// Global variables
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// MACROs
//-----------------------------------------------------------------------------
#define CheckABMBBMLastSentence(nNumStTotal, nCurStNum)        (nNumStTotal == nCurStNum)

//-----------------------------------------------------------------------------
// Static variables
//-----------------------------------------------------------------------------
char*   CPI::m_pstrOutBuff       = NULL;

//-----------------------------------------------------------------------------
// Implementation
//-----------------------------------------------------------------------------
CPI::CPI(UINT8 uHighSpdPortID, char chPortID, cUart *pUartPort)
{
    if(!m_pstrOutBuff)
        m_pstrOutBuff = (char*)SysAllocMemory(sizeof(char) * 1024);

    m_nHighSpdPortID= uHighSpdPortID;
    m_chPortID      = chPortID;
    m_pUartPort     = pUartPort;
    m_nBaudIdx      = BAUD_IDX_38400;

    m_nRxSize       = 0;
    m_pRxData       = (UCHAR*)SysAllocMemory(sizeof(UCHAR) * RX_MAX_DATA_SIZE);

    m_bEnableSendRcvDataToMKD= FALSE;

    m_pUartPort->ClearRxBuff();
}

CPI::~CPI()
{
}

/**
 * @brief Set the UART baud rate index
 * @param nBaudIdx The baud rate index
 * @return None
 */
void CPI::SetUartBaudIdx(int nBaudIdx)
{
    //---------------------------------------------
    // nBaudIdx : BAUD_IDX_38400 or BAUD_IDX_4800
    //---------------------------------------------
    if(nBaudIdx != BAUD_IDX_4800 && nBaudIdx != BAUD_IDX_38400)// && nBaudIdx != BAUD_IDX_AUTO)
        nBaudIdx = BAUD_IDX_38400;

    int nBaudrate = (nBaudIdx == BAUD_IDX_4800) ? 4800 : 38400;

    if(nBaudIdx != m_nBaudIdx)
    {
        m_nBaudIdx = nBaudIdx;
        m_pUartPort->SetUartPara(nBaudrate);

        DEBUG_LOG("PI-#%d] Set-Baud ok, idx: %d -> %d, %d -> %d\r\n",
            m_nHighSpdPortID, m_nBaudIdx, nBaudIdx, m_pUartPort->GetSpeed(), nBaudrate);
    }
}

/**
 * @brief Send a string to the high speed port
 * @param pstrMsg The string to be sent
 * @return None
 */
void CPI::SendOutStr(const char *pstrMsg)
{
    m_pUartPort->WriteComStr(pstrMsg);
}

/**
 * @brief Send data to the high speed port
 * @param pstrMsg The data to be sent
 * @param uSize The size of the data
 * @return None
 */
void CPI::SendOutData(const char *pstrMsg, UINT16 uSize)
{
    m_pUartPort->WriteComData((const UCHAR*)pstrMsg, uSize);
}

/**
 * @brief Send data to the high speed port
 * @param pData The data to be sent
 * @param nSize The size of the data
 * @return None
 */
void CPI::SendHighSpdPortData(BYTE *pData, int nSize)
{
    m_pUartPort->WriteComData((BYTE*)pData, nSize);
}

/**
 * @brief Send data to all high speed ports
 * @param pData The data to be sent
 * @param nSize The size of the data
 * @param bSendMKD Whether to send to MKD
 * @return None
 */
void CPI::SendAllHighSpdPortData(BYTE *pData, int nSize, BOOL bSendMKD)
{
#ifdef __ENABLE_PILOT__
    CPILOT::getInst()->SendHighSpdPortData(pData, nSize);
#endif
#ifdef __ENABLE_EXT_DISP__
    CEXTDISP::getInst()->SendHighSpdPortData(pData, nSize);
#endif
    if(bSendMKD)
    {
        CMKD::getInst()->SendHighSpdPortData(pData, nSize);
    }
}

/**
 * @brief Enable or disable sending/receiving data to/from MKD
 * @param bEnable Whether to enable or disable
 * @return None
 */
void CPI::EnableSendRcvDataToMKD(BOOL bEnable)
{
    DEBUG_LOG("PI-#%d] EnableMon-HS%d] %d -> %d\r\n", m_nHighSpdPortID, m_nHighSpdPortID, m_bEnableSendRcvDataToMKD, bEnable);
    m_bEnableSendRcvDataToMKD = bEnable;
}

/**
 * @brief Send the UART port monitoring setup to MKD
 * @param None
 * @return None
 */
void CPI::SendSetupUartPortMon()
{
    char pstrSycBuff[32];
    sprintf((char*)pstrSycBuff, "$AIINL,MON,%c,%d", m_chPortID, m_bEnableSendRcvDataToMKD);
    CSentence::AddSentenceTail(pstrSycBuff);
    CMKD::getInst()->SendOutStr(pstrSycBuff);
}

/**
 * @brief Check the TX channel for PI
 * @param uDestMMSI The destination MMSI
 * @param tx_ch The TX channel
 * @param pbEnableAis1 Whether to enable AIS1
 * @param pbEnableAis2 Whether to enable AIS2
 * @return None
 */
void CPI::CheckTxChPI(UINT uDestMMSI, BYTE tx_ch, BOOL *pbEnableAis1, BOOL *pbEnableAis2)
{
    *pbEnableAis1 = *pbEnableAis2 = FALSE;

    UINT8 uLastRcvChID = AIS_CHANNEL_NONE;
    switch(tx_ch)
    {
    case ABM_CH_NO_CH:    // no broadcast channel preference => Auto
        {
            if(uDestMMSI == AIS_AB_MMSI_NULL)
            {
                uLastRcvChID = CLayerNetwork::m_pLastTxCH ? CLayerNetwork::m_pLastTxCH->GetChOrdinal() : AIS_CHANNEL_NONE;
                if(uLastRcvChID == AIS_CHANNEL_AIS1 && CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh())
                    *pbEnableAis2 = TRUE;
                else if(uLastRcvChID == AIS_CHANNEL_AIS2 && CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
                    *pbEnableAis1 = TRUE;
            }
            else
            {
                uLastRcvChID = CUserDirMgr::getInst()->GetLastRcvCh(uDestMMSI);
                if(uLastRcvChID == AIS_CHANNEL_AIS1 && CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
                    *pbEnableAis1 = TRUE;
                else if(uLastRcvChID == AIS_CHANNEL_AIS2 && CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh())
                    *pbEnableAis2 = TRUE;
            }

            if(uLastRcvChID == AIS_CHANNEL_NONE || (!*pbEnableAis1 && !*pbEnableAis2))
            {
                if(CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
                    *pbEnableAis1 = TRUE;
                else if(CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh())
                    *pbEnableAis2 = TRUE;
            }
        }
        break;

    case ABM_CH_BOTH:    // copy AIS1 and AIS2
        if(CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
            *pbEnableAis1 = TRUE;
        if(CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh())
            *pbEnableAis2 = TRUE;
        break;

    case ABM_CH_A:    // AIS1
        if(CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
            *pbEnableAis1 = TRUE;
        break;

    case ABM_CH_B:    // AIS2
        if(CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
            *pbEnableAis2 = TRUE;
        break;
    }

    DEBUG_LOG("CheckTxChPI] dest: %09d, CH: %d, lastRcvCH: %d, tx1: %d, tx2: %d\r\n",
            uDestMMSI, tx_ch, uLastRcvChID, *pbEnableAis1, *pbEnableAis2);
}

/**
 * @brief Check if the interrogation message is valid
 * @param nInterrogatedStType The type of the interrogated station
 * @param nInterrogatedMsgID The ID of the interrogated message
 * @return TRUE if valid, FALSE otherwise
 */
BOOL CPI::CheckValidInterrMsg(int nInterrogatedStType, int nInterrogatedMsgID)
{
    //------------------------------------------------------------------------------------------------------------------------------
    // refer to ITU-R-M 1371-5 Annex 8 3.13 Table 65 Class A interrogator 일때 다음 메시지에 대한 interrogation 메시지 15 송신
    // 다음 메시지에 대한 interrogation 메시지 15 송신
    // - Class A      :  3,  5, 24
    //   Class B-SO   : 18, 24, 19(Ivan recommendation)
    //   Class B-CS   : 18, 24, 19(Ivan recommendation)
    //   SAR-aircraft :  9, 24
    //   AtoN          : 21
    //   Base Station :  4, 24
    //------------------------------------------------------------------------------------------------------------------------------

    switch(nInterrogatedStType)
    {
    case AIS_ST_TYPE_SAR_AIRBORNE:
        {
            switch (nInterrogatedMsgID)
            {
            case AIS_MSG_NO_ID_09:
            case AIS_MSG_NO_ID_24:
                return TRUE;
            }
        }
        break;

    case AIS_ST_TYPE_ATON:
        {
            switch (nInterrogatedMsgID)
            {
            case AIS_MSG_NO_ID_21:
                return TRUE;
            }
        }
        break;

    case AIS_ST_TYPE_CLASS_A_ONLY:
        {
            switch (nInterrogatedMsgID)
            {
            case AIS_MSG_NO_ID_03:
            case AIS_MSG_NO_ID_05:
            case AIS_MSG_NO_ID_24:
                return TRUE;
            }
        }
        break;

    case AIS_ST_TYPE_CLASS_B_ALL:
    case AIS_ST_TYPE_CLASS_B_SOTDMA_ONLY:
    case AIS_ST_TYPE_CLASS_B_CSTDMA_ONLY:
        {
            switch (nInterrogatedMsgID)
            {
            case AIS_MSG_NO_ID_18:
            case AIS_MSG_NO_ID_24:
                return TRUE;
            }
        }
        break;

    case AIS_ST_TYPE_ALL_MOBILES:
        {
            switch (nInterrogatedMsgID)
            {
            case AIS_MSG_NO_ID_03:
            case AIS_MSG_NO_ID_05:
            case AIS_MSG_NO_ID_18:
            case AIS_MSG_NO_ID_21:
            case AIS_MSG_NO_ID_24:
                return TRUE;
            }
        }
        break;

    case AIS_ST_TYPE_BASE_STATION:
        {
            switch (nInterrogatedMsgID)
            {
            case AIS_MSG_NO_ID_04:
            case AIS_MSG_NO_ID_24:
                return TRUE;
            }
        }
        break;
    }
    return FALSE;
}

/**
 * @brief Check the interrogation request information
 * @param bAirType The interrogation type
 * @param uMMSI1 The MMSI of the first interrogation target
 * @param bMsgID11 The message ID of the first interrogation target
 * @param bMsgID12 The message ID of the second interrogation target
 * @param uMMSI2 The MMSI of the second interrogation target
 * @param bMsgID21 The message ID of the third interrogation target
 * @return TRUE if valid, FALSE otherwise
 */
BOOL CPI::CheckInterrRequestInfo(BYTE bAirType, UINT uMMSI1, int bMsgID11, int bMsgID12, UINT uMMSI2, int bMsgID21)
{
    BOOL bReqOkMsgID11 = FALSE;
    BOOL bReqOkMsgID12 = FALSE;
    BOOL bReqOkMsgID21 = FALSE;

    int nInterrogatedStType = CUserDirMgr::getInst()->GetInterrogatedStType(uMMSI1);
    if(CheckValidInterrMsg(nInterrogatedStType, bMsgID11))
        bReqOkMsgID11 = TRUE;
    else
    {
        INFO_LOG("AIR-check-1] Invalid interrogation msg, MMSI : %09d, stType : %d, msg %d\r\n", uMMSI1, nInterrogatedStType, bMsgID11);
    }

    if(bAirType == AIR_ID1_MSG1)
    {
        bReqOkMsgID12 = TRUE;
        bReqOkMsgID21 = TRUE;
    }
    else
    {
        if(bMsgID12 == AIS_MSG_NO_ID_UNDEFINED || CheckValidInterrMsg(nInterrogatedStType, bMsgID12))
            bReqOkMsgID12 = TRUE;
        else
        {
            INFO_LOG("AIR-check-2] Invalid interrogation msg, MMSI : %09d, st : %d, msg %d\r\n", uMMSI1, nInterrogatedStType, bMsgID12);
        }

        if(bAirType == AIR_ID1_MSG12)
            bReqOkMsgID21 = TRUE;
        else
        {
            // AIR_ID12_MSG13 or AIR_ID12_MSG123
            nInterrogatedStType = CUserDirMgr::getInst()->GetInterrogatedStType(uMMSI2);
            if((uMMSI2 == AIS_AB_MMSI_NULL && bMsgID21 == AIS_MSG_NO_ID_UNDEFINED) || CheckValidInterrMsg(nInterrogatedStType, bMsgID21))
                bReqOkMsgID21 = TRUE;
            else
            {
                INFO_LOG("AIR-check-3] Invalid interrogation msg, MMSI : %09d, stType : %d, msg %d\r\n", uMMSI2, nInterrogatedStType, bMsgID21);
            }
        }
    }

    BOOL bRet = FALSE;

    // ITU-R.1371-5 Table 65 note-1
    if(bMsgID11 == AIS_MSG_NO_ID_24 && bAirType == AIR_ID1_MSG1)
        bRet =  bReqOkMsgID11;
    else if((bMsgID11 == AIS_MSG_NO_ID_24 || bMsgID12 == AIS_MSG_NO_ID_24) && (bAirType == AIR_ID1_MSG1 || bAirType == AIR_ID1_MSG12))
        bRet =  bReqOkMsgID11 || bReqOkMsgID12;
    else if((bMsgID11 == AIS_MSG_NO_ID_24 || bMsgID21 == AIS_MSG_NO_ID_24) && (bAirType == AIR_ID1_MSG1 || bAirType == AIR_ID12_MSG13))
        bRet = bReqOkMsgID11 || bReqOkMsgID21;
    else if((bMsgID11 == AIS_MSG_NO_ID_24 || bMsgID12 == AIS_MSG_NO_ID_24 || bMsgID21 == AIS_MSG_NO_ID_24) && (bAirType == AIR_ID1_MSG1 || bAirType == AIR_ID12_MSG13 || bAirType == AIR_ID12_MSG123))
        bRet = bReqOkMsgID11 || bReqOkMsgID12 || bReqOkMsgID21;
    else
    {
        if(bAirType == AIR_ID1_MSG1)
            bRet = bReqOkMsgID11;
        if(bAirType == AIR_ID1_MSG12)
            bRet = bReqOkMsgID11 && bReqOkMsgID12;
        if(bAirType == AIR_ID12_MSG13)
            bRet = bReqOkMsgID11 && bReqOkMsgID21;
        if(bAirType == AIR_ID12_MSG123)
            bRet = bReqOkMsgID11 && bReqOkMsgID12 && bReqOkMsgID21;
    }

    if(!bRet)
    {
        INFO_LOG("AIR-check] NG, type : %d, MMSI_1 : %09d, MSG_1_1 : %d, MSG_1_2 : %d, MMSI_2 : %d, MSG_2_1 : %d, ok11: %d, ok12: %d, ok21: %d\r\n",
                bAirType, uMMSI1, bMsgID11, bMsgID12, uMMSI2, bMsgID21, bReqOkMsgID11, bReqOkMsgID12, bReqOkMsgID21);
    }

    return bRet;
}

/*************************************************************************************/
/**
 * @brief Send VDM sentence to PI
 * @param dwMsgID The message ID
 * @param nRxChNo The RX channel number
 * @param pPacketData The packet data
 * @param nPacketSize The packet size
 * @return None
 */
void CPI::SendRxMsgToAllPI(DWORD dwMsgID, int nRxChNo, UCHAR *pPacketData, int nPacketSize)
{
    memset(m_pstrOutBuff, 0x00, sizeof(char)*1024);
    int nFullLen = CVdm::MakeSentence(m_pstrOutBuff, nRxChNo, pPacketData, nPacketSize);

    if(nFullLen > 0)
        SendAllHighSpdPortData((BYTE*)m_pstrOutBuff, nFullLen, TRUE);
}

/**
 * @brief Send VDO sentence to PI
 * @param nTxChNo The TX channel number
 * @param pPacketData The packet data
 * @param nPacketSize The packet size
 * @return None
 */
void CPI::SendTxMsgToAllPI(int nTxChNo, UCHAR *pPacketData, int nPacketSize)
{
    memset(m_pstrOutBuff, 0x00, sizeof(char)*1024);
    int nFullLen = CVdo::MakeSentence(m_pstrOutBuff, nTxChNo, pPacketData, nPacketSize);

    if(nFullLen > 0)
        SendAllHighSpdPortData((BYTE*)m_pstrOutBuff, nFullLen, TRUE);
}

/**
 * @brief Send dummy VDO sentence to PI
 * @param bCheckNullMMSI Whether to check for null MMSI
 * @return None
 */
void CPI::SendDummyVDO(void)
{
    BYTE pTxSlotBuff[NUM_MAX_BITS_TXBLOCK];

    CChannelMgr *pChannel = CLayerNetwork::getInst()->GetChPrimary();
    int nMsgSizeBits = 0;

    // [IEC 61993-2, Ed.3, clause 7.6.3.4]
    // The VDO sentence (containing Messages 1, 2 or 3) shall be output on both high-speed output ports, 
    // at nominal 1 s intervals, use A and B to indicate that the data was transmitted on the VDL channel A or B, 
    // null indicating not transmitted on the VDL.
    if (CSetupMgr::getInst()->IsAisAClass())
        nMsgSizeBits = pChannel->m_pVdlTxMgr->MakeAisTxMsg_01_02_03(pChannel, SLOTID_NONE, AIS_MSG_NO_ID_01, pTxSlotBuff);
    else
        nMsgSizeBits = pChannel->m_pVdlTxMgr->MakeAisTxSlotMsg18(pChannel, SLOTID_NONE, pTxSlotBuff);

    if (nMsgSizeBits > 0)
    {
        memset(m_pstrOutBuff, 0x00, sizeof(char)*1024);
        int nFullLen = CVdo::MakeSentence(m_pstrOutBuff, AIS_CHANNEL_NONE, pTxSlotBuff, nMsgSizeBits / 8);

        if(nFullLen > 0)
            SendOutData(m_pstrOutBuff, nFullLen);
    }
}

/**
 * @brief Send SSD sentence to PI
 * @param pstrTalker The talker ID to be sent
 * @return None
 */
void CPI::SendSSDToPI(char *pstrTalker)
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    memset((char*)pstrBuff, 0x00, RX_MAX_DATA_SIZE);

    // Send static data with internal antenna position
    if (CSsd::MakeSentence(pstrBuff, pstrTalker, EPFD_ANTENNA_TYPE_INT) > 0)
        SendOutStr(pstrBuff);

    memset((char*)pstrBuff, 0x00, RX_MAX_DATA_SIZE);

    // Send static data with external antenna position
    if (CSsd::MakeSentence(pstrBuff, pstrTalker, EPFD_ANTENNA_TYPE_EXT) > 0)
        SendOutStr(pstrBuff);
}

void CPI::SendVSDToPI(char *pstrTalker)
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    memset((char*)pstrBuff, 0x00, RX_MAX_DATA_SIZE);

    // Send voyage data
    if (CVsd::MakeSentence(pstrBuff, pstrTalker) > 0)
        SendOutStr(pstrBuff);
}

/**
 * @brief Send system date and time to PI
 * @param None
 * @return None    
 */
void CPI::SendSysDateTime()
{
    //----------------------------------------------------------------
    // PI(MKD)로 부터 시간정보 요청 처리(약 30초마다 MKD로부터 수신.)
    //----------------------------------------------------------------

    char pstrBuff[RX_MAX_DATA_SIZE];

    sprintf(pstrBuff, "$AIINL,002,,%d,%04d%02d%02d,%02d%02d%02d,%1d",
            cShip::getOwnShipInst()->xSysTime.nValid ? cShip::getOwnShipInst()->bSysTimeUTC : 2,    // 0: local time, 1: UTC, 2: invalid
            cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay, 
            cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec, 
            CSensorMgr::getInst()->GetPosSensorIdx());

    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send own ship MMSI to PI
 * @param None
 * @return None    
 */
void CPI::SendOwnShipMMSI()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,106,%09d", cShip::getOwnShipInst()->xStaticData.dMMSI);
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send own ship IMO to PI
 * @param None
 * @return None    
 */
void CPI::SendOwnShipIMO()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,107,%010d", cShip::getOwnShipInst()->xStaticData.dwImoID);
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send VER sentence to PI
 * @param None
 * @return None
 */
void CPI::SendVERToPI()
{
    char pstrTxt[RX_MAX_DATA_SIZE];
    memset(pstrTxt, 0x00, RX_MAX_DATA_SIZE);

    int nLen = CVer::MakeSentence(pstrTxt);
    if (nLen > 0)
        SendOutData(pstrTxt, nLen);
}

/**
 * @brief Send ALR sentence to all PI
 * @param pAlarmThing The alarm thing to be sent
 * @return None
 */
void CPI::SendALRToAllPI(CAlarmThing *pAlarmThing)
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    memset((char*)pstrBuff, 0x00, RX_MAX_DATA_SIZE);

    int nFullLen = CAlr::MakeSentence(pstrBuff, pAlarmThing);
    if (nFullLen > 0)
    {
        SendAllHighSpdPortData((BYTE*)pstrBuff, nFullLen, TRUE);
    }
}

/**
 * @brief Send ALF sentence to PI
 * @param pAlarmThing The alarm thing to be sent
 * @param bSendAllPI Whether to send to all PI
 * @return None
 */
void CPI::SendALFToPI(CAlarmThing *pAlarmThing, bool bSendAllPI)
{
    char pstrOutMsg[RX_MAX_DATA_SIZE*2];

    memset(pstrOutMsg, 0x00, sizeof(pstrOutMsg));

    int nFullLen = CAlf::MakeSentence(pstrOutMsg, pAlarmThing);

    if (nFullLen > 0)
    {
        if (bSendAllPI)
            SendAllHighSpdPortData((BYTE*)pstrOutMsg, nFullLen, TRUE);
        else
            SendOutData(pstrOutMsg, nFullLen);
    }
}

/**
 * @brief Send security log data to PI
 * @param pLogData The log data to be sent
 * @param pUtcTime The UTC time
 * @param nAlrIndex The alarm index
 * @param nAlrStat The alarm status
 * @return None
 */
void CPI::SendSecurityLogData(EVENTLOG_DATA *pLogData, SYS_DATE_TIME *pUtcTime, int nAlrIndex, _tagBAMAlertStat nAlrStat)    // nAlrStat : threshold exceeded or not exceeded
{
    char pstrAlarmTxt[RX_MAX_DATA_SIZE];
    char pstrOutMsg[RX_MAX_DATA_SIZE];
    char pstrTemp[RX_MAX_DATA_SIZE];
    char pstrMonth[32];

    if(nAlrIndex < ALRID_SECURITYLOG_MIN || nAlrIndex > ALRID_SECURITYLOG_MAX)
    {
        return;
    }

    pstrAlarmTxt[0] = '\0';
    pstrMonth[0] = '\0';

    if(nAlrIndex == ALRID_SECURITYLOG_MAX)
    {
        ;
    }
    else if(pLogData)
    {
        if(CAisLib::IsValidAisSysDateTime(&(pLogData->sEventTime)) && pLogData->bEventTimeUTC)
        {
            CAisLib::GetMonthNumToStr(pLogData->sEventTime.xDate.nMon, pstrMonth);
            sprintf(pstrAlarmTxt, "%04d-%s-%02d %02d:%02d ",
                    pLogData->sEventTime.xDate.nYear, pstrMonth, pLogData->sEventTime.xDate.nDay, pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin);
        }
        else
            strcpy(pstrAlarmTxt, "--------- --:-- ");

        switch(pLogData->nEventLogID)
        {
        case ALRID_SECURITYLOG_POWEROFF:
            sprintf(pstrTemp, "Power off for %02dd-%02dh:%02dm", (pLogData->nDurationMin / 1440), (pLogData->nDurationMin / 60), (pLogData->nDurationMin) % 60);
            break;

        case ALRID_SECURITYLOG_MMSI_INVALID:
            sprintf(pstrTemp, "MMSI NULL for %02dd-%02dh:%02dm", (pLogData->nDurationMin / 1440), (pLogData->nDurationMin / 60), (pLogData->nDurationMin) % 60);
            break;

        case ALRID_SECURITYLOG_TX_SHUTDOWN:
            strcpy(pstrTemp, "TX shutdown");
            break;

        case ALRID_SECURITYLOG_RXONLYMODE:
            sprintf(pstrTemp, "RX only for %02dd-%02dh:%02dm", (pLogData->nDurationMin / 1440), (pLogData->nDurationMin / 60), (pLogData->nDurationMin) % 60);
            break;

        default:
            return;
        }
        strcat(pstrAlarmTxt, pstrTemp);
    }

    //----------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.10.2.1
    // The "alarm condition" field shall be set to "A" when the alarm condition threshold is exceeded,
    // and "V" when the alarm condition returns to a level that does not exceed the threshold. During
    // healthy conditions (no alarm condition) an empty ALR sentence shall be sent at one-minute intervals.
    // The local alarm identifiers (alarm ID) given in Table 2 are defined for the use with formatters
    // ALR, ACK, and as text identifiers in TXT sentences to link associated messages.
    // ALR-sentences with "alarm numbers" greater than 099 cannot be followed by TXT-sentences containing additional
    // information by using the TXT-sentence's "text identifier". The "text identifier" is limited to the range of 01 to 99.
    // Additional numbers may be used by the manufacturer for other purposes but shall be in the range 051 to 099.
    //----------------------------------------------------------------------------------------------------------------------
    int nFullLen = CAlr::MakeSentence(pstrOutMsg, nAlrIndex, pUtcTime, nAlrStat, pstrAlarmTxt);
    if (nFullLen > 0)
    {
        SendOutData(pstrOutMsg, nFullLen);
    }
}

/**
 * @brief Send ABK sentence to PI
 * @param uDestMMSI The destination MMSI
 * @param nAckRxCh The RX channel
 * @param nVdlMsgID The VDL message ID
 * @param nMsgSeqNum The message sequence number
 * @param nAckType The ACK type
 * @return None
 */
void CPI::SendABKtoPI(UINT uDestMMSI, int nAckRxCh, int nVdlMsgID, int nMsgSeqNum, int nAckType)
{
    char abk_buff[RX_MAX_DATA_SIZE];
    memset((char*)abk_buff, 0x00, sizeof(abk_buff));

    int nFullLen = CAbk::MakeSentence(abk_buff, uDestMMSI, nAckRxCh, nVdlMsgID, nMsgSeqNum, nAckType);
    if (nFullLen > 0)
    {
        SendAllHighSpdPortData((BYTE*)abk_buff, nFullLen, TRUE);
    }
}

/**
 * @brief Send ACA and ACS sentences to PI
 * @param nRosIndex The ROS index to be sent
 * @param bSendAllPI Whether to send to all PI
 * @return None
 */
void CPI::SendACAACStoPI(int nRosIndex, bool bSendAllPI)
{
    char pstrBuffACA[RX_MAX_DATA_SIZE];
    char pstrBuffACS[RX_MAX_DATA_SIZE];

    memset((char*)pstrBuffACA, 0x00, sizeof(pstrBuffACA));
    memset((char*)pstrBuffACS, 0x00, sizeof(pstrBuffACS));

    int nFullLen = CAca::MakeSentence(pstrBuffACA, &CROSMgr::getInst()->m_vAllRosDATA[nRosIndex], nRosIndex);
    if (nFullLen > 0)
    {
        if (bSendAllPI)
            SendAllHighSpdPortData((BYTE*)pstrBuffACA, nFullLen, TRUE);
        else
            SendOutData(pstrBuffACA, nFullLen);
    }

    if(m_nHighSpdPortID != HIGHSPD_PORTID_MKD)
    {
        nFullLen = CAcs::MakeSentence(pstrBuffACS, &CROSMgr::getInst()->m_vAllRosDATA[nRosIndex], nRosIndex);
        if (nFullLen > 0)
        {
            if (bSendAllPI)
                SendAllHighSpdPortData((BYTE*)pstrBuffACS, nFullLen, TRUE);
            else
                SendOutData(pstrBuffACS, nFullLen);
        }
    }

    // Increase the sequential message identifier
    CAca::IncSequentialId();
}

/**
 * @brief Send all ACA and ACS sentences to PI
 * @param None
 * @return None
 */
void CPI::SendAllACAACStoPI(void)
{
    for(int i = 0 ; i < MAX_ROS_DATA_SIZE ; i++)
    {
        if(CROSMgr::getInst()->m_vAllRosDATA[i].bValidMode == MODE_VAL_ON)
            SendACAACStoPI(i);
    }

    if(m_nHighSpdPortID != HIGHSPD_PORTID_0)
    {
        SendACAACStoPI(ROS_IDX_HIGHSEA);
    }
}

/**
 * @brief Send RF Test mode message to PI
 * @param send_text The text to be sent
 * @param nMode The mode to be sent
 * @return None
 */
void CPI::SendRfTestModeMsgToPI(char *send_text, int nMode)
{
    char pstrBuff[RX_MAX_DATA_SIZE];

    memset((BYTE *)pstrBuff, 0x00, sizeof(pstrBuff));

    sprintf((char*)pstrBuff, "$AIINL,DIG,%s,%d", send_text, nMode);
    CSentence::AddSentenceTail(pstrBuff);

    SendOutStr(pstrBuff);
}

/**
 * @brief Send setup data to PI for Test SART
 * @param None
 * @return None
 */
void CPI::SendSetupShowTestingSART()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf(pstrBuff, "$AIINL,113,%d", CSetupMgr::getInst()->GetShowTestingSART());
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send setup data to PI for Silent Mode
 * @param None
 * @return None
 */
void CPI::SendSetupEnableSilentMode()
{
    char pstrBuff[RX_MAX_DATA_SIZE];
    sprintf((char*)pstrBuff, "$AIINL,114,%d", CSetupMgr::getInst()->GetEnableSilentMode());
    CSentence::AddSentenceTail(pstrBuff);
    SendOutStr(pstrBuff);
}

/**
 * @brief Send ALR sentence to PI
 * @param nAlrStartID The start ALR ID
 * @param nAlrEndID The end ALR ID
 * @return None
 */
void CPI::SendINLALRToPI(int nAlrStartID, int nAlrEndID)
{
    //----------------------------------------------------------------------------------------
    // - Start.Alarm.id ~ End.Alarm.id까지 PI로 전송. PI에서 MKD로 Bypass.
    // - Alarm.id=90을 받으면 119에 상관없이 모든 Security Log를 전송.
    //----------------------------------------------------------------------------------------
    int  i;

    if(nAlrStartID == 0 && nAlrEndID == 0)
        return;

    if(nAlrStartID == ALRID_SECURITYLOG_MIN)
    {
        for(i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
        {
            if(CEventLogMgr::getInst()->m_pLogData[i].nLogFlag == TRUE)
                SendSecurityLogData(&(CEventLogMgr::getInst()->m_pLogData[i]), &(cShip::getOwnShipInst()->xUtcTime), i+ALRID_SECURITYLOG_MIN, BAM_ALERT_STAT_ACTIVE_UNACK);
        }
        SendSecurityLogData(NULL, &(cShip::getOwnShipInst()->xUtcTime), ALRID_SECURITYLOG_MAX, BAM_ALERT_STAT_ACTIVE_UNACK);
    }
}

/**
 * @brief Send TXT sentence to PI
 * @param nTxtID The TXT ID to be sent
 * @param bSendAllPI Whether to send to all PI
 * @return None
 */
void CPI::SendTXTtoPI(int nTxtID, bool bSendAllPI)
{
    //-------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ******** Using the TXT formatter
    // Status messages shall be IEC 61162-1 compliant "$AITXT"-sentences on the presentation interface output port.
    // Status messages do not activate the alarm relay and do not require an acknowledgement.
    //-------------------------------------------------------------------------------------------------------------

    if(nTxtID < 0)
    {
        return;
    }

    // Since it's a TXT ID not defined in the specification, just return it.
    if(nTxtID == TXT_ID_NO_SENSOR_POS)
        return;

    char pstrBuff[RX_MAX_DATA_SIZE];
    memset(pstrBuff, 0x00, RX_MAX_DATA_SIZE);

    int nFullLen = CTxt::MakeSentence(pstrBuff, nTxtID);
    if (nFullLen > 0)
    {
        if (bSendAllPI)
            SendAllHighSpdPortData((BYTE*)pstrBuff, nFullLen, TRUE);
        else
            SendOutData(pstrBuff, nFullLen);
    }
}

/**
 * @brief Check the status of the TXT and send it to PI
 * @param nTxtID The TXT ID to be sent
 * @return None
 */
void CPI::CheckStatAndSendTXT(int nTxtID)
{
    if(CTxt::CheckTxtStat(nTxtID)) {
        SendTXTtoPI(nTxtID);
    }
}

/**
 * @brief Send all TXT sentences to PI
 * @param bSendUnconditionally Whether to send all TXT sentences unconditionally
 * @return None
 */
void CPI::SendAllTXTtoPI(BOOL bSendUnconditionally)
{
    if(bSendUnconditionally)
    {
        SendTXTtoPI(TXT_ID_EXT_DGNSS);
        SendTXTtoPI(TXT_ID_EXT_GNSS);
        SendTXTtoPI(TXT_ID_INT_DGNSS_BEACON);
        SendTXTtoPI(TXT_ID_INT_DGNSS_MSG17);
        SendTXTtoPI(TXT_ID_INT_GNSS);

        SendTXTtoPI(TXT_ID_EXT_SOG_COG);
        SendTXTtoPI(TXT_ID_INT_SOG_COG);
        SendTXTtoPI(TXT_ID_HEADING_VALID);
        SendTXTtoPI(TXT_ID_ROT_INDICATOR);
        SendTXTtoPI(TXT_ID_OTHER_ROT_SRC);
        SendTXTtoPI(TXT_ID_CH_MNG_CHANGE);

        // refer to IEC-61993-2:2018 ED3.0 6.10.2.4
        SendTXTtoPI(TXT_ID_LOW_PWR_MODE_ACT);
        SendTXTtoPI(TXT_ID_LOW_PWR_MODE_INACT);
        SendTXTtoPI(TXT_ID_ASSIGNED_MODE);
        SendTXTtoPI(TXT_ID_DATALINK_MNG_MODE);
        SendTXTtoPI(TXT_ID_CH_MNG_MODE);
        SendTXTtoPI(TXT_ID_GRP_ASSGNED_MODE);
        SendTXTtoPI(TXT_ID_RTN_DEFAULT_MODE);
    }
    else
    {
        CheckStatAndSendTXT(TXT_ID_EXT_DGNSS);
        CheckStatAndSendTXT(TXT_ID_EXT_GNSS);
        CheckStatAndSendTXT(TXT_ID_INT_DGNSS_BEACON);
        CheckStatAndSendTXT(TXT_ID_INT_DGNSS_MSG17);
        CheckStatAndSendTXT(TXT_ID_INT_GNSS);
        if(m_nHighSpdPortID != HIGHSPD_PORTID_MKD)
            CheckStatAndSendTXT(TXT_ID_INT_GNSS_SBAS);

        CheckStatAndSendTXT(TXT_ID_EXT_SOG_COG);
        CheckStatAndSendTXT(TXT_ID_INT_SOG_COG);
        CheckStatAndSendTXT(TXT_ID_HEADING_VALID);
        CheckStatAndSendTXT(TXT_ID_ROT_INDICATOR);
        CheckStatAndSendTXT(TXT_ID_OTHER_ROT_SRC);
        CheckStatAndSendTXT(TXT_ID_CH_MNG_CHANGE);
        // refer to IEC-61993-2:2018 ED3.0 6.10.2.4
        CheckStatAndSendTXT(TXT_ID_LOW_PWR_MODE_ACT);
        CheckStatAndSendTXT(TXT_ID_LOW_PWR_MODE_INACT);
        CheckStatAndSendTXT(TXT_ID_ASSIGNED_MODE);
        CheckStatAndSendTXT(TXT_ID_DATALINK_MNG_MODE);
        CheckStatAndSendTXT(TXT_ID_CH_MNG_MODE);
        CheckStatAndSendTXT(TXT_ID_GRP_ASSGNED_MODE);
        CheckStatAndSendTXT(TXT_ID_RTN_DEFAULT_MODE);
    }

    if(m_nHighSpdPortID == HIGHSPD_PORTID_MKD)
    {
        SendTXTtoPI(TXT_ID_ENDOF_ROSLIST, true);
    }
}

/**
 * @brief Send EPV sentence to PI
 * @param None
 * @return None
 */
void CPI::SendAllEPVtoPI()
{
    //-------------------------------------------------------------------
    // EPV became mandatory from IEC 61993-2 ed3(2018-07)
    //-------------------------------------------------------------------
    // IEC 61993-2 ed3(2018-07) 7.6.3.6 Configuration using EPV sentence
    // IEC 61993-2 ed3(2018-07) 19.9.4
    // IEC 61993-2 ed3(2018-07) 19.9.5
    // IEC 61993-2 ed3(2018-07) Annex F
    // IEC-61993-2 (2012) Annex E E.2
    //-------------------------------------------------------------------
    char    pstrResp[RX_MAX_DATA_SIZE];
    memset(pstrResp, 0x00, RX_MAX_DATA_SIZE);

    for (int nPropertyID = EPV_PROP_SENSOR1_BAUDRATE; nPropertyID <= EPV_PROP_ANTENNA_ED; nPropertyID++)
    {
        int nFullLen = CEpv::MakeSentence(pstrResp, nPropertyID);
        if (nFullLen > 0)
        {
            SendOutData(pstrResp, nFullLen);
        }
    }
}

/**
 * @brief Send TRL sentence to PI
 * @param None
 * @return None
 */
void CPI::SendTRLtoPI()
{
    char pstrOutMsg[RX_MAX_DATA_SIZE];
    int  i = 0;
    int  nLogCnt = 0;
    int  nEntryNum = 0;

    for(i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
    {
        if(CEventLogMgr::getInst()->m_pLogData[i].nLogFlag == TRUE)
            nLogCnt++;
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Total number of log entries (1...10). When a query is received for this sentence and no log entries exist, this
    // field should be set to “0” and all other fields should be set to NULL. When a query is received for this sentence
    // and one or more log entries exist, this field should report the total number of log entries.
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    if (!nLogCnt)
    {
        int nLen = CTrl::MakeSentence(pstrOutMsg, STR_TALKER_AI, NULL, 0, 0);
        if (nLen > 0)
            SendOutData(pstrOutMsg, nLen);
    }
    else
    {
        for(i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
        {
            if(CEventLogMgr::getInst()->m_pLogData[i].nLogFlag == TRUE)
            {
                int nLen = CTrl::MakeSentence(pstrOutMsg, STR_TALKER_AI, &(CEventLogMgr::getInst()->m_pLogData[i]), nLogCnt, ++nEntryNum);
                if (nLen > 0)
                    SendOutData(pstrOutMsg, nLen);
            }
        }
    }
}

/**
 * @brief Send NAK sentence to PI
 * @param pstrAffectSenctence The sentence to be affected
 * @param nReasonCode The reason code
 * @param pstrTalkerID The talker ID
 * @return None
 */
void CPI::SendNAK(char *pstrAffectSenctence, int nReasonCode, const char *pstrTalkerID)
{
    char pstrTxt[RX_MAX_DATA_SIZE];
    int nLen = CNak::MakeSentence(pstrTxt, pstrTalkerID, pstrAffectSenctence, nReasonCode);
    if(nLen > 0)
    {
        SendOutData(pstrTxt, nLen);
        return;
    }
}
/*********************************************************************************/

/**
 * @brief Process the security password command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessSPW(char *pstrCmd)
{
    if (m_xSpw.Parse(pstrCmd))
    {
        INFO_LOG("PI-%d] SPW OK, sentence:%d\r\n", m_nHighSpdPortID, m_xSpw.GetCode());
    }
    else
    {
        WARNING_LOG("PI-%d] SPW,ignore\r\n", m_nHighSpdPortID);
    }
}

/**
 * @brief Get the MD5 hash key for the SSA command
 * @param pstrUserKey The user key to be hashed
 * @param pHashKey The hash key to be returned
 * @return None
 */
void CPI::GetMd5HashKey(const char* pstrUserKey, BYTE *pHashKey)
{
    MD5_CTX context;
    MD5Init(&context);
    MD5Update(&context, (BYTE*)pstrUserKey, strlen(pstrUserKey));
    MD5Final(pHashKey, &context);
}

/**
 * @brief Check the MD5 hash key for the SSA command
 * @param pstrCmd The sentence to be parsed
 * @param pstrHashKeyInput The hash key to be compared
 * @return TRUE if matched, FALSE otherwise
 */
BOOL CPI::CheckMd5MatchSSA(char *pstrCmd, char *pstrHashKeyInput)
{
    BYTE pHashKey[LEN_SSA_MD5_HASH_BIN+1];
    char pstrInput[256];

    strcpy((char*)pstrInput, (char*)CSetupMgr::getInst()->GetUserKeySSA());
    strcat((char*)pstrInput, pstrCmd);
    RightTrimStr(pstrInput, 0x0A);
    RightTrimStr(pstrInput, 0x0D);
    RightTrimStr(pstrInput, '*');
    GetMd5HashKey((const char*)pstrInput, pHashKey);

    char pstrPwdMd5KeySSA[LEN_SSA_MD5_STR+10];
    memset(pstrPwdMd5KeySSA, 0, sizeof(pstrPwdMd5KeySSA));

    ByteArrayToHexStr(pHashKey, LEN_SSA_MD5_HASH_BIN, TRUE, pstrPwdMd5KeySSA);

    return !strcmp((char*)pstrPwdMd5KeySSA, (char*)pstrHashKeyInput);
}

/**
 * @brief Process the SSA command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessSSA(char *pstrCmd)
{
    if (m_xSsa.Parse(pstrCmd))
    {
        INFO_LOG("PI-%d] SSA OK, sentence:%d\r\n", m_nHighSpdPortID, m_xSsa.GetCode());
    }
    else
    {
        WARNING_LOG("PI-%d] SSA,ignore\r\n", m_nHighSpdPortID);
    }
}

/**
 * @brief Process the SSD command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessSSD(char *pstrCmd)
{
    BOOL bPwdPassed = TRUE;

    if(CSetupMgr::getInst()->GetPilotPortRestricted())
    {
        WARNING_LOG("SSD] Access denied! Pilot port restricted mode enabled: %d\r\n", CSetupMgr::getInst()->GetPilotPortRestricted());
        SendNAK((char*)"SSD", NAK_REASON_ACCESS_DENIED);
        return;
    }

    if(m_nHighSpdPortID != HIGHSPD_PORTID_MKD)
    {
        bPwdPassed = (m_xSpw.GetCode() == SPW_SSD);
        if(!bPwdPassed)
        {
            BOOL bSSAMatch = FALSE;
            if(m_xSsa.GetCode() == SSA_SSD)
                bSSAMatch = CheckMd5MatchSSA(pstrCmd, m_xSsa.GetAuthenticationPtr());

            bPwdPassed = bSSAMatch;
        }
    }

    if(!bPwdPassed)
    {
        WARNING_LOG("SSD-%d] Password not matched\r\n", m_nHighSpdPortID);
        SendNAK((char*)"SSD", NAK_REASON_ACCESS_DENIED);
        return;
    }

    CSsd::Parse(pstrCmd);
}

/**
 * @brief Process the VSD command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessVSD(char *pstrCmd)
{
    if(CSetupMgr::getInst()->GetPilotPortRestricted())
    {
        WARNING_LOG("VSD] Access denied! Pilot port restricted mode enabled: %d\r\n", CSetupMgr::getInst()->GetPilotPortRestricted());
        SendNAK((char*)"VSD", NAK_REASON_ACCESS_DENIED);
        return;
    }

    CVsd::Parse(pstrCmd);
}

/**
 * @brief Process the ABM command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessABM(char *pstrCmd)
{
    //------------------------------------------------------------------------------
    // ABM : Addressed binary & safety related messages
    // 메시지 6, 12, 25, 26 의 송신요구
    // ABM 수신 시 VDL 전송 후, 슬롯 초과시 거부 시에 ABK 출력할것!
    // 5 슬롯 초과 또는 현재 프레임에서 20 슬롯 RATDMA 초과시 ABK 로 NACK 출력할것!
    // TxCh은 AUTO시에는 Dest.MMSI로부터 최근 수신 채널로 Addr Msg를 전송.
    //------------------------------------------------------------------------------

    char pstrEncData[RX_MAX_DATA_SIZE];
    int     nNumTotalSentence = 0, nSentenceID = 0, nTxSeqNum = 0;
    int  tx_ch = 0, nABMMsgID = 0, nNumFillBits = 0;
    UINT uDestMMSI = 0;
    int  nNumDataBits = 0;
    BOOL result = FALSE;

    nNumTotalSentence = CSentence::GetFieldInteger(pstrCmd, 1); // sentence total num
    nSentenceID = CSentence::GetFieldInteger(pstrCmd, 2);       // sentence num
    nTxSeqNum = CSentence::GetFieldInteger(pstrCmd, 3);         // Seq.Msg.Id
    uDestMMSI = CSentence::GetFieldInteger(pstrCmd, 4);         // Dest.mmsi
    tx_ch = CSentence::GetFieldInteger(pstrCmd, 5);             // Tx Channel(AUTO, AIS1, AIS2, AIS12)
    nABMMsgID = CSentence::GetFieldInteger(pstrCmd, 6);         // Tx Msg Num

    if(1 <= nNumTotalSentence && nNumTotalSentence <= 9 && 1 <= nSentenceID && nSentenceID <= 9 && 0 <= nTxSeqNum && nTxSeqNum <= 3)
    {
        if(tx_ch <= ABM_CH_BOTH)
        {
            if(nABMMsgID == AIS_MSG_NO_ID_06 || nABMMsgID == AIS_MSG_NO_ID_12 || nABMMsgID == AIS_MSG_NO_ID_25 ||
                nABMMsgID == AIS_MSG_NO_ID_26 || nABMMsgID == PI_MSGID_70 || nABMMsgID == PI_MSGID_71)
            {
                CSentence::GetFieldString(pstrCmd, 7, pstrEncData, sizeof(pstrEncData));

                nNumFillBits = CSentence::GetFieldInteger(pstrCmd, 8);
                if(nNumFillBits <= 5)
                {
                    nNumDataBits = (strlen(pstrEncData)*6) - nNumFillBits;

                    DEBUG_LOG("ABM] nNumDataBits : %d, nNumFillBits:%d, s:%d\r\n", nNumDataBits, nNumFillBits, cTimerSys::getInst()->GetCurTimerSec());

                    if(CVdlTxMgr::IsTxAvailable())
                    {
                        BOOL bEnableAis1, bEnableAis2;
                        CheckTxChPI(uDestMMSI, tx_ch, &bEnableAis1, &bEnableAis2);

                        if(nABMMsgID == AIS_MSG_NO_ID_06 || nABMMsgID == AIS_MSG_NO_ID_12)
                        {
                            if(bEnableAis1)
                                result |= ProcessABMSub_06_12(nNumTotalSentence, nSentenceID, nTxSeqNum, uDestMMSI, AIS_CHANNEL_AIS1, nABMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
                            if(bEnableAis2)
                                result |= ProcessABMSub_06_12(nNumTotalSentence, nSentenceID, nTxSeqNum, uDestMMSI, AIS_CHANNEL_AIS2, nABMMsgID, nNumFillBits, pstrEncData, nNumDataBits);

                            if(!result)
                                SendABKtoPI(uDestMMSI, AIS_CHANNEL_NONE, nABMMsgID, nTxSeqNum, ABK_TX_FAIL);
                            return;
                        }
                        else if(nABMMsgID == AIS_MSG_NO_ID_25 || nABMMsgID == PI_MSGID_70)
                        {
                            if(!CSetupMgr::getInst()->IsAisBClassCS())
                            {
                                if(bEnableAis1)
                                    result |= ProcessABMSub_25(nNumTotalSentence, nSentenceID, nTxSeqNum, uDestMMSI, AIS_CHANNEL_AIS1, nABMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
                                if(bEnableAis2)
                                    result |= ProcessABMSub_25(nNumTotalSentence, nSentenceID, nTxSeqNum, uDestMMSI, AIS_CHANNEL_AIS2, nABMMsgID, nNumFillBits, pstrEncData, nNumDataBits);

                                if(CheckABMBBMLastSentence(nNumTotalSentence, nSentenceID) && !result)
                                    SendABKtoPI(uDestMMSI, AIS_CHANNEL_NONE, nABMMsgID, nTxSeqNum, ABK_TX_FAIL);
                            }
                            return;
                        }
                        else if(nABMMsgID == AIS_MSG_NO_ID_26 || nABMMsgID == PI_MSGID_71)
                        {
                            if(bEnableAis1)
                                result |= ProcessABMSub_26(nNumTotalSentence, nSentenceID, nTxSeqNum, uDestMMSI, AIS_CHANNEL_AIS1, nABMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
                            if(bEnableAis2)
                                result |= ProcessABMSub_26(nNumTotalSentence, nSentenceID, nTxSeqNum, uDestMMSI, AIS_CHANNEL_AIS2, nABMMsgID, nNumFillBits, pstrEncData, nNumDataBits);

                            if(CheckABMBBMLastSentence(nNumTotalSentence, nSentenceID) && !result)
                                SendABKtoPI(uDestMMSI, AIS_CHANNEL_NONE, nABMMsgID, nTxSeqNum, ABK_TX_FAIL);
                            return;
                        }
                    }
                    else
                    {
                        WARNING_LOG("ABM] wrong-5, TX not avail!\r\n");
                    }
                }
                else
                {
                    WARNING_LOG("ABM] wrong-4, nNumFillBits : %d\r\n", nNumFillBits);
                }
            }
            else
            {
                WARNING_LOG("ABM] wrong-3, nABMMsgID : %d\r\n", nABMMsgID);
            }
        }
        else
        {
            WARNING_LOG("ABM] wrong-2, tx_ch : %d\r\n", tx_ch);
        }
    }
    else
    {
        WARNING_LOG("ABM] wrong-1, stTotalNum : %d, stNum : %d, seqMsgID : %d\r\n", nNumTotalSentence, nSentenceID , nTxSeqNum);
    }

    if(CheckABMBBMLastSentence(nNumTotalSentence, nSentenceID) && !result)
        SendABKtoPI(uDestMMSI, AIS_CHANNEL_NONE, nABMMsgID, nTxSeqNum, ABK_TX_FAIL);            // ABK 전송: uDestMMSI, 2= message could not be broadcast.
}

/**
 * @brief Process the ABM(06, 12) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessABMSub_06_12(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT uDestMMSI, UINT8 uChID, int nTxMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    int   nNumTxSlot;
    TDMA_UNSCHE_ADDRMSG* pTxParam;
    CChannelMgr *pCh = CLayerNetwork::getInst()->GetChPtr(uChID);

    if(nTxMsgID != AIS_MSG_NO_ID_06 && nTxMsgID != AIS_MSG_NO_ID_12)
        return FALSE;

    DEBUG_LOG("ABM-Sub] stTotalNum : %d, stNum : %d, seqMsgID : %d, dest : %d, ch : %d, msg : %d, fill : %d, len : %d\r\n",
            nNumTotalSentence, nSentenceID , nTxSeqNum, uDestMMSI, uChID, nTxMsgID, nNumFillBits, nNumDataBits);

    if(!(pTxParam = pCh->m_pVdlTxMgr->GetBinMsgBuffPtr_Addressed(uDestMMSI, nTxMsgID, nTxSeqNum)))
    {
        WARNING_LOG("ABM] GetBuff Fail!\r\n");
        return FALSE;
    }

    // Multiple Sentence이면, First Sentence는 "6bit" 48 symbol만큼 채워져서 보내어진다. 이 때, nNumFillBits는 0이 되고,
    // 8bit로 나누어 떨어진다. (288/8=36byte), Last Sentence가 되면, 총 bit수는 nNumFillBits만큼 감소한 후, Set되어진다.
    if(nSentenceID == 1) // 1st ABM
    {
        pCh->m_pVdlTxMgr->ClearBinMsgBuff_Addressed(pTxParam);
        pTxParam->nTxMsgID    = nTxMsgID;
        pTxParam->uDestMMSI = uDestMMSI;
        pTxParam->nTxSeqNum = nTxSeqNum;
        memset(pTxParam->pstrEncAisciiData, 0, sizeof(pTxParam->pstrEncAisciiData));

        strcpy(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits = nNumDataBits;
    }
    else    // 2st ABM ~ last ABM
    {
        strcat(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits += nNumDataBits;
    }

    if(nSentenceID != nNumTotalSentence)
    {
        return TRUE;
    }

    // Address Msg Max Data Check
    if(pTxParam->nNumDataBits <= MAX_ABM3_BIT)
    {
        DEBUG_LOG("ABM-06-12] nNumTotalSentence:%d, nNumDataBits:%d, nNumFillBits:%d\r\n",
                nNumTotalSentence, pTxParam->nNumDataBits, nNumFillBits);

        if(CAisLib::ConvertASCIICodeTo6BitBinary(pTxParam->pMsgBinData, pTxParam->pstrEncAisciiData))
        {
            nNumTxSlot = CAisLib::GetSlotAppDataLength(TYPE_ABM, nTxMsgID, FALSE, FALSE, pTxParam->nNumDataBits);
            if(nNumTxSlot != MSG_SLOT_OVER)
            {
                if(pCh->m_pVdlTxMgr->CheckTxCntOkForBinMsg(TYPE_ABM, nNumTxSlot))
                {
                    INFO_LOG("ABM-06-12] OK! Unscheduled TX dest : %d, msg : %d, seq : %d, slot : %d\r\n",
                        uDestMMSI, nTxMsgID, nTxSeqNum, nNumTxSlot);
                        
                    pCh->m_pVdlTxMgr->SetUnScheAddrTxMsgInfo(pTxParam, uDestMMSI, nTxMsgID, nTxSeqNum, nNumTxSlot);
                    pCh->m_pVdlTxMgr->SetTxSlotIdBinMsg(TYPE_ABM, nNumTxSlot);
                    return TRUE;
                }
                else
                {
                    WARNING_LOG("ABM-06-12] Error, CheckTxCntOkForBinMsg m:%d, txSlot : %d\r\n",
                        nTxMsgID, nNumTxSlot);
                }
            }
            else
            {
                WARNING_LOG("ABM-06-12] Error, needs too many slots! m:%d, %d\r\n", nTxMsgID, nNumTxSlot);
            }
        }
        else
        {
            WARNING_LOG("ABM-06-12] Error, Invalid ABM(06) Char! \r\n");
        }
    }
    else
    {
        WARNING_LOG("ABM-06-12] Error, too large data : %d, m:%d\r\n", pTxParam->nNumDataBits, nTxMsgID);
    }

    pCh->m_pVdlTxMgr->ClearBinMsgBuff_Addressed(pTxParam);
    return FALSE;
}

/**
 * @brief Process the ABM(25) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessABMSub_25(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT uDestMMSI, UINT8 uChID, int nABMMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    int   nNumTxSlot;
    TDMA_UNSCHE_MSG25* pTxParam;
    BYTE bBinDataFlag = BINARY_UNSTRUCTURED;
    CChannelMgr *pCh = CLayerNetwork::getInst()->GetChPtr(uChID);
    int nTxMsgID = nABMMsgID;

    DEBUG_LOG("ABM-Sub-25] stTotalNum : %d, stNum : %d, seqMsgID : %d, dest : %d, ch : %d, msg : %d, fill : %d, len : %d\r\n",
        nNumTotalSentence, nSentenceID , nTxSeqNum, uDestMMSI, uChID, nTxMsgID, nNumFillBits, nNumDataBits);

    if(nTxMsgID == AIS_MSG_NO_ID_25)
        bBinDataFlag = BINARY_ASM;
    else if(nTxMsgID == PI_MSGID_70)
    {
        nTxMsgID = AIS_MSG_NO_ID_25;
        bBinDataFlag = BINARY_UNSTRUCTURED;
    }
    else
        return FALSE;

    if(!(pTxParam = pCh->m_pVdlTxMgr->GetMsg25BuffPtr(uDestMMSI, nTxMsgID, nTxSeqNum)))
    {
        WARNING_LOG("ABM-25] GetBuff Fail!\r\n");
        return FALSE;
    }

    int nMaxDataBits = 128;
    if(uDestMMSI != AIS_AB_MMSI_NULL)
        nMaxDataBits = 96;

    // Multiple Sentence이면, First Sentence는 "6bit" 48 symbol만큼 채워져서 보내어진다. 이 때, nNumFillBits는 0이 되고,
    // 8bit로 나누어 떨어진다. (288/8=36byte), Last Sentence가 되면, 총 bit수는 nNumFillBits만큼 감소한 후, Set되어진다.
    if(nSentenceID == 1) // 1st ABM
    {
        pCh->m_pVdlTxMgr->ClearMsg25Buff(pTxParam);
        pTxParam->nTxMsgID = nTxMsgID;
        pTxParam->nTxSeqNum= nTxSeqNum;
        memset(pTxParam->pstrEncAisciiData, 0, sizeof(pTxParam->pstrEncAisciiData));

        strcpy(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits = nNumDataBits;
    }
    else    // 2st ABM ~ last ABM
    {
        strcat(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits += nNumDataBits;
    }

    if(nSentenceID != nNumTotalSentence)
    {
        return TRUE;
    }

    // Address Msg Max Data Check
    if(pTxParam->nNumDataBits <= nMaxDataBits)
    {
        DEBUG_LOG("ABM-25] nNumTotalSentence:%d, nNumDataBits:%d, nNumFillBits:%d\r\n",
            nNumTotalSentence, pTxParam->nNumDataBits, nNumFillBits);

        if(CAisLib::ConvertASCIICodeTo6BitBinary(pTxParam->pMsgBinData, pTxParam->pstrEncAisciiData))
        {
            nNumTxSlot = CAisLib::GetSlotAppDataLength(TYPE_ABM, nTxMsgID, (uDestMMSI != AIS_AB_MMSI_NULL), bBinDataFlag, pTxParam->nNumDataBits);
            if(nNumTxSlot != MSG_SLOT_OVER)
            {
                if(pCh->m_pVdlTxMgr->CheckTxCntOkForBinMsg(TYPE_ABM, nNumTxSlot))
                {
                    INFO_LOG("ABM-25] OK! Unscheduled TX dest : %d, msg : %d, seq : %d, slot : %d\r\n",
                        uDestMMSI, nTxMsgID, nTxSeqNum, nNumTxSlot);

                    pCh->m_pVdlTxMgr->SetTxMsg25Info(pTxParam, uDestMMSI, nABMMsgID, nTxMsgID, nTxSeqNum, nNumTxSlot, bBinDataFlag);
                    pCh->m_pVdlTxMgr->SetTxSlotIdBinMsg(TYPE_ABM, nNumTxSlot);
                    return TRUE;
                }
                else
                {
                    WARNING_LOG("ABM-25] Error, CheckTxCntOkForBinMsg m:%d, txSlot : %d\r\n",
                        nTxMsgID, nNumTxSlot);
                }
            }
            else
            {
                WARNING_LOG("ABM-25] Error, needs too many slots! %d, m:%d\r\n", nNumTxSlot, nTxMsgID);
            }
        }
        else
        {
            WARNING_LOG("ABM-25] Error, Invalid ABM(06) Char! \r\n");
        }
    }
    else
    {
        WARNING_LOG("ABM-25] Error, too large data : %d max : %d, m:%d\r\n", pTxParam->nNumDataBits, nMaxDataBits, nTxMsgID);
    }

    pCh->m_pVdlTxMgr->ClearMsg25Buff(pTxParam);
    return FALSE;
}

/**
 * @brief Process the ABM(26) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessABMSub_26(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT uDestMMSI, UINT8 uChID, int nABMMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    int   nNumTxSlot;
    TDMA_UNSCHE_MSG26* pTxParam;
    BYTE bBinDataFlag = BINARY_UNSTRUCTURED;
    CChannelMgr *pCh = CLayerNetwork::getInst()->GetChPtr(uChID);
    int nTxMsgID = nABMMsgID;

    DEBUG_LOG("ABM-Sub-26] stTotalNum : %d, stNum : %d, seqMsgID : %d, dest : %d, ch : %d, msg : %d, fill : %d, len : %d\r\n",
        nNumTotalSentence, nSentenceID , nTxSeqNum, uDestMMSI, uChID, nTxMsgID, nNumFillBits, nNumDataBits);

    if(nTxMsgID == AIS_MSG_NO_ID_26)
        bBinDataFlag = BINARY_ASM;
    else if(nTxMsgID == PI_MSGID_71)
    {
        nTxMsgID = AIS_MSG_NO_ID_26;
        bBinDataFlag = BINARY_UNSTRUCTURED;
    }
    else
        return FALSE;

    if(!(pTxParam = pCh->m_pVdlTxMgr->GetMsg26BuffPtr(uDestMMSI, nTxMsgID, nTxSeqNum)))
    {
        WARNING_LOG("ABM-26] GetBuff Fail!\r\n");
        return FALSE;
    }

    // Multiple Sentence이면, First Sentence는 "6bit" 48 symbol만큼 채워져서 보내어진다. 이 때, nNumFillBits는 0이 되고,
    // 8bit로 나누어 떨어진다. (288/8=36byte), Last Sentence가 되면, 총 bit수는 nNumFillBits만큼 감소한 후, Set되어진다.
    if(nSentenceID == 1) // 1st ABM
    {
        pCh->m_pVdlTxMgr->ClearMsg26Buff(pTxParam);
        pTxParam->nTxMsgID    = nTxMsgID;
        pTxParam->nTxSeqNum    = nTxSeqNum;
        pTxParam->uDestMMSI    = uDestMMSI;
        memset(pTxParam->pstrEncAisciiData, 0, sizeof(pTxParam->pstrEncAisciiData));

        strcpy(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits = nNumDataBits;
    }
    else    // 2st ABM ~ last ABM
    {
        strcat(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits += nNumDataBits;
    }

    if(nSentenceID != nNumTotalSentence)
    {
        return TRUE;
    }

    // Address Msg Max Data Check
    if(pTxParam->nNumDataBits <= MAX_ABM3_BIT)
    {
        DEBUG_LOG("ABM-26] nNumTotalSentence:%d, nNumDataBits:%d, nNumFillBits:%d\r\n",
            nNumTotalSentence, pTxParam->nNumDataBits, nNumFillBits);

        if(CAisLib::ConvertASCIICodeTo6BitBinary(pTxParam->pMsgBinData, pTxParam->pstrEncAisciiData))
        {
            nNumTxSlot = CAisLib::GetSlotAppDataLength(TYPE_ABM, nTxMsgID, (uDestMMSI != AIS_AB_MMSI_NULL), bBinDataFlag, pTxParam->nNumDataBits);
            if(nNumTxSlot != MSG_SLOT_OVER)
            {
                if(pCh->m_pVdlTxMgr->CheckTxCntOkForBinMsg(TYPE_ABM, nNumTxSlot))
                {
                    INFO_LOG("ABM-26] OK! Unscheduled TX dest : %d, msg : %d, seq : %d, slot : %d\r\n",
                        uDestMMSI, nTxMsgID, nTxSeqNum, nNumTxSlot);

                    pCh->m_pVdlTxMgr->SetTxMsg26Info(pTxParam, uDestMMSI, nABMMsgID, nTxMsgID, nTxSeqNum, nNumTxSlot, bBinDataFlag);
                    pCh->m_pVdlTxMgr->SetTxSlotIdBinMsg(TYPE_ABM, nNumTxSlot);
                    return TRUE;
                }
                else
                {
                    WARNING_LOG("ABM-26] Error, CheckTxCntOkForBinMsg m:%d, txSlot : %d\r\n",
                        nTxMsgID, nNumTxSlot);
                }
            }
            else
            {
                WARNING_LOG("ABM-26] Error, needs too many slots! %d, m:%d\r\n", nNumTxSlot, nTxMsgID);
            }
        }
        else
        {
            WARNING_LOG("ABM-26] Error, Invalid ABM(06) Char! \r\n");
        }
    }
    else
    {
        WARNING_LOG("ABM-26] Error, too large data : %d, m:%d\r\n", pTxParam->nNumDataBits, nTxMsgID);
    }

    pCh->m_pVdlTxMgr->ClearMsg26Buff(pTxParam);
    return FALSE;
}

/**
 * @brief Process the BBM command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessBBM(char *pstrCmd)
{
    //------------------------------------------------------------------------------
    // BBM : Broadcast binary message
    // 메시지 8, 14, 25, 26 의 송신
    // BBM 수신 시 VDL 전송 후, 슬롯 초과시 거부 시에 ABK 출력할것!
    // 5 슬롯 초과 또는 현재 프레임에서 20 슬롯 RATDMA 초과시 ABK 로 NACK 출력할것!
    //------------------------------------------------------------------------------
    char pstrEncData[RX_MAX_DATA_SIZE];
    int  nNumTotalSentence, nSentenceID, nTxSeqNum, tx_ch, nBBMMsgID, nNumFillBits;
    int  nNumDataBits;
    int  result = FALSE;

    nNumTotalSentence = CSentence::GetFieldInteger(pstrCmd, 1); // sentence total num
    nSentenceID = CSentence::GetFieldInteger(pstrCmd, 2);       // sentence num
    nTxSeqNum = CSentence::GetFieldInteger(pstrCmd, 3);         // Seq.Msg.Id
    tx_ch = CSentence::GetFieldInteger(pstrCmd, 4);             // Tx Channel
    nBBMMsgID = CSentence::GetFieldInteger(pstrCmd, 5);         // Tx Msg Num

    if(1 <= nNumTotalSentence && nNumTotalSentence <= ABMBBM_SENTENCENUM_MAX && 1 <= nSentenceID && nSentenceID <= ABMBBM_SENTENCENUM_MAX && 0 <= nTxSeqNum && nTxSeqNum <= ABMBBM_SEQNUM_MAX)
    {
        if(tx_ch <= 3)
        {
            CSentence::GetFieldString(pstrCmd, 6, pstrEncData, sizeof(pstrEncData));

            nNumFillBits = CSentence::GetFieldInteger(pstrCmd, 7);
            if(nNumFillBits <= 5)
            {
                nNumDataBits = (strlen(pstrEncData)*6) - nNumFillBits;

                if(CVdlTxMgr::IsTxAvailable())
                {
                    BOOL bEnableAis1, bEnableAis2;
                    CheckTxChPI(AIS_AB_MMSI_NULL, tx_ch, &bEnableAis1, &bEnableAis2);

                    if(bEnableAis1)
                        result |= ProcessBBMSub(nNumTotalSentence, nSentenceID, nTxSeqNum, AIS_CHANNEL_AIS1, nBBMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
                    if(bEnableAis2)
                        result |= ProcessBBMSub(nNumTotalSentence, nSentenceID, nTxSeqNum, AIS_CHANNEL_AIS2, nBBMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
                }
                else
                {
                    WARNING_LOG("BBM] wrong-4, TX unavailable!\r\n");
                }
            }
            else
            {
                WARNING_LOG("BBM] wrong-3, fill : %d\r\n", nNumFillBits);
            }
        }
        else
        {
            WARNING_LOG("BBM] wrong-2, tx_ch : %d\r\n", tx_ch);
        }
    }
    else
    {
        WARNING_LOG("BBM] wrong-1, st_total : %d, st : %d, seq : %d\r\n", nNumTotalSentence, nSentenceID, nTxSeqNum);
    }

    if(CheckABMBBMLastSentence(nNumTotalSentence, nSentenceID) && !result)
        SendABKtoPI(AIS_AB_MMSI_NULL, AIS_CHANNEL_NONE, nBBMMsgID, nTxSeqNum, ABK_TX_FAIL);
}

/**
 * @brief Process the BBM(8, 14, 25, 26) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessBBMSub(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nBBMMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    switch(nBBMMsgID)
    {
    case AIS_MSG_NO_ID_08:
    case AIS_MSG_NO_ID_14:
        return ProcessBBMSub_08_14(nNumTotalSentence, nSentenceID, nTxSeqNum, uChID, nBBMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
        break;

    case AIS_MSG_NO_ID_25:                    // Single slot binary message 25 (Binary data coded using the 16-bit application identifier)
    case PI_MSGID_70:                        // Single slot binary message 25 (unstructured binary data)
        if(CSetupMgr::getInst()->IsAisBClassCS())
            return FALSE;                        // ITU-R 1371-5 Table 80, class B "CS" mobile AIS stations should not transmit message 25
        return ProcessBBMSub_25(nNumTotalSentence, nSentenceID, nTxSeqNum, uChID, nBBMMsgID, nNumFillBits, pstrEncData, nNumDataBits);

    case AIS_MSG_NO_ID_26:                    // Multiple slot binary message 26 with communication state (Binary data coded using the 16-bit application identifier)
    case PI_MSGID_71:                        // Multiple slot binary message 26 with communication state (unstructured binary data)
        return ProcessBBMSub_26(nNumTotalSentence, nSentenceID, nTxSeqNum, uChID, nBBMMsgID, nNumFillBits, pstrEncData, nNumDataBits);
    }
    return FALSE;
}

/**
 * @brief Process the BBM(8, 14) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessBBMSub_08_14(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nVdlMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    int      nNumTxSlot;
    TDMA_UNSCHE_BROADMSG* pTxParam = NULL;
    CChannelMgr *pCh = CLayerNetwork::getInst()->GetChPtr(uChID);

    DEBUG_LOG("BBM-8-14] stTotalNum : %d, stNum : %d, seqMsgID : %d, ch : %d, msg : %d, fill : %d, len : %d\r\n",
        nNumTotalSentence, nSentenceID , nTxSeqNum, uChID, nVdlMsgID, nNumFillBits, nNumDataBits);

    if(!(pTxParam = pCh->m_pVdlTxMgr->GetBinMsgEmptyBuffBroadcast(nVdlMsgID, nTxSeqNum)))
    {
        WARNING_LOG("BBM-8-14] pTxParam NULL!\r\n");
        return FALSE;
    }

    DEBUG_LOG("BBM-8-14] buff : %x, %x, %d\r\n",
        pTxParam, pTxParam->pstrEncAisciiData, strlen(pTxParam->pstrEncAisciiData));

    if(nSentenceID == 1) // 1st BBM
    {
        pCh->m_pVdlTxMgr->ClearBinMsgBuff_Broadcast(pTxParam);
        pTxParam->nTxMsgID = nVdlMsgID;
        pTxParam->nTxSeqNum= nTxSeqNum;
        strcpy(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits = nNumDataBits;

        INFO_LOG("BBM-8-14] first BBM(%d) Sentence, ptr : 0x%x\r\n", nVdlMsgID, (DWORD)pTxParam);
    }
    else    // 2st BBM ~ last BBM
    {
        strcat(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits += nNumDataBits;

        INFO_LOG("BBM-8-14] Multi BBM(%d) Sentence, ptr : 0x%x, %d\r\n", nVdlMsgID, (DWORD)pTxParam, pTxParam->nNumDataBits);
    }

    if(nSentenceID != nNumTotalSentence)
    {
        return TRUE;
    }

    int nMaxTxDataBits = 0;
    if(nVdlMsgID == AIS_MSG_NO_ID_08)
        nMaxTxDataBits = 544;                    // 68 bytes, 3 slots
    else if(nVdlMsgID == AIS_MSG_NO_ID_14)
        nMaxTxDataBits = 720;                    // 90 bytes, 3 slots

    // Broadcast Max Data Check
    if(pTxParam->nNumDataBits <= nMaxTxDataBits)
    {
        if(!CAisLib::ConvertASCIICodeTo6BitBinary(pTxParam->pMsgBinData, pTxParam->pstrEncAisciiData))
        {
            WARNING_LOG("BBM-8-14] Error, Invalid BBM(08) Char! \r\n");
        }
        else
        {
            nNumTxSlot = CAisLib::GetSlotAppDataLength(TYPE_BBM, nVdlMsgID, FALSE, FALSE, pTxParam->nNumDataBits);
            if(nNumTxSlot != MSG_SLOT_OVER)
            {
                if(pCh->m_pVdlTxMgr->CheckTxCntOkForBinMsg(TYPE_BBM, nNumTxSlot))
                {
                    pCh->m_pVdlTxMgr->SetUnScheBroadTxMsgInfo(pTxParam, nVdlMsgID, nTxSeqNum, nNumTxSlot);
                    pCh->m_pVdlTxMgr->SetTxSlotIdBinMsg(TYPE_BBM, nNumTxSlot);

                    INFO_LOG("BBM-8-14] OK! nNumTxSlot : %d, dataBitsLen : %d, pTxParam : 0x%x, %d, %d, strlen:%d\r\n",
                            nNumTxSlot, pTxParam->nNumDataBits, (DWORD)pTxParam, pTxParam->bReserveTx, pTxParam->bAllocSlot, strlen(pTxParam->pstrEncAisciiData));
                    return TRUE;
                }
                else
                {
                    WARNING_LOG("BBM-8-14] error, exceeds 5 slot, max 20 slot! txSlot:%d\r\n", nNumTxSlot);
                }
            }
            else
            {
                WARNING_LOG("BBM-8-14] error, needs too many slots! %d, m:%d\r\n", nNumTxSlot, nVdlMsgID);
            }
        }
    }
    else
    {
        WARNING_LOG("BBM-8-14] Error, too large data! max:%d, %d, m:%d\r\n", nMaxTxDataBits, pTxParam->nNumDataBits, nVdlMsgID);
    }

    pCh->m_pVdlTxMgr->ClearBinMsgBuff_Broadcast(pTxParam);
    return FALSE;
}

/**
 * @brief Process the BBM(25) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessBBMSub_25(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nBBMMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    int     nNumTxSlot;
    TDMA_UNSCHE_MSG25* pTxParam;
    BYTE bBinDataFlag = BINARY_UNSTRUCTURED;

    UINT uDestMMSI = AIS_AB_MMSI_NULL;
    CChannelMgr *pCh = CLayerNetwork::getInst()->GetChPtr(uChID);

    int nTxMsgID = AIS_MSG_NO_ID_25;
    switch(nBBMMsgID)
    {
    case AIS_MSG_NO_ID_25:                        // Single slot binary message 25 (Binary data coded using the 16-bit application identifier)
        bBinDataFlag = BINARY_ASM;
        break;
    case PI_MSGID_70:                            // Single slot binary message 25 (unstructured binary data)
        bBinDataFlag = BINARY_UNSTRUCTURED;
        break;
    }

    DEBUG_LOG("BBM-25] stTotalNum : %d, stNum : %d, seqMsgID : %d, dest : %d, ch : %d, msg : %d, fill : %d, len : %d\r\n",
        nNumTotalSentence, nSentenceID , nTxSeqNum, uDestMMSI, uChID, nTxMsgID, nNumFillBits, nNumDataBits);

    if(!(pTxParam = pCh->m_pVdlTxMgr->GetMsg25BuffPtr(uDestMMSI, AIS_MSG_NO_ID_25, nTxSeqNum)))
        return FALSE;

    if(nSentenceID == 1) // 1st BBM
    {
        pCh->m_pVdlTxMgr->ClearMsg25Buff(pTxParam);
        pTxParam->nTxMsgID = nTxMsgID;
        pTxParam->nTxSeqNum= nTxSeqNum;
        strcpy(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits = nNumDataBits;

        DEBUG_LOG("BBM-25] first BBM(25) Sentence \r\n");
    }
    else    // 2st BBM ~ last BBM
    {
        strcat(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits += nNumDataBits;

        DEBUG_LOG("BBM-25] Multi BBM(25) Sentence \r\n");
    }

    if(nSentenceID != nNumTotalSentence)
    {
        return TRUE;
    }

    // Broadcast Max Data Check
    if(pTxParam->nNumDataBits <= MAX_TX_BITS_MSG25)
    {
        if(!CAisLib::ConvertASCIICodeTo6BitBinary(pTxParam->pMsgBinData, pTxParam->pstrEncAisciiData))
        {
            WARNING_LOG("BBM-25] Error, Invalid BBM(25) Char! \r\n");
        }
        else
        {
            nNumTxSlot = CAisLib::GetSlotAppDataLength(TYPE_BBM, nTxMsgID, FALSE, bBinDataFlag, pTxParam->nNumDataBits);
            if(nNumTxSlot != MSG_SLOT_OVER)
            {
                if(pCh->m_pVdlTxMgr->CheckTxCntOkForBinMsg(TYPE_BBM, nNumTxSlot))
                {
                    INFO_LOG("BBM-25] OK! nNumTxSlot : %d, pTxParam : 0x%x, %d, %d\r\n", 
                        nNumTxSlot, (DWORD)pTxParam, pTxParam->bReserveTx, pTxParam->bAllocSlot);

                    pCh->m_pVdlTxMgr->SetTxMsg25Info(pTxParam, uDestMMSI, nBBMMsgID, nTxMsgID, nTxSeqNum, nNumTxSlot, bBinDataFlag);
                    pCh->m_pVdlTxMgr->SetTxSlotIdBinMsg(TYPE_BBM, nNumTxSlot);
                    return TRUE;
                }
                else
                {
                    WARNING_LOG("BBM-25] error, exceeds 5 slot, max 20 slot! txSlot:%d\r\n", nNumTxSlot);
                }
            }
            else
            {
                WARNING_LOG("BBM-25] error, needs too many slots! %d, m:%d\r\n", nNumTxSlot, nTxMsgID);
            }
        }
    }
    else
    {
        WARNING_LOG("BBM-25] Error, too large data! %d, m:%d\r\n", pTxParam->nNumDataBits, nTxMsgID);
    }

    pCh->m_pVdlTxMgr->ClearMsg25Buff(pTxParam);
    return FALSE;
}

/**
 * @brief Process the BBM(26) command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
BOOL CPI::ProcessBBMSub_26(int nNumTotalSentence, int nSentenceID, int nTxSeqNum, UINT8 uChID, int nBBMMsgID, int nNumFillBits, char *pstrEncData, int nNumDataBits)
{
    int      nNumTxSlot;
    TDMA_UNSCHE_MSG26* pTxParam;
    BYTE bBinDataFlag = BINARY_UNSTRUCTURED;

    UINT uDestMMSI = AIS_AB_MMSI_NULL;
    CChannelMgr *pCh = CLayerNetwork::getInst()->GetChPtr(uChID);

    int nTxMsgID = AIS_MSG_NO_ID_26;
    switch(nBBMMsgID)
    {
    case AIS_MSG_NO_ID_26:                    // Multiple slot binary message 26 with communication state (Binary data coded using the 16-bit application identifier)
        bBinDataFlag = BINARY_ASM;
        break;
    case PI_MSGID_71:                        // Multiple slot binary message 26 with communication state (unstructured binary data)
        bBinDataFlag = BINARY_UNSTRUCTURED;
        break;
    }

    DEBUG_LOG("BBM-26] stTotalNum : %d, stNum : %d, seqMsgID : %d, dest : %d, ch : %d, msg : %d, fill : %d, len : %d\r\n",
        nNumTotalSentence, nSentenceID , nTxSeqNum, uDestMMSI, uChID, nTxMsgID, nNumFillBits, nNumDataBits);

    if(!(pTxParam = pCh->m_pVdlTxMgr->GetMsg26BuffPtr(uDestMMSI, AIS_MSG_NO_ID_26, nTxSeqNum)))
        return FALSE;

    if(nSentenceID == 1) // 1st BBM
    {
        pCh->m_pVdlTxMgr->ClearMsg26Buff(pTxParam);
        pTxParam->nTxMsgID    = nTxMsgID;
        pTxParam->nTxSeqNum    = nTxSeqNum;
        pTxParam->uDestMMSI    = uDestMMSI;

        strcpy(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits = nNumDataBits;

        INFO_LOG("BBM-26] first BBM Sentence \r\n");
    }
    else    // 2st BBM ~ last BBM
    {
        pTxParam->nTxMsgID = nTxMsgID;
        pTxParam->nTxSeqNum= nTxSeqNum;

        strcat(pTxParam->pstrEncAisciiData, pstrEncData);
        pTxParam->nNumDataBits += nNumDataBits;

        INFO_LOG("BBM-26] Multi BBM Sentence \r\n");
    }

    if(nSentenceID != nNumTotalSentence)
    {
        return TRUE;
    }

    // Broadcast Max Data Check
    if(pTxParam->nNumDataBits <= 1000)            // ITU-R 1371-5 Table 83
    {
        if(!CAisLib::ConvertASCIICodeTo6BitBinary(pTxParam->pMsgBinData, pTxParam->pstrEncAisciiData))
        {
            WARNING_LOG("BBM-26] Error, Invalid BBM(25) Char! \r\n");
        }
        else
        {
            nNumTxSlot = CAisLib::GetSlotAppDataLength(TYPE_BBM, nTxMsgID, FALSE, bBinDataFlag, pTxParam->nNumDataBits);
            if(nNumTxSlot != MSG_SLOT_OVER)
            {
                if(pCh->m_pVdlTxMgr->CheckTxCntOkForBinMsg(TYPE_BBM, nNumTxSlot))
                {
                    pCh->m_pVdlTxMgr->SetTxMsg26Info(pTxParam, uDestMMSI, nBBMMsgID, nTxMsgID, nTxSeqNum, nNumTxSlot, bBinDataFlag);
                    pCh->m_pVdlTxMgr->SetTxSlotIdBinMsg(TYPE_BBM, nNumTxSlot);

                    INFO_LOG("BBM-26] OK! bits:%d, nNumTxSlot : %d, pTxParam : 0x%x, %d, %d\r\n",
                            pTxParam->nNumDataBits, nNumTxSlot, (DWORD)pTxParam, pTxParam->bReserveTx, pTxParam->bAllocSlot);

                    return TRUE;
                }
                else
                {
                    WARNING_LOG("BBM-26] error, exceeds 5 slot, max 20 slot! txSlot:%d\r\n", nNumTxSlot);
                }
            }
            else
            {
                WARNING_LOG("BBM-26] error, needs too many slots! %d, m:%d\r\n", nNumTxSlot, nTxMsgID);
            }
        }
    }
    else
    {
        WARNING_LOG("BBM-26] Wrong-1, too large message %d\r\n", pTxParam->nNumDataBits);
    }

    pCh->m_pVdlTxMgr->ClearMsg26Buff(pTxParam);
    return FALSE;
}

/**
 * @brief Process the AIR command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
int CPI::ProcessAIR(char *pstrCmd)
{
    //------------------------------------------------------------------------------------------
    // AIR : AIS interrogation request, 메시지 10 및 15 송신 명령
    //------------------------------------------------------------------------------------------
    // Refer to IEC-61162-1 "AIR"
    // This sentence supports ITU-R M.1371 Message 10 and 15. It provides an external
    // application with the means to initiate requests for specific ITU-R M.1371 messages, from
    // distant mobile or base station, AIS units. A single sentence can be used to request up to
    // two messages from one AIS unit and one message from a second AIS unit, or up to three
    // messages from one AIS unit. The message types that can be requested are limited. The
    // complete list of messages that may be requested can be found within the Message 15
    // description in ITU-R M.1371. Improper requests may be ignored. With Message 10 always
    // Message 11 is requested.
    // If the requested message type is 11 then a Message 10 is transmitted to only one station.
    // The fields of station 2 should be null fields in this case.
    // 메시지 ID 필드 값이 11 이면 오직 station-1 에 메시지 10 을 송신할것!
    // 이 경우 station-2 필드는 NULL 이어야한다!
    //------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 6.11.2 Communication test
    // It shall also be possible to initiate the communication test via the presentation interface
    // input using an AIR sentence (indicating Message 11 in the "message ID" field). In response to
    // the AIR sentence the AIS unit shall transmit a Message 10.
    //------------------------------------------------------------------------------------------
    // 송신채널 : first MMSI 로 부터 마지막으로 수신된 채널로 송신할것!
    //------------------------------------------------------------------------------------------


    UINT8 uTxCh = AIS_CHANNEL_NONE;
    CChannelMgr* pCh = NULL;

    if(CVdlTxMgr::IsTxAvailable())
    {
        if (!CAir::Parse(pstrCmd))
        {
            return false;
        }

        DEBUG_LOG("AIR] type : %d, MMSI_1 : %09d, MSG_1_1 : %d, MSG_1_2 : %d, MMSI_2 : %d, MSG_2_1 : %d\r\n",
            CAir::GetMsgType(), 
            CAir::GetMMSI1(), 
            CAir::GetMsgReq1_1(), 
            CAir::GetMsgReq1_2(), 
            CAir::GetMMSI2(), 
            CAir::GetMsgReq2());

        //-------------------------------------------------------------------
        // 송신채널 : first MMSI 로 부터 마지막으로 수신된 채널로 송신할것!
        //-------------------------------------------------------------------
        if((uTxCh = CUserDirMgr::getInst()->GetLastRcvCh(CAir::GetMMSI1())) == AIS_CHANNEL_NONE)
            uTxCh = AIS_CHANNEL_AIS1;

        pCh = CLayerNetwork::getInst()->GetChPtr(uTxCh);
        if(!pCh)
        {
            WARNING_LOG("AIR]] Can't get CH! %d, %d, %09d\r\n", uTxCh, CUserDirMgr::getInst()->GetLastRcvCh(CAir::GetMMSI1()), CAir::GetMMSI1());
            return FALSE;
        }

        if(CAir::GetMsgReq1_1() == AIS_MSG_NO_ID_11)
        {
            //-------------------------------------------------------------------------------------------
            // Refer to IEC-61162-1 "AIR"
            // If the requested message type is 11 then a Message 10 is transmitted to only one station.
            // The fields of station 2 should be null fields in this case.
            //-------------------------------------------------------------------------------------------
            const int nMsgID = AIS_MSG_NO_ID_10;
            if(CUserDirMgr::getInst()->IsStationTypeMobile(CAir::GetMMSI1()))
            {
                TDMA_ONESLOT_ADDRMSG *pTxData = pCh->m_pVdlTxMgr->GetOneSlotAddrTxMsgEmptyBuff(nMsgID);
                if(pTxData)
                {
                    pCh->m_pVdlTxMgr->SetOneSlotAddrTxMsgInfo(pTxData, CAir::GetMMSI1(), AIS_MSG_NO_ID_11, nMsgID);
                    DEBUG_LOG("AIR-SetBuf] MSG-10, 0x%x, mmsi : %09d, msg : %d\r\n",
                        pTxData, pTxData->uDestMMSI, pTxData->nTxMsgID);
                    return TRUE;
                }
                else
                {
                    WARNING_LOG("AIR] MSG-10, Can't get buffer!\r\n");
                }
            }
            else
            {
                WARNING_LOG("AIR] MSG-10, Fail, mmsi : %09d, msg : %d, stType: %d\r\n",
                    CAir::GetMMSI1(), nMsgID, CUserDirMgr::getInst()->GetInterrogatedStType(CAir::GetMMSI1()));
            }

            SendABKtoPI(CAir::GetMMSI1(), AIS_CHANNEL_NONE, AIS_MSG_NO_ID_11, 0, ABK_TX_FAIL);
            return FALSE;
        }
        else if(CheckInterrRequestInfo(CAir::GetMsgType(), CAir::GetMMSI1(), CAir::GetMsgReq1_1(), CAir::GetMsgReq1_2(), CAir::GetMMSI2(), CAir::GetMsgReq2()))
        {
            if(!CVdlTxMgr::CheckTxCntOkForMsg15())
            {
                //-------------------------------------------------------------------------------------
                // IEC-61993-2 7.3.4.1
                // For Message 15 own transmissions shall not exceed a total of 5 messages in a frame.
                // If either case is exceeded, the AIS shall generate an ABK warning sentence.
                //-------------------------------------------------------------------------------------
                SendABKtoPI(CAir::GetMMSI1(), AIS_CHANNEL_NONE, AIS_MSG_NO_ID_15, 0, ABK_TX_FAIL);

                WARNING_LOG("AIR] MSG-15, TxCnt above 5 times, send NACK, %d\r\n", cTimerSys::getInst()->GetCurTimerSec());
                return TRUE;
            }

            TDMA_UNSCHE15_INFO *pTxData = pCh->m_pVdlTxMgr->GetEmptyMsg15BuffPtr();
            if(pTxData)
            {
#ifdef __ENABLE_CHECK_MSG15_CNT__
                CVdlTxMgr::IncMsg15TxCnt();
#else
                CVdlTxMgr::SetTxSlotIdMsg15(OPSTATUS::nCurFrameMapSlotID);
#endif

                pCh->m_pVdlTxMgr->SetUnScheTxMsg15Info( pTxData, 
                                                        CAir::GetMsgType(), 
                                                        CAir::GetMMSI1(), 
                                                        CAir::GetMsgReq1_1(), 
                                                        CAir::GetMsgReq1_2(), 
                                                        CAir::GetMMSI2(), 
                                                        CAir::GetMsgReq2());

                DEBUG_LOG("AIR] MSG-15, 0x%x, mmsi_1 : %09d, msg_1_1 : %d, msg_1_2 : %d, mmsi_2 : %d, msg_2_1 : %d\r\n",
                    pTxData, CAir::GetMMSI1(), CAir::GetMsgReq1_1(), CAir::GetMsgReq1_2(), 
                    CAir::GetMMSI2(), CAir::GetMsgReq2());
                return TRUE;
            }
            else
            {
                WARNING_LOG("AIR] MSG-15, Can't get buffer!\r\n");
            }
        }
        else
        {
            WARNING_LOG("AIR] ERROR, wrong interrogation!\r\n");
        }
    }

    //[IEC 61993-2, Ed.3, clause ********.2(c)]
    //[Rec. ITU-R M.1371-5, Ann.8, clause 3.13]
    //Check that EUT DOES NOT transmit the interrogation message
    //(Message 15) addressed to AIS Class B SO (interrogation for Message 19).
    SendABKtoPI(CAir::GetMMSI1(), AIS_CHANNEL_NONE, AIS_MSG_NO_ID_15, 0, ABK_TX_FAIL);

    return FALSE;
}

/**
 * @brief Process the ACK command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
int CPI::ProcessACK(char *pstrCmd)
{
    char  pstrSubData1[32];
    int   pstrSubData1_len;

    if (CAck::Parse(pstrCmd))
    {
        CAlarmMgr::getInst()->SetAlarmAcked(CAck::GetAlarmNum());
    }
    else
    {
        WARNING_LOG("ACK] Parse failed\r\n");
        return 0;
    }

    return 1;
}

/**
 * @brief Process the ACN command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
int CPI::ProcessACN(char *pstrCmd)
{
    if (CAcn::Parse(pstrCmd))
    {
        WARNING_LOG("ACN] Parse failed\r\n");
        return FALSE;
    }

    char cAlertCommand = CAcn::GetAlertCommand();
    int nAlertID = CAcn::GetAlertID();
    int nAlertInstance = CAcn::GetAlertInstance();
    int nAlertManufacturer = CAcn::GetAlertManufacturer();
    CAlarmMgr::getInst()->PrecessACNCommand(cAlertCommand, nAlertID, nAlertInstance, nAlertManufacturer, this);
    return TRUE;
}

/**
 * @brief Process the HBT command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessHBT(char *pstrCmd)
{
    //-------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.11.1 Minimum keyboard and display
    //-------------------------------------------------------------------------------------------
    // The DTE flag (refer to Recommendation ITU-R M.1371-4/A8-3.3) shall only be set to "1" when
    // there is no means of displaying received text messages. External equipment indicates the
    // availability of a remote MKD functionality by a HBT sentence sent every 30 s. If an SSD
    // sentence is applied the DTE field should be evaluated together with the HBT sentence to
    // define if the external equipment is able to display text messages.
    //-------------------------------------------------------------------------------------------

    m_xHbt.Parse(pstrCmd);
}

/**
 * @brief Check the HBT timeout
 * @return TRUE if timeout, FALSE otherwise
 */
BOOL CPI::CheckTimeoutHBT()
{
    return m_xHbt.IsTimeout();
}

//----------------------------------------------------------------------------------------
// - TP가 ON상태에서 MKD(PC플로터)의 전원 ON하면, MKD에서 AIQ를 전송하고, TP는 ACA/ACS를 전송.
//----------------------------------------------------------------------------------------
void CPI::ProcessAIQ(char *pstrCmd)
{
    //---------------------------------------------------------
    // AIQ : Query of the other messages from AIS
    // $--AIQ,xxx
    // xxx : queried sentence like ACA, SSD, VSD, TRL, VER, TXT
    //---------------------------------------------------------

    char pstrSubData1[16];
    char pstrSubData2[16];
    char pstrSubData3[16];

    CSentence::GetFieldString(pstrCmd, 1, pstrSubData1, sizeof(pstrSubData1));

    if(!strncmp((char*)pstrSubData1, "ACA", 3))
    {
        // IEC 61993-2 6.10.3.3
        CPI::SendAllACAACStoPI();
    }
    else if(!strncmp((char*)pstrSubData1, "ALR", 3))
    {
        CSentence::GetFieldString(pstrCmd, 2, pstrSubData2, sizeof(pstrSubData2));
        CSentence::GetFieldString(pstrCmd, 3, pstrSubData3, sizeof(pstrSubData3));

        SendINLALRToPI(atoi((char*)pstrSubData2), atoi((char*)pstrSubData3));
    }
    else if(!strncmp((char*)pstrSubData1, "EPV", 3))
    {
        SendAllEPVtoPI();
    }
    else if(!strncmp((char*)pstrSubData1, "SSD", 3))
    {
        // $AIAIQ,SSD
        SendSSDToPI(STR_TALKER_AI);
    }
    else if(!strncmp((char*)pstrSubData1, "TRL", 3))
    {
        SendTRLtoPI();
    }
    else if(!strncmp((char*)pstrSubData1, "TXT", 3))
    {
        SendAllTXTtoPI(FALSE);
        return;
    }
    else if(!strncmp((char*)pstrSubData1, "VER", 3))
    {
        SendVERToPI();
    }
    else if(!strncmp((char*)pstrSubData1, "VSD", 3))
    {
        SendVSDToPI(STR_TALKER_AI);
    }
}

/**
 * @brief Process the ACA command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessACA(char *pstrCmd)
{
    CAca::Parse(pstrCmd);
}

BOOL CPI::IsPropProtectedEPV(int nProperty)
{
    return !(EPV_PROP_SHOW_TESTING_SART <= nProperty && nProperty <= EPV_PROP_LOCATING_DEVICE_ALERT)
            && !(EPV_PROP_ANTENNA_EA <= nProperty && nProperty <= EPV_PROP_ANTENNA_ED);
}

/**
 * @brief Process the EPV command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessEPV(char *pstrCmd)
{
    //-------------------------------------------------------------------
    // EPV became mandatory from IEC 61993-2 ed3(2018-07)
    //-------------------------------------------------------------------
    // IEC 61993-2 ed3(2018-07) 7.6.3.6 Configuration using EPV sentence
    // IEC 61993-2 ed3(2018-07) 19.9.4
    // IEC 61993-2 ed3(2018-07) 19.9.5
    // IEC 61993-2 ed3(2018-07) Annex F
    // IEC-61993-2 (2012) Annex E E.2
    //-------------------------------------------------------------------
    char pstrTalkerID[LEN_NMEA_TALKER];
    char pstrSentID[LEN_NMEA_SENT];
    char pstrResp[RX_MAX_DATA_SIZE];
    bool bCheckPwd  = false;
    bool bPwdPassed = true;
    bool bSSAMatch  = true;
    bool bProcOK    = false;

    CSentence::GetTalkerSenderID((char*)pstrCmd, pstrTalkerID, pstrSentID);

    if (!CEpv::Parse(pstrCmd))
    {
        WARNING_LOG("EPV] Parse failed\r\n");
        SendNAK((char*)"EPV", NAK_REASON_WRONG_DATAFIELD, pstrTalkerID);
        return;
    }

    if (CSetupMgr::getInst()->GetPilotPortRestricted() && CEpv::GetPropertyID() != EPV_PROP_PILOT_PORT_RESTRICTED)
    {
        WARNING_LOG("EPV] Access denied! Pilot port restricted mode enabled: %d, property: %d, checkPwd: %d, pwdMatch: %d\r\n",
                CSetupMgr::getInst()->GetPilotPortRestricted(), CEpv::GetPropertyID(), bCheckPwd, bPwdPassed);
        SendNAK((char*)"EPV", NAK_REASON_ACCESS_DENIED);
        return;
    }

    bCheckPwd = (m_nHighSpdPortID != HIGHSPD_PORTID_MKD) && IsPropProtectedEPV(CEpv::GetPropertyID());
    if (bCheckPwd)
    {
        bPwdPassed = (m_xSpw.GetCode() == SPW_EPV);
        if (!bPwdPassed)
        {
            bSSAMatch = FALSE;
            if (m_xSsa.GetCode() == SSA_EPV)
                bSSAMatch = CheckMd5MatchSSA(pstrCmd, m_xSsa.GetAuthenticationPtr());

            bPwdPassed = bSSAMatch;

            if (!bPwdPassed)
            {
                SendNAK((char*)"EPV", NAK_REASON_ACCESS_DENIED);
                return;
            }
        }
    }

    uint8_t nPropertyID = CEpv::GetPropertyID();
    switch(nPropertyID)
    {
    // Sensor 1 baudrate
    case EPV_PROP_SENSOR1_BAUDRATE:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            DWORD dwOrgSetup = CSetupMgr::getInst()->GetPortSpeedBitmap();
            if (CSetupMgr::getInst()->SetBaudrateSensor1(nPropertyData))
            {
                bProcOK = true;
                if (dwOrgSetup != CSetupMgr::getInst()->GetPortSpeedBitmap())
                {                
                    // Set uart baudrate for Sensor 1/2/3
                    CSensorMgr::getInst()->SetSensorUartBaudrate(CSetupMgr::getInst()->GetPortSpeedBitmap());
                    // Send baudrate to MKD
                    CMKD::getInst()->SendSetupUartPortSpeed();
                    // Save system setup data
                    CSetupMgr::getInst()->ReserveToSaveSysConfigData();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid Sensor 1 baudrate : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Sensor 2 baudrate
    case EPV_PROP_SENSOR2_BAUDRATE:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            DWORD dwOrgSetup = CSetupMgr::getInst()->GetPortSpeedBitmap();
            if (CSetupMgr::getInst()->SetBaudrateSensor2(nPropertyData))
            {
                bProcOK = true;
                if (dwOrgSetup != CSetupMgr::getInst()->GetPortSpeedBitmap())
                {
                    // Set uart baudrate for Sensor 1/2/3
                    CSensorMgr::getInst()->SetSensorUartBaudrate(CSetupMgr::getInst()->GetPortSpeedBitmap());
                    // Send baudrate to MKD
                    CMKD::getInst()->SendSetupUartPortSpeed();
                    // Save system setup data
                    CSetupMgr::getInst()->ReserveToSaveSysConfigData();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid Sensor 2 baudrate : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Sensor 3 baudrate
    case EPV_PROP_SENSOR3_BAUDRATE:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            DWORD dwOrgSetup = CSetupMgr::getInst()->GetPortSpeedBitmap();
            if (CSetupMgr::getInst()->SetBaudrateSensor3(nPropertyData))
            {
                bProcOK = true;
                if (dwOrgSetup != CSetupMgr::getInst()->GetPortSpeedBitmap())
                {
                    // Set uart baudrate for Sensor 1/2/3
                    CSensorMgr::getInst()->SetSensorUartBaudrate(CSetupMgr::getInst()->GetPortSpeedBitmap());
                    // Send baudrate to MKD
                    CMKD::getInst()->SendSetupUartPortSpeed();
                    // Save system setup data
                    CSetupMgr::getInst()->ReserveToSaveSysConfigData();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid Sensor 3 baudrate : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Long-range baudrate
    case EPV_PROP_LR_BAUDRATE:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            DWORD dwOrgSetup = CSetupMgr::getInst()->GetPortSpeedBitmap();
            if (CSetupMgr::getInst()->SetBaudrateLongRange(nPropertyData))
            {
                bProcOK = true;
                if (dwOrgSetup != CSetupMgr::getInst()->GetPortSpeedBitmap())
                {
                    // Set uart baudrate for Long Range
                    CLongRange::getInst()->SetUartBaudIdx(GET_BAUD_IDX_LR(CSetupMgr::getInst()->GetPortSpeedBitmap()));
                    // Send baudrate to MKD
                    CMKD::getInst()->SendSetupUartPortSpeed();
                    // Save system setup data
                    CSetupMgr::getInst()->ReserveToSaveSysConfigData();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid LR baudrate : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // DGNSS baudrate
    case EPV_PROP_DGNSS_BAUDRATE:
        // DGNSS baud, it's not supported!
        SendNAK((char*)"EPV", NAK_REASON_CANNOT_PERFORM);
        return;

    // Change MMSI number
    case EPV_PROP_MMSI:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            if (CAisLib::IsValidMMSI(nPropertyData))
            {
                bProcOK = true;
                if (nPropertyData != cShip::getOwnShipInst()->xStaticData.dMMSI)
                {
                    // Set MMSI
                    CLayerNetwork::getInst()->ChangeMMSI(nPropertyData);
                    // Request to send static voyage data when static data changed
                    CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();
                    // Set operation phase to monitor VDL when MMSI changed
                    CLayerNetwork::getInst()->SetOpPhase(OPPHASE_MONITOR_VDL);
                    // Save system setup data
                    CSetupMgr::getInst()->ReserveToSaveSysConfigData();
                }
            }
            else
            {
                WARNING_LOG("EPV] Invalid MMSI: %d\r\n", nPropertyData);
                SendNAK((char*)"EPV", NAK_REASON_WRONG_DATAFIELD);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Change IMO number
    case EPV_PROP_IMO:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            uint32_t nPropertyData = CEpv::GetPropertyInt();
            if (CAisLib::IsValidImoNum(nPropertyData))
            {
                bProcOK = true;
                if (nPropertyData != cShip::getOwnShipInst()->xStaticData.dwImoID)
                {
                    // Set IMO
                    cShip::getOwnShipInst()->xStaticData.dwImoID = nPropertyData;
                    // Request to send static voyage data when static data changed
                    CLayerNetwork::getInst()->ProcStaticVoyageDataChanged();
                    // Save system setup data
                    CSetupMgr::getInst()->ReserveToSaveSysConfigData();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid IMO ID : %d\r\n", m_nHighSpdPortID, nPropertyData);
                SendNAK((char*)"EPV", NAK_REASON_WRONG_DATAFIELD);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Long-range interface configuration
    case EPV_PROP_LR_CONFIG:    
        // Long-range interface configuration, "A" : Automatic, "M" : Manual
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            char *pstrPropertyData = CEpv::GetPropertyStr();
            BOOL bOrgAutoReplay = CSetupMgr::getInst()->GetLongRangeAutoReply();
            UINT dwOrgSetup = CSetupMgr::getInst()->GetLongRangeCfg();

            if (pstrPropertyData[0] == 'A' || pstrPropertyData[0] == 'M')
            {
                bProcOK = true;
                if (pstrPropertyData[0] == 'A')
                {
                    // Set long range auto reply
                    CSetupMgr::getInst()->SetLongRangeAutoReply(true); 
                    // Set long range mode
                    CSetupMgr::getInst()->SetLongRangeCfg(CSetupMgr::getInst()->GetLongRangeCfg() | LR_MODE);
                }
                else if (pstrPropertyData[0] == 'M')
                {
                    // Set long range manual reply
                    CSetupMgr::getInst()->SetLongRangeAutoReply(false);
                    // Set long range mode
                    CSetupMgr::getInst()->SetLongRangeCfg(CSetupMgr::getInst()->GetLongRangeCfg() & ~LR_MODE);
                }

                if (bOrgAutoReplay != CSetupMgr::getInst()->GetLongRangeAutoReply() 
                    || dwOrgSetup != CSetupMgr::getInst()->GetLongRangeCfg())
                {
                    // Set long range config
                    CLongRange::getInst()->SetLongRangeConfig(CSetupMgr::getInst()->GetLongRangeAutoReply(), CSetupMgr::getInst()->GetLongRangeCfg());
                    // Send long range config to MKD
                    CMKD::getInst()->SendSetupLongRange();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid LR config : %s\r\n", m_nHighSpdPortID, pstrPropertyData);
                SendNAK((char*)"EPV", NAK_REASON_WRONG_DATAFIELD);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    case EPV_PROP_LR_CH1:    // Long-range AIS broadcast channel 1, Valid channel according ITU-R M.1084-5. See 8.3.    Default value 0 indicates no transmission of message 27
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData == AIS_DEFAULT_TXLR_CH_1 || nPropertyData == 0)
            {
                bProcOK = true;
                if (CSetupMgr::getInst()->GetLongRangeCh1() != nPropertyData)
                {
                    // Set long range CH1
                    CSetupMgr::getInst()->ChangeLongRangeCH1(nPropertyData);
                    // Send long range CH1 to MKD
                    CMKD::getInst()->SendSetupLongRangeCH1();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid LR CH1 : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    case EPV_PROP_LR_CH2:    // Long-range AIS broadcast channel 2, Valid channel according ITU-R M.1084-5. See 8.3. Default value 0 indicates no transmission of message 27
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int32_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData == AIS_DEFAULT_TXLR_CH_2 || nPropertyData == 0)
            {
                bProcOK = true;
                if(CSetupMgr::getInst()->GetLongRangeCh2() != nPropertyData)
                {
                    // Set long range CH2
                    CSetupMgr::getInst()->ChangeLongRangeCH2(nPropertyData);
                    // Send long range CH2 to MKD
                    CMKD::getInst()->SendSetupLongRangeCH2();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid LR CH2 : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Change administrator password, New administrator password
    case EPV_PROP_CHANGE_ADMIN_PWD:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            char *pstrPropertyData = CEpv::GetPropertyStr();
            int nLen = strlen(pstrPropertyData);
            if (nLen <= MAX_PASSWORD_LEN)
            {
                bProcOK = true;
                // Set administrator password
                CSetupMgr::getInst()->SetAddminPassword(pstrPropertyData, nLen);
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid admin password : %s\r\n", m_nHighSpdPortID, pstrPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Change user password, New user password
    case EPV_PROP_CHANGE_USER_PWD:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            char *pstrPropertyData = CEpv::GetPropertyStr();
            int nLen = strlen(pstrPropertyData);
            if (nLen <= MAX_PASSWORD_LEN)
            {
                bProcOK = true;
                // Set user password
                CSetupMgr::getInst()->SetUserPassword(pstrPropertyData, nLen);
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid user password : %s\r\n", m_nHighSpdPortID, pstrPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // AIS-SART test mode
    case EPV_PROP_SHOW_TESTING_SART:
        // AIS-SART test mode 
        // 0 = normal mode, 1 = display and output AIS-SART in test mode
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            uint8_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData == 0 || nPropertyData == 1)
            {
                bProcOK = true;
                if (CSetupMgr::getInst()->GetShowTestingSART() != nPropertyData)
                {
                    // Set show test SART
                    CSetupMgr::getInst()->SetShowTestingSART(nPropertyData);
                    // Send show test SART to MKD
                    CMKD::getInst()->SendSetupShowTestingSART();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid show testing SART : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // AIS silent mode
    case EPV_PROP_SILENT_MODE:
        // AIS silent mode
        // 0 = AIS in normal transmit operation, 1 = AIS in receive only operation
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            uint8_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData == 0 || nPropertyData == 1)
            {
                bProcOK = true;
                if (CSetupMgr::getInst()->GetEnableSilentMode() != nPropertyData)
                {
                    // Set silent mode
                    CSetupMgr::getInst()->SetEnableSilentMode(nPropertyData);
                    // Send silent mode to MKD
                    CMKD::getInst()->SendSetupEnableSilentMode();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid silent mode : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Activation of locating device alert
    case EPV_PROP_LOCATING_DEVICE_ALERT:
        // Activation of locating device alert
        // 0 = No alert from locating devices, 1 = Alert for received devices is generated(default)
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            uint8_t nPropertyData = CEpv::GetPropertyInt();
            if(nPropertyData == 0 || nPropertyData == 1)
            {
                bProcOK = true;
                if(CSetupMgr::getInst()->GetEnableAlert14() != nPropertyData)
                {
                    // Set ALR 14 enable/disable
                    CSetupMgr::getInst()->SetEnableAlr14(nPropertyData);
                    // Send ALR 14 enable/disable to MKD
                    CMKD::getInst()->SendSetupEnableALR14();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid ALR 14 : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Sensor alert configuration
    case EPV_PROP_SENSOR_ALERT:
        //--------------------------------------------
        // Sensor alert configuration
        //--------------------------------------------
        // Format: Hexadecimal representing bits.
        // Bit value 0 means alert function deselected, 1 means alert function enabled.
        // Usage of bits:
        // bit 2: external position
        // bit 1: heading
        // bit 0 (lsb): rate of turn
        // Default value 7: all alerts enabled.
        //--------------------------------------------
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            uint8_t nPropertyData = CEpv::GetPropertyInt();
            if(0 <= nPropertyData && nPropertyData <= 7)
            {
                bProcOK = true;
                bool bEnable = nPropertyData & 0x01;
                if(CSetupMgr::getInst()->GetEnableExtROT() != bEnable)
                {
                    // Set enable/disable ROT
                    CSetupMgr::getInst()->SetEnableExtROT(bEnable);
                    // Send enable/disable ROT to MKD
                    CMKD::getInst()->SendSetupEnableROT();
                }

                bEnable = (nPropertyData & 0x02) >> 1;
                if(CSetupMgr::getInst()->GetEnableExtHeading() != bEnable)
                {
                    // Set enable/disable heading
                    CSetupMgr::getInst()->SetEnableExtHeading(bEnable);
                    // Send enable/disable heading to MKD
                    CMKD::getInst()->SendSetupEnableHeading();
                }

                bEnable = (nPropertyData & 0x04) >> 2;
                if(CSetupMgr::getInst()->GetEnableExtEPFS() != bEnable)
                {
                    // Set enable/disable external EPFS
                    CSetupMgr::getInst()->SetEnableExtEPFS(bEnable);
                    // Reset alarm check status
                    gpAlarmExtEpfsLost->ResetAlarmCheckStat();
                    // Send enable/disable external EPFS to MKD
                    CMKD::getInst()->SendSetupEnableExtEPFS();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid sensor alert : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Pilot Port access level
    case EPV_PROP_PILOT_PORT_RESTRICTED:
        //----------------------------------------------------------------------------------------------------------------------------------------
        // Pilot Port access level
        //----------------------------------------------------------------------------------------------------------------------------------------
        // 0 = normal access to PI port functionality (default)
        // 1 = restricted access to PI port functionality: no input allowed except EPV sentence with property value 117, preceded by SSA sentence
        //----------------------------------------------------------------------------------------------------------------------------------------
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            uint8_t nPropertyData = CEpv::GetPropertyInt();
            if(nPropertyData == 0 || nPropertyData == 1)
            {
                bProcOK = true;
                if(CSetupMgr::getInst()->GetPilotPortRestricted() != nPropertyData)
                {
                    // Set pilot port restricted
                    CSetupMgr::getInst()->SetPilotPortRestricted(nPropertyData);
                    // Send pilot port restricted to MKD
                    CMKD::getInst()->SendSetupPilotPortAccessLevel();
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid pilot port restricted : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Extended dimensions configuration
    case EPV_PROP_ANTENNA_EA:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int16_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData >= 0 && nPropertyData <= AIS_GNSS_ANT_POS_A_MAX)
            {
                // If internal/external Antenna Position + Extened Antenna Position > 511,
                // Send Nak command to PI
                if (CSetupMgr::getInst()->GetIntAntennaPosA() + nPropertyData <= AIS_GNSS_ANT_POS_A_MAX
                    && CSetupMgr::getInst()->GetExtAntennaPosA() + nPropertyData <= AIS_GNSS_ANT_POS_A_MAX)
                {
                    bProcOK = true;
                    if(CSetupMgr::getInst()->GetExtendAntennaPosA() != nPropertyData)
                    {
                        // Set extended antenna A
                        CSetupMgr::getInst()->SetExtendAntennaPosA(nPropertyData);
                        // Send extended antenna A to MKD
                        CMKD::getInst()->SendSetupExtDimInt();
                    }
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid antenna EA : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    case EPV_PROP_ANTENNA_EB:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int16_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData >= 0 && nPropertyData <= AIS_GNSS_ANT_POS_B_MAX)
            {
                // If internal/external Antenna Position + Extened Antenna Position > 511,
                // Send Nak command to PI
                if (CSetupMgr::getInst()->GetIntAntennaPosB() + nPropertyData <= AIS_GNSS_ANT_POS_B_MAX
                    && CSetupMgr::getInst()->GetExtAntennaPosB() + nPropertyData <= AIS_GNSS_ANT_POS_B_MAX)
                {
                    bProcOK = true;
                    if(CSetupMgr::getInst()->GetExtendAntennaPosB() != nPropertyData)
                    {
                        // Set extended antenna B
                        CSetupMgr::getInst()->SetExtendAntennaPosB(nPropertyData);
                        // Send extended antenna B to MKD
                        CMKD::getInst()->SendSetupExtDimInt();
                    }
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid antenna EB : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    case EPV_PROP_ANTENNA_EC:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int16_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData >= 0 && nPropertyData <= AIS_GNSS_ANT_POS_C_MAX)
            {
                // If internal/external Antenna Position + Extened Antenna Position > 63,
                // Send Nak command to PI
                if (CSetupMgr::getInst()->GetIntAntennaPosC() + nPropertyData <= AIS_GNSS_ANT_POS_C_MAX
                    && CSetupMgr::getInst()->GetExtAntennaPosC() + nPropertyData <= AIS_GNSS_ANT_POS_C_MAX)
                {
                    bProcOK = true;
                    if(CSetupMgr::getInst()->GetExtendAntennaPosC() != nPropertyData)
                    {
                        // Set extended antenna C
                        CSetupMgr::getInst()->SetExtendAntennaPosC(nPropertyData);
                        // Send extended antenna C to MKD
                        CMKD::getInst()->SendSetupExtDimInt();
                    }
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid antenna EC : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    case EPV_PROP_ANTENNA_ED:
        if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_CMD)
        {
            int16_t nPropertyData = CEpv::GetPropertyInt();
            if (nPropertyData >= 0 && nPropertyData <= AIS_GNSS_ANT_POS_D_MAX)
            {
                // If internal/external Antenna Position + Extened Antenna Position > 63,
                // Send Nak command to PI
                if (CSetupMgr::getInst()->GetIntAntennaPosD() + nPropertyData <= AIS_GNSS_ANT_POS_D_MAX
                    && CSetupMgr::getInst()->GetExtAntennaPosD() + nPropertyData <= AIS_GNSS_ANT_POS_D_MAX)
                {
                    bProcOK = true;
                    if(CSetupMgr::getInst()->GetExtendAntennaPosD() != nPropertyData)
                    {
                        // Set extended antenna D
                        CSetupMgr::getInst()->SetExtendAntennaPosD(nPropertyData);
                        // Send extended antenna D to MKD
                        CMKD::getInst()->SendSetupExtDimInt();
                    }
                }
            }
            else
            {
                WARNING_LOG("EPV-%d] ignore, invalid antenna ED : %d\r\n", m_nHighSpdPortID, nPropertyData);
            }
        }
        else if (CEpv::GetStatusFlag() == EPV_STAT_FLAG_REQ)
        {
            bProcOK = true;
        }
        break;

    // Identifiers for IEC 61162-450 interface configuration
    case EPV_PROP_IP_AND_NETMASK:
    case EPV_PROP_SFI:
    case EPV_PROP_MULTICAST_GROUP:
    case EPV_PROP_MULTICAST_GROUP2:
    case EPV_PROP_TRANSMISSION_GROUP:
    // Identifiers for SFI configuration
    case EPV_PROP_PRI_POSITION_SENSOR:
    case EPV_PROP_SEC_POSITION_SENSOR:
    case EPV_PROP_PRI_SOG_COG_SENSOR:
    case EPV_PROP_SEC_SOG_COG_SENSOR:
    case EPV_PROP_PRI_HDG_SENSOR:
    case EPV_PROP_SEC_HDG_SENSOR:
    case EPV_PROP_PRI_ROT_SENSOR:
    case EPV_PROP_SEC_ROT_SENSOR:
    case EPV_PROP_PRI_AIS_CONTROL:
    case EPV_PROP_SEC_AIS_CONTROL:
    case EPV_PROP_PRI_ALERT_SOURCE:
    case EPV_PROP_SEC_ALERT_SOURCE:
        SendNAK((char*)"EPV", NAK_REASON_CANNOT_PERFORM);
        return;

    default:
        break;
    }

    // if property value is not changed, send NAK command to PI
    // else, send response as property value changed
    if (!bProcOK)
    {
        SendNAK((char*)"EPV", NAK_REASON_WRONG_DATAFIELD);
        return;
    }
    else
    {
        memset(pstrResp, 0x00, sizeof(pstrResp));

        // Send response as property value changed
        int nLen = CEpv::MakeSentence(pstrResp, nPropertyID);
        if(nLen > 0)
        {
            SendOutData(pstrResp, nLen);
        }
    }
}

/**
 * @brief Process the intellian AIQ command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessINLAIQ(char *pstrCmd)
{
    char pstrSubID[8];
    CSentence::GetFieldString(pstrCmd, 2, pstrSubID, sizeof(pstrSubID));

    // Request time infomation to PI
    if (!strncmp(pstrSubID, "106", 3))   {SendOwnShipMMSI();    return;}
    if (!strncmp(pstrSubID, "107", 3))   {SendOwnShipIMO();     return;}
}

/**
 * @brief Process the Diagnostic mode command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessINLDIG(char *pstrData)
{
    //-----------------------------------------------------
    // PI(MKD)로 부터 Diagnostic mode request 명령 수신
    // $AIINL,DIG,RFT,x*hh<CR><LF>
    // x = 0 : Normal, 1 : 송신테스트, 2 : 수신테스트
    //-----------------------------------------------------

    char pstrSubData1[RX_MAX_DATA_SIZE];
    char pstrSubData2[RX_MAX_DATA_SIZE];

    CSentence::GetFieldString(pstrData, 2, pstrSubData1, sizeof(pstrSubData1));
    if(!strcmp(pstrSubData1, "RFT"))
    {
        TagTestMode nTestMode = (TagTestMode)CSentence::GetFieldInteger(pstrData, 3);
        SendRfTestModeMsgToPI((char*)"RFT", nTestMode);
        CTestModeMgr::getInst()->SetTestMode(nTestMode, this);
    }
}

/**
 * @brief Process the RF Test mode command from the PI
 * @param pstrData The sentence to be parsed
 * @return None
 */
void CPI::ProcessINLRFT(char *pstrData)
{
    WORD    wTmpData1 = 0;
    WORD    wTmpData2 = 0;
    char    pstrSubID[5];
    char    pstrParam[5];
    int     tm_cmd;

    if(!CTestModeMgr::getInst()->IsTestModeRunning())
        return;

    CSentence::GetFieldString(pstrData, 2, pstrSubID, sizeof(pstrSubID));
    if((tm_cmd = CAisLib::HexaStrToDecimal(pstrSubID, 3)) == -1)
        return;

    switch(tm_cmd)
    {
        // #AIINL,RFT,001*hh<CR><LF>
        case TM_GET_TX_PARAM:
            CTestModeMgr::getInst()->TMGetTxParam();
            break;

        // #AIINL,RFT,002*hh<CR><LF>
        case TM_GET_TX_POWER:
            CTestModeMgr::getInst()->TMGetTxPower();
            break;

        // #AIINL,RFT,003*hh<CR><LF>
        case TM_GET_TX_FREQ_OFFSET:
            CTestModeMgr::getInst()->TMGetFreqOffset();
            break;

        // #AIINL,RFT,004*hh<CR><LF>
        case TM_GET_TX_DC_OFFSET:
            CTestModeMgr::getInst()->TMGetDcOffset();
            break;

        // #AIINL,RFT,005*hh<CR><LF>
        case TM_GET_VSWRLIMIT:
            CTestModeMgr::getInst()->TMGetVswrLimit();
            break;

        // #AIINL,RFT,006*hh<CR><LF>
        case TM_GET_RX1:
            CTestModeMgr::getInst()->TMGetRxParam(AIS_CHANNEL_AIS1);
            break;

        // #AIINL,RFT,007*hh<CR><LF>
        case TM_GET_RX2:
            CTestModeMgr::getInst()->TMGetRxParam(AIS_CHANNEL_AIS2);
            break;

        // #AIINL,RFT,008*hh<CR><LF>
        case TM_GET_RX3:
            CTestModeMgr::getInst()->TMGetRxParam(AIS_CHANNEL_DSC);
            break;

        // #AIINL,RFT,011,xxxx,yyyy*hh<CR><LF>
        case TM_SET_TX_PARAM:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);

            CSentence::GetFieldString(pstrData, 4, pstrParam, sizeof(pstrParam));
            wTmpData2 = CAisLib::HexaStrToDecimal(pstrParam, 4);

            CTestModeMgr::getInst()->TMSetTxParam(wTmpData1, wTmpData2);
            break;

        // #AIINL,RFT,012,xxxx*hh<CR><LF>
        case TM_SET_TX_POWER:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CTestModeMgr::getInst()->TMSetTxPower(wTmpData1);
            break;

        // #AIINL,RFT,013,xxxx*hh<CR><LF>
        case TM_SET_TX_FREQ_OFFSET:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CTestModeMgr::getInst()->TMSetFreqOffset(wTmpData1);
            break;

        // #AIINL,RFT,014,xxxx*hh<CR><LF>
        case TM_SET_TX_DC_OFFSET:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);

            CSentence::GetFieldString(pstrData, 4, pstrParam, sizeof(pstrParam));
            wTmpData2 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CTestModeMgr::getInst()->TMSetDcOffset(wTmpData1, wTmpData2);
            break;

        // #AIINL,RFT,015,xxxx*hh<CR><LF>
        case TM_SET_VSWRLIMIT:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CTestModeMgr::getInst()->TMSetVswrLimit(wTmpData1);
            break;

        // #AIINL,RFT,016,xxxx*hh<CR><LF>
        case TM_SET_RX1:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CSentence::GetFieldString(pstrData, 4, pstrParam, sizeof(pstrParam));
            wTmpData2 = CAisLib::HexaStrToDecimal(pstrParam, 4);

            CTestModeMgr::getInst()->TMSetRxParam(AIS_CHANNEL_AIS1, wTmpData1);
            break;

        // #AIINL,RFT,017,xxxx*hh<CR><LF>
        case TM_SET_RX2:
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CSentence::GetFieldString(pstrData, 4, pstrParam, sizeof(pstrParam));
            wTmpData2 = CAisLib::HexaStrToDecimal(pstrParam, 4);

            CTestModeMgr::getInst()->TMSetRxParam(AIS_CHANNEL_AIS2, wTmpData1);
            break;

        // #AIINL,RFT,018,xxxx*hh<CR><LF>
        case TM_SET_RX3:    // DSC
            CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
            wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);
            CSentence::GetFieldString(pstrData, 4, pstrParam, sizeof(pstrParam));
            wTmpData2 = CAisLib::HexaStrToDecimal(pstrParam, 4);

            CTestModeMgr::getInst()->TMSetRxParam(AIS_CHANNEL_DSC, wTmpData1);
            break;

        //---------------------------------
        // Start TX/RX Test
        //--------------------------------
        //#AIINL,RFT,C01,xxxx*hh<CR><LF>
        //#AIINL,RFT,C01*hh<CR><LF>
        case TM_MODE_START:
            if (CTestModeMgr::getInst()->IsTransmitTestRunning())
            {
                CSentence::GetFieldString(pstrData, 3, pstrParam, sizeof(pstrParam));
                wTmpData1 = CAisLib::HexaStrToDecimal(pstrParam, 4);

                CTestModeMgr::getInst()->TMSetTranmitterTest(wTmpData1);
            }
            else if (CTestModeMgr::getInst()->IsReceiveTestRunning())
            {
                CTestModeMgr::getInst()->TMReceiveTestStart();
            }
            else if (CTestModeMgr::getInst()->IsBuiltInTestRunning())
            {
                CTestModeMgr::getInst()->RunBITE(FALSE);
            }
            break;

        //#AIINL,RFT,C02*hh<CR><LF>
        case TM_MODE_CANCLE:
            CTestModeMgr::getInst()->TMTransmitterTestStop();
            CTestModeMgr::getInst()->TMReceiveTestStop();
            //CTestModeMgr::getInst()->FinishBITE();
            break;

        default:
            break;
    }
}

/**
 * @brief Process Intellian Sentence
 * @param pstrCmd The sentence to be parsed
 * @return None
 */
void CPI::ProcessINL(char *pstrCmd)
{
    char pstrSubID[8];
    CSentence::GetFieldString(pstrCmd, 1, pstrSubID, sizeof(pstrSubID));

    if (!strncmp(pstrSubID, "TIM", 3))   {SendSysDateTime();        return;}

    // Request time infomation to PI
    if (!strncmp(pstrSubID, "AIQ", 3))   {SendOwnShipMMSI();        return;}
    // Request Diagnostic mode to PI
    if (!strncmp(pstrSubID, "DIG", 3))   {ProcessINLDIG(pstrCmd);   return;}
    // Request RF Test mode to PI
    if (!strncmp(pstrSubID, "RFT", 3))   {ProcessINLRFT(pstrCmd);   return;}
}

int CPI::GetLoopBackPortID()
{
    switch(m_nHighSpdPortID)
    {
    case HIGHSPD_PORTID_1:
        return LOOPBACK_PORT_ID_PILOT;
    case HIGHSPD_PORTID_2:
        return LOOPBACK_PORT_ID_EXTDISP;
    }
    return LOOPBACK_PORT_ID_NA;
}

/**
 * @brief Process the loopback response
 * @param pstrCmd The sentence to be parsed
 * @return None
 */
void CPI::ProcRunComLoopBackResp(char *pstrCmd)
{
    //-----------------------------------------
    // $AIINL,LBP,%d
    //-----------------------------------------
    //  %d : Loop-back test를 실행할 타겟 포트
    //         0 : Sensor1
    //         1 : Sensor2
    //         2 : Sensor3
    //         3 : Long - Range Port
    //         4 : External display Port
    //         5 : Pilot Port
    //-----------------------------------------
    int  nPortID;

    nPortID = CSentence::GetFieldInteger(pstrCmd, 2);
    if(nPortID == GetLoopBackPortID())
    {
        CMKD::getInst()->SendOutStr(pstrCmd);                // send it back
    }
    else
    {
        WARNING_LOG("PI-%d] loopback ID mismatch : %d, ", m_nHighSpdPortID, nPortID);
    }
}

/**
 * @brief Process the data from the PI
 * @param pUartDbgP The UART port to read the data
 * @return None
 */
void CPI::ProcessData(cUart *pUartDbgP)
{
    int nData = UART_NULL_CHAR;

    if(m_pUartPort == NULL)
        return;

    nData = m_pUartPort->GetComData();
    while (nData != UART_NULL_CHAR)
    {
        if(m_nRxSize || (m_nRxSize == 0 && nData == '$') || (m_nRxSize == 0 && nData == '!') || (m_nRxSize == 0 && nData == '&') || (m_nRxSize == 0 && nData == '#'))
            m_pRxData[m_nRxSize++] = nData;

        if(m_nRxSize > 5 && nData == ASC_CHR_LF)
        {
            m_pRxData[m_nRxSize] = 0x00;

        #ifdef __LOOPBACK_MKD_SENTENCE__
            // for test - Loopback to MKD
            SendOutStr((char*)m_pRxData);
        #endif

            ProcessSentence((char*)m_pRxData);

            m_nRxSize = 0;
        }

        if(m_nRxSize >= (RX_MAX_DATA_SIZE - 2))
            m_nRxSize = 0;

        nData = m_pUartPort->GetComData();
    }
}

/**
 * @brief Process the sentences
 * @param pstrCmd The sentence to be parsed
 * @return None
 */
int CPI::ProcessSentence(char *pstrCmd)
{
    char pstrSentence[RX_MAX_DATA_SIZE];
    char pstrTalkerID[LEN_NMEA_TALKER];
    char pstrSentID[LEN_NMEA_SENT];
    int   nLen;

    nLen = strlen((char*)pstrCmd);
    strcpy(pstrSentence, pstrCmd);

    if (!CSentence::IsValidCheckSum(pstrCmd))
    {
        return 0;
    }

    CSentence::GetTalkerSenderID((char*)pstrCmd, pstrTalkerID, pstrSentID);

    if(m_bEnableSendRcvDataToMKD)
    {
        //--------------------------------------------------------------------------------
        // For Port monitoring
        // ex) $GPRMC,005957.12,A,0004.9972,N,00000.0000,E,0,0,200103,,,D*57\r\n
        // => $GPRMC,005957.12,A,0004.9972,N,00000.0000,E,0,0,200103,,,D*57,#E\r\n 로 송신
        //--------------------------------------------------------------------------------
        sprintf(&pstrSentence[nLen-2], ",#%c\r\n", m_chPortID);
        CMKD::getInst()->SendOutStr(pstrSentence);
    }

    if(!strcmp(pstrSentID, "SPW"))  {ProcessSPW(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "SSA"))  {ProcessSSA(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "SSD"))  {ProcessSSD(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "VSD"))  {ProcessVSD(pstrCmd);   return 1;}

    // Intellian sentence
    if(!strcmp(pstrSentID, "INL"))  {ProcessINL(pstrCmd);   return 1;}

    if(!strcmp(pstrSentID, "ABM"))  {ProcessABM(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "ACA"))  {ProcessACA(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "ACK"))  {ProcessACK(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "ACN"))  {ProcessACN(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "AIQ"))  {ProcessAIQ(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "AIR"))  {ProcessAIR(pstrCmd);   return 1;}

    if(!strcmp(pstrSentID, "BBM"))  {ProcessBBM(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "EPV"))  {ProcessEPV(pstrCmd);   return 1;}
    if(!strcmp(pstrSentID, "HBT"))  {ProcessHBT(pstrCmd);   return 1;}

    if(!strcmp(pstrSentID, "LRF"))
    {
        if(!CSetupMgr::getInst()->GetLongRangeAutoReply())
        {
            //---------------------------------------------------------------------------------------
            // LR manual mode 상태에서 외부포트를 통해 LR sentence 수신했을때 사용자 ACK 입력 수신됨
            // -> LR response 를 외부포트로 출력해야함
            //---------------------------------------------------------------------------------------
            if(!strncmp(pstrTalkerID, "SY", 2))
            {
                CLongRange::getInst()->ProcessLRILRF(LRF, pstrCmd, LR_FROM_PORT_PI);
            }
            else
            {
                if(CLongRange::getInst()->ProcessAckLRF(pstrCmd, LR_FROM_PORT_PI))
                {
                    CMKD::getInst()->SendOutStr(pstrCmd);
                }
            }
        }
        return 1;
    }
    if(!strcmp((char*)pstrSentID, "LRI"))
    {
        CLongRange::getInst()->ProcessLRILRF(LRI, pstrCmd, LR_FROM_PORT_PI);
        return 1;
    }

    return 1;
}

/**
 * @brief Run api periodically
 * @return None
 */
void CPI::RunPeriodicallyPI()
{
    // Disable password valid time after 3 seconds
    m_xSpw.RunPeriodically();
    m_xSsa.RunPeriodically();

    if(cTimerSys::getInst()->GetTimeDiffSec(CVdlTxMgr::m_dwSendSecVDO) >= 1)
    {
    // [IEC 61993-2, Ed.3, clause 7.6.3.4]
    // The VDO sentence (containing Messages 1, 2 or 3) shall be output on both high-speed output ports, 
    // at nominal 1 s intervals, use A and B to indicate that the data was transmitted on the VDL channel A or B, 
    // null indicating not transmitted on the VDL.
    #ifdef __ENABLE_DUMMY_VDO__
        SendDummyVDO();
    #endif
    }
}

void CPI::RunUartIsrHandler()
{
    if (m_pUartPort != NULL)
    {
        m_pUartPort->RunUartIsrHandler();
    }
}
