#include "SysConst.h"
#include "SysLib.h"
#include "string.h"
#include "LayerNetwork.h"
#include "VdlRxMgr.h"
#include "GpsBoard.h"
#include "AisModem.h"
#include "ChannelMgr.h"
#include "SensorMgr.h"
#include "FrameMapMgr.h"
#include "SetupMgr.h"
#include "Ship.h"
#include "Timer.h"
#include "SlotMgr.h"


#define SIZE_SLOTRULE_BUFF    NUM_SLOT_PER_FRAME

INT8 *gpnAppRuleBuff = NULL;
WORD *gpwCandiListByPriBuff = NULL;
float *gpfDistList = NULL;

CSlotMgr::CSlotMgr()
{
    gpnAppRuleBuff = (INT8*)SysAllocMemory(sizeof(INT8) * SIZE_SLOTRULE_BUFF);
    gpwCandiListByPriBuff = (WORD*)SysAllocMemory(sizeof(WORD) * SIZE_SLOTRULE_BUFF);
    gpfDistList = (float*)SysAllocMemory(sizeof(float) * SIZE_SLOTRULE_BUFF);
}

CSlotMgr::~CSlotMgr()
{
}

/**
 * @brief Check if slot is adjacent to another slot
 * @param pOppCh Opposite channel manager
 * @param wFrSlotID Frame slot ID
 * @return True if slot is adjacent, false otherwise
 */
BOOL CSlotMgr::CheckSlotAdjacentSlotRule(CChannelMgr *pOppCh, WORD wFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlotLeft = pOppCh->GetSlotDataPtr(FrameMapSlotIdAdd(wFrSlotID, -1));
    FRAMEMAP_SLOTDATA *pSlot     = pOppCh->GetSlotDataPtr(wFrSlotID);
    FRAMEMAP_SLOTDATA *pSlotRight= pOppCh->GetSlotDataPtr(FrameMapSlotIdAdd(wFrSlotID, 1));
    if(pSlot)
    {
        if( pSlot->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() ||       // check if the slot on the opposite channel is internally allocated
            pSlotLeft->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() ||   // check if the adjacent slot on the opposite channel is internally allocated
            pSlotRight->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())    // check if the adjacent slot on the opposite channel is internally allocated
            return FALSE;
        return TRUE;
    }
    return FALSE;
}

/**
 * @brief Check if slot is free
 * @param pCurCh Current channel manager
 * @param wFrSlotID Frame slot ID
 * @return True if slot is free, false otherwise
 */
BOOL CSlotMgr::CheckFrameMapSlotFree(CChannelMgr *pCurCh, WORD wFrSlotID)
{
    //-------------------------------------------------------------------------------------------------------------------------
    // IEC-61993-2 17.7.1 NOTE
    //-------------------------------------------------------------------------------------------------------------------------
    // Free slots are:  - Slots not used
    //                  - Slots used by a mobile station under way that has not been received for 3 min or more
    //                  - Slots used by a base station (Message 20 and Message 4) beyond 120 NM, garbled slots.
    //-------------------------------------------------------------------------------------------------------------------------

    if(IsSlotBaseStMsg20FATDMA(pCurCh, wFrSlotID))
        return FALSE;

    FRAMEMAP_SLOTDATA *pSlot = pCurCh->GetSlotDataPtr(wFrSlotID);
    if(pSlot)
    {
        if(pSlot->uMMSI == AIS_AB_MMSI_NULL)
            return TRUE;

        if(!CUserDirMgr::getInst()->IsStationPosAvail(pSlot->uMMSI))
            return FALSE;

        if(IsValidMMSI_BaseSt(pSlot->uMMSI))
        {
            if(!CUserDirMgr::getInst()->IsBaseStationWithin120NM(pSlot->uMMSI))
            {
                return TRUE;
            }
        }

        if(IsValidMMSI_MobileSt(pSlot->uMMSI))
        {
            xDIRDATA *pDirData = CUserDirMgr::getInst()->FindDirDataPtr(pSlot->uMMSI);
            if(pDirData && (!IsNavStatusNotUnderway(pDirData->bNavStatus) && CheckStationNoPosReportFor3Min(pSlot->uMMSI)))
            {

                return TRUE;
            }
        }
    }

    return FALSE;
}

/**
 * @brief Check if station has not reported position for 3 minutes
 * @param dMMSI MMSI
 * @return True if station has not reported position for 3 minutes, false otherwise
 */
BOOL CSlotMgr::CheckStationNoPosReportFor3Min(DWORD dMMSI)
{
    //--------------------------------------------------------------------------------------------------------
    // ITU-R M.1371-5 ed.2014 Appndix2 3.3.1.2 Candidate slots
    // The slots of another station, whose navigational status is not set to "at anchor" or "moored" and has
    // not been received for 3 min, should be used as candidate slots for intentional slot reuse.
    //--------------------------------------------------------------------------------------------------------

    xDIRDATA *pDirData;
    if(IsValidMMSI_MobileSt(dMMSI))
    {
        if((pDirData = CUserDirMgr::getInst()->FindDirDataPtr(dMMSI)))
        {
            return (IsNavStatusNotUnderway(pDirData->bNavStatus) && cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastPosReportSec) > 180);
        }
    }
    return FALSE;
}

/**
 * @brief Check if slot is available
 * @param pDirData Directory data
 * @return True if slot is available, false otherwise
 */
BOOL CSlotMgr::CheckReuse1minRule(xDIRDATA *pDirData)
{
    if(!pDirData)
        return TRUE;                    // when MMSI is NULL, it's reusable.
    return (pDirData->dwLastSlotReuseSec == 0 || cTimerSys::getInst()->GetTimeDiffSec(pDirData->dwLastSlotReuseSec) > SLOT_REUSE_UNAVAIL_SEC);
}

/**
 * @brief Check if slot is available
 * @param nMMSI MMSI
 * @return True if slot is available, false otherwise
 */
BOOL CSlotMgr::CheckReuse1minRule(int nMMSI)
{
    xDIRDATA *pDirData = CUserDirMgr::getInst()->FindDirDataPtr(nMMSI);
    return CheckReuse1minRule(pDirData);
}

/**
 * @brief Check if slot is available
 * @param pCurCh Current channel manager
 * @param wFrSlotID Frame slot ID
 * @return True if slot is available, false otherwise
 */
BOOL CSlotMgr::CheckFrameMapSlotAvail(CChannelMgr *pCurCh, WORD wFrSlotID)
{
    //-------------------------------------------------------------------------------------------------------------------------
    // IEC-61993-2 17.7.1 NOTE
    //-------------------------------------------------------------------------------------------------------------------------
    // Available slots are: Distant station slots. mobile station(SOTDMA or ITDMA), or Base station reserved slot(FATDMA or MSG4) beyond 120NM
    // Unavailable slots are:    - Near station slots
    //                            - Slots used by a base station (Message 20 and Message 4) within 120 NM,
    //                            - Slots used by mobile stations reporting without position information
    //                            - Slots used by mobile stations with a reporting interval of 1 min or more.
    //-------------------------------------------------------------------------------------------------------------------------
    //------------------------------------------------------------------------------------------------
    // ITU-R M.1371-5 ed.2014 Appndix2 4.4.1 Intentional slot reuse by the own station
    // Slots may not be intentionally reused from stations that indicate no position available.
    //------------------------------------------------------------------------------------------------

    if(IsSlotBaseStMsg20FATDMA(pCurCh, wFrSlotID))
        return FALSE;

    if(CheckFrameMapSlotFree(pCurCh, wFrSlotID))
        return TRUE;                                                // free slot

    FRAMEMAP_SLOTDATA *pSlot = pCurCh->GetSlotDataPtr(wFrSlotID);
    if(!pSlot)
        return FALSE;

    xDIRDATA *pDirData = CUserDirMgr::getInst()->FindDirDataPtr(pSlot->uMMSI);
    if(!pDirData)
        return FALSE;

    //------------------------------------------------------------------------------------------------
    // IEC-61993-2 17.7.2
    // b) slots of the most distant test targets are used for transmission on channel A.
    //      Check that not more than one slot of a station is reused in a frame,
    //------------------------------------------------------------------------------------------------
    if(!CheckReuse1minRule(pDirData))
    {
        return FALSE;
    }

    if(!CUserDirMgr::getInst()->IsStationPosAvail(pSlot->uMMSI)  ||     // check if the slot is allocated by a station that has no position available
        pSlot->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI() ||   // check if the slot is internally allocated
        IsSlotBaseStMsg20FATDMA(pCurCh, wFrSlotID))                     // check if the slot is allocated by a base station with MSG 20
        return FALSE;

    if(IsValidMMSI_MobileSt(pSlot->uMMSI))
    {
        // 해당 스테이션이 1분 초과의 보고주기인지 판단 : navStatus로
        if(IsNavStatusNotUnderway(pDirData->bNavStatus))
            return FALSE;
    }
    else if(IsValidMMSI_BaseSt(pSlot->uMMSI))
    {
        //-------------------------------------------------------------------------------------------------------------------------------------------
        // ITU-R M.1371-5 ed.2014 Appndix2 4.4.1 Intentional slot reuse by the own station
        // Unavailable - Base station reserved slot (FATDMA or Message 4) within 120 NM, or a Mobile Station reporting without position information.
        //-------------------------------------------------------------------------------------------------------------------------------------------
        if(CUserDirMgr::getInst()->IsBaseStationWithin120NM(pSlot->uMMSI))
            return FALSE;
    }

    return TRUE;
}

/**
 * @brief Check if slot is allocated by a base station with MSG 20
 * @param pCurCh Current channel manager
 * @param wFrSlotID Frame slot ID
 * @return True if slot is allocated by a base station with MSG 20, false otherwise
 */
BOOL CSlotMgr::IsSlotBaseStMsg20FATDMA(CChannelMgr *pCurCh, WORD wFrSlotID)
{
    FRAMEMAP_SLOTDATA *pSlot = pCurCh->GetSlotDataPtr(wFrSlotID);
    if(!pSlot)
        return FALSE;

    // check if the slot is allocated by a base station with MSG 20
    return (pSlot->bFAtoSO || pSlot->uMsgID == AIS_MSG_NO_ID_20);
}

/**
 * @brief Get slot allocation priority list
 * @param pCurCh Current channel manager
 * @param nMsgID Message ID
 * @param wStartSI Start slot
 * @param nSizeSI Size of the slot
 */
void CSlotMgr::GetSlotAllocPriorityList(CChannelMgr *pCurCh, int nMsgID, WORD wStartSI, int nSizeSI)
{
    //--------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 4.4.1 Intentional slot reuse by the own station
    // A station should reuse time slots only in accordance with this paragraph 
    // and only when own position is available.

    WORD wFrSlotID = wStartSI;
    CChannelMgr *pOppCh = CLayerNetwork::getInst()->GetOppositeChPtr(pCurCh);

    BOOL bEnableSlotReuse = CSensorMgr::getInst()->IsGnssFixed();
    BOOL bCurFree = FALSE;
    BOOL bOppFree = FALSE;
    BOOL bCurAvail= FALSE;
    BOOL bOppAvail= FALSE;

    int nCnt = 0;
    FRAMEMAP_SLOTDATA *pSlot;
    while(nCnt < nSizeSI)
    {
        gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_NA;

        if(CheckSlotAdjacentSlotRule(pOppCh, wFrSlotID))
        {
            bCurFree = CheckFrameMapSlotFree(pCurCh, wFrSlotID);
            bOppFree = CheckFrameMapSlotFree(pOppCh, wFrSlotID);
            bCurAvail= CheckFrameMapSlotAvail(pCurCh, wFrSlotID);
            bOppAvail= CheckFrameMapSlotAvail(pOppCh, wFrSlotID);

            if(bCurFree && bOppFree)
            {
                gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_0;
            }
            else
            {
                pSlot = pCurCh->GetSlotDataPtr(wFrSlotID);
                if(bEnableSlotReuse)
                {
                    if(!IsSlotBaseStMsg20FATDMA(pCurCh, wFrSlotID))
                    {
                        if(CheckReuse1minRule(pSlot->uMMSI))
                        {
                            if(bCurFree && bOppAvail)
                            {
                                gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_1;
                            }
                            else if(bCurAvail && bOppFree)
                            {
                                gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_2;
                            }
                            else if(bCurAvail && bOppAvail)
                            {
                                gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_3;
                            }
                            else if(bCurFree && !bOppAvail)
                            {
                                gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_4;
                            }
                            else if(bCurAvail && !bOppAvail)
                            {
                                gpnAppRuleBuff[nCnt] = SLOTREUSE_RULE_5;
                            }
                        }
                    }
                }
            }
        }

        wFrSlotID = FrameMapSlotIdAdd(wFrSlotID, 1);
        ++nCnt;
    }
}

/**
 * @brief Get reusable candidate slot list
 * @param pwCandiSlotList Candidate slot list
 * @param nNumCandiToGet Number of candidates to get
 * @param pCurCh Current channel manager
 * @param nMsgID Message ID
 * @param wStartSI Start slot
 * @param nSizeSI Size of the slot
 * @param nNumSlotToReserve Number of slots to reserve
 * @return Number of candidates
 */
int CSlotMgr::GetReusableCandiList(WORD *pwCandiSlotList, int nNumCandiToGet, CChannelMgr *pCurCh, 
                                    int nMsgID, WORD wStartSI, int nSizeSI, int nNumSlotToReserve)
{
    //----------------------------------------------------------
    // ITU-R M.1371-5 ed.2014 Appndix2 3.3.1.2 Candidate slots
    // ITU-R M.1371-5 ed.2014 Appndix2 3.3.1.2 Figure 9
    // ITU-R M.1371-5 ed.2014 Appndix2 3.3.1.2 Candidate slots
    //----------------------------------------------------------

    int nNumTotalCandi = 0;
    int nNumByPri = 0;
    WORD pwCandiSlotBuffByPri[NUM_MAX_SLOTS_TXBLOCK];
    INT8 nSlotReuseStep = SLOTREUSE_RULE_1;
    int nNumToGetByPri;

    while(nSlotReuseStep < SLOTREUSE_RULE_NA)
    {
        nNumToGetByPri = nNumCandiToGet - nNumTotalCandi;
        if(nNumToGetByPri <= 0)
            break;

        // 각 Slot reuse priority 단계에 해당하는 candidate 슬롯리스트를 얻는다
        nNumByPri = GetReusableCandiSlotListByPri(pwCandiSlotBuffByPri, nNumToGetByPri, nSlotReuseStep, 
                                                pCurCh, wStartSI, nSizeSI, nNumSlotToReserve);
        if(nNumByPri > 0)
        {
            int nNumToCpy = MIN(nNumToGetByPri, nNumByPri);
            memmove(&pwCandiSlotList[nNumTotalCandi], pwCandiSlotBuffByPri, sizeof(pwCandiSlotBuffByPri[0])*nNumToCpy);
            nNumTotalCandi += nNumToCpy;

            if(nNumToCpy > 0)
                break;
        }

        ++nSlotReuseStep;
    }

    return nNumTotalCandi;
}

/**
 * @brief Get candidate slot list by priority
 * @param pwCandiListByPriBuff Candidate slot list by priority
 * @param nNumCandiToGet Number of candidates to get
 * @param nSlotReuseStep Slot reuse step
 * @param pCurCh Current channel manager
 * @param wStartSI Start slot
 * @param nSizeSI Size of the slot
 * @param nNumSlotToReserve Number of slots to reserve
 * @return Number of candidates
 */
int CSlotMgr::GetCandiSlotBuffByPri(WORD *pwCandiListByPriBuff, int nNumCandiToGet, INT8 nSlotReuseStep, 
                                    CChannelMgr *pCurCh, WORD wStartSI, int nSizeSI, const int nNumSlotToReserve)
{
    int nNumCandi = 0;
    int nCntConsecutive = 0;
    WORD wResStartSlot = 0;

    for(int i = 0 ; i < nSizeSI ; i++)
    {
        // 연속슬롯의 맨처음 슬롯의 priority 가 같고 나머지슬롯은 reusable 하기만하면 후보로 결정하기 위해
        if(    (!nCntConsecutive && gpnAppRuleBuff[i] == nSlotReuseStep) 
            || (nCntConsecutive > 0 && gpnAppRuleBuff[i] != SLOTREUSE_RULE_NA))
        {
            nCntConsecutive++;
            if(nCntConsecutive == 1)
                wResStartSlot = FrameMapSlotIdAdd(wStartSI, i);
        }
        else
            nCntConsecutive = 0;

        // check a slot or a block of consecutive slots
        if(nCntConsecutive == nNumSlotToReserve)
        {
            pwCandiListByPriBuff[nNumCandi++] = wResStartSlot;
            if(nNumCandi >= nNumCandiToGet)
                break;
            nCntConsecutive = 0;
        }
    }

    return nNumCandi;
}

/**
 * @brief Get reusable candidate slot list by priority
 * @param pwCandiListByPriBuff Candidate slot list by priority
 * @param nNumCandiToGet Number of candidates to get
 * @param nSlotReuseStep Slot reuse step
 * @param pCurCh Current channel manager
 * @param wStartSI Start slot
 * @param nSizeSI Size of the slot
 * @param nNumSlotToReserve Number of slots to reserve
 * @return Number of candidates
 */
int CSlotMgr::GetReusableCandiSlotListByPri(WORD *pwCandiListByPriBuff, int nNumCandiToGet, INT8 nSlotReuseStep, 
                                            CChannelMgr *pCurCh, WORD wStartSI, int nSizeSI, const int nNumSlotToReserve)
{
    //--------------------------------------------------------------------------
    // 각 Slot reuse priority 단계에 해당하는 candidate 슬롯리스트를 얻는다.
    // input : gpnAppRuleBuff, the list of priority rule number
    // output : pwCandiListByPriBuff, the list of candidate slot ID
    //--------------------------------------------------------------------------
    //------------------------------------------------------------------------------------------------------------------
    // ITU-R-1371-5 Annex2 3.3.1.2 Candidate slots
    // For Class A mobile AIS stations when selecting candidates for messages longer than one (1) slot (see § 3.2.2.11)
    // a candidate slot should be the first slot in a consecutive block of free or available slots.
    //------------------------------------------------------------------------------------------------------------------
    //---------------------------------------
    // ITU-R-1371-5 Annex2 3.3.1 Figure 9
    //---------------------------------------
    if(nSlotReuseStep == SLOTREUSE_RULE_0)
        return 0;

    int nNumByPri = GetCandiSlotBuffByPri(gpwCandiListByPriBuff, nSizeSI, nSlotReuseStep, pCurCh, wStartSI, nSizeSI, nNumSlotToReserve);    // 해당 priority 에 속하는 모든 슬롯을 구한다.
    if(nNumByPri <= 0)
        return 0;

    if(nSlotReuseStep == SLOTREUSE_RULE_1 || nSlotReuseStep == SLOTREUSE_RULE_4)                        // In case under the condition that current CH is free, sort the opposite channel
    {
        CChannelMgr *pOppCh = CLayerNetwork::getInst()->GetOppositeChPtr(pCurCh);
        return GetReusableCandiSlotListByPriSub(pwCandiListByPriBuff, gpwCandiListByPriBuff, nNumByPri, TRUE, nNumCandiToGet, nSlotReuseStep, pOppCh, wStartSI, nSizeSI, nNumSlotToReserve);
    }
    return GetReusableCandiSlotListByPriSub(pwCandiListByPriBuff, gpwCandiListByPriBuff, nNumByPri, TRUE, nNumCandiToGet, nSlotReuseStep, pCurCh, wStartSI, nSizeSI, nNumSlotToReserve);
}

int CSlotMgr::GetReusableCandiSlotListByPriSub(WORD *pwCandiListByPriBuff, WORD *pwInputSlotList, int nNumByPri, BOOL bCheckReuseTime, int nNumCandiToGet, INT8 nSlotReuseStep, CChannelMgr *pCh, WORD wStartSI, int nSizeSI, const int nNumSlotToReserve)
{
    //------------------------------------------------------------------------------------------------
    // 가장 멀리 떨어진 스테이션 순으로 슬롯들을 sort 한다.
    // 모두 같은 거리이면 같은 priority 를 가지므로 맨 처음 슬롯으로 정한다. (Ivan test report p.796)
    // 거리를 알수 없는 스테이션도 거리를 최대값으로 정한 후 sort 한다.
    //------------------------------------------------------------------------------------------------
    const float    SORT_DIST_NA = -1;

    static WORD *pwTmpSlotList = NULL;
    if(!pwTmpSlotList)
        pwTmpSlotList = (WORD*)SysAllocMemory(sizeof(WORD) * SIZE_SLOTRULE_BUFF);

    FRAMEMAP_SLOTDATA *pSlot;
    xDIRDATA *pDirData;
    int i, k, nTemp;
    float fTemp;

    int nToAlloc = MIN(nNumByPri, nNumCandiToGet);
    int nNumFinalCandi = 0;
    int nNumToSort = 0;

    for(i = 0; i < nNumByPri ; i++)
    {
        if((pSlot = pCh->GetSlotDataPtr(pwInputSlotList[i])))
        {
            pDirData = CUserDirMgr::getInst()->FindDirDataPtr(pSlot->uMMSI);

            fTemp = AIS_DIST_NM_INVALID;
            if(pDirData)
            {
                if(!CheckStationNoPosReportFor3Min(pSlot->uMMSI))
                    fTemp = pDirData->fDistance;

                if(fTemp > 0 && (!bCheckReuseTime || CheckReuse1minRule(pDirData)))                                // IEC-61993-2 17.7.2 (b) not more than one slot of a station is reused in a frame
                {
                    gpfDistList[nNumToSort]        = fTemp;
                    pwTmpSlotList[nNumToSort]    = pwInputSlotList[i];
                    nNumToSort++;
                }
            }
        }
    }

    for(i = 0 ; i < nNumToSort ; i++)
    {
        for(k = i+1 ; k < nNumToSort ; k++)
        {
            if(gpfDistList[i] < gpfDistList[k])
            {
                fTemp = gpfDistList[i];
                gpfDistList[i] = gpfDistList[k];
                gpfDistList[k] = fTemp;

                nTemp = pwTmpSlotList[i];
                pwTmpSlotList[i] = pwTmpSlotList[k];
                pwTmpSlotList[k] = nTemp;
            }
        }
    }

    nNumFinalCandi = 0;
    for(i = 0 ; i < nNumToSort ; i++)
    {
        if(gpfDistList[i] != SORT_DIST_NA)
        {
            pwCandiListByPriBuff[nNumFinalCandi] = pwTmpSlotList[i];
            if(++nNumFinalCandi >= nToAlloc)
                break;
        }
    }

    return nNumFinalCandi;
}

WORD CSlotMgr::GetTxTdmaSlotAlloc_SO(CChannelMgr *pCurCh, int nMsgID, int nStartSI, int nSizeSI, const int nNumSlotToReserve, WORD wCurFrMapSlotToAvoidID)
{
    //---------------------------------------
    // ITU-R-1371-5 Annex2 3.3.1 Figure 9
    // nNumSlotToReserve : 연속슬롯의 수
    //---------------------------------------

    if(nSizeSI <= 0)
        return SLOTID_NONE;

    if(CSetupMgr::getInst()->IsAisBClassSO())
    {
        // ITU-R.M 1371-5 Annex2. 3.3.1.2 (page 27)
        // For class B "SO" mobile AIS stations the candidate slots for message6, 8, 12 and 14 should be free.
        // 이반 테스트 리포트 확인 후 필요 시 구현예정
    }

    if(nNumSlotToReserve > NUM_MAX_SLOTS_TXBLOCK)
    {
        return SLOTID_NONE;
    }

    GetSlotAllocPriorityList(pCurCh, nMsgID, nStartSI, nSizeSI);

    WORD    pwCandiSlotList[NUM_SLOT_CANDI_MAX];
    WORD    pwFreeCandiSlotList[NUM_SLOT_CANDI_MAX+1];
    WORD    pwReuseCandiSlotList[NUM_SLOT_CANDI_MAX+1];
    int        nNumToGetReuse= 0;
    int        nNumFreeCandi = 0;
    int        nNumReuseCandi= 0;
    int        nNumTotalCandi= 0;

    memset(pwFreeCandiSlotList, 0xFF, sizeof(pwFreeCandiSlotList));
    memset(pwReuseCandiSlotList, 0xFF, sizeof(pwReuseCandiSlotList));
    memset(pwCandiSlotList, 0xFF, sizeof(pwCandiSlotList));

    nNumFreeCandi = GetCandiSlotBuffByPri(pwFreeCandiSlotList, NUM_SLOT_CANDI_MAX, SLOTREUSE_RULE_0, pCurCh, nStartSI, nSizeSI, nNumSlotToReserve);
    memcpy(&pwCandiSlotList[0], pwFreeCandiSlotList, nNumFreeCandi * sizeof(pwCandiSlotList[0]));
    nNumTotalCandi += nNumFreeCandi;

    if(nNumFreeCandi < NUM_SLOT_CANDI_MIN)
    {
        if(CSensorMgr::getInst()->IsGnssFixed())
        {
            nNumToGetReuse= NUM_SLOT_CANDI_MIN-nNumFreeCandi;
            nNumReuseCandi = GetReusableCandiList(pwReuseCandiSlotList, nNumToGetReuse, pCurCh, nMsgID, nStartSI, nSizeSI, nNumSlotToReserve);
            memcpy(&pwCandiSlotList[nNumTotalCandi], pwReuseCandiSlotList, nNumReuseCandi * sizeof(pwCandiSlotList[0]));
            nNumTotalCandi += nNumReuseCandi;
        }
    }

    WORD wAllocSlot = SLOTID_NONE;
    int nRandID = -1;

    if(nNumTotalCandi > 0)
    {
        if(nNumTotalCandi <= 1)
            wAllocSlot = pwCandiSlotList[0];
        else
        {
            if(wCurFrMapSlotToAvoidID != SLOTID_NONE)
            {
                WORD wCurSlotID = pCurCh->GetSlotIdFromFrameSlotID(wCurFrMapSlotToAvoidID);
                for(int i = 0 ; i < nNumTotalCandi ; i++)
                {
                    if(pCurCh->GetSlotIdFromFrameSlotID(pwCandiSlotList[i]) == wCurSlotID)
                    {
                        memcpy(&pwCandiSlotList[i], &pwReuseCandiSlotList[i+1], nNumTotalCandi-i-1 * sizeof(pwCandiSlotList[0]));
                        --nNumTotalCandi;
                        break;
                    }
                }
            }

            nRandID = GetRandomValueOneBySEED(0, nNumTotalCandi-1);
            wAllocSlot = pwCandiSlotList[nRandID];
        }
    }

    BOOL bGetReuseSlot = CheckWordDataExisting(pwReuseCandiSlotList, nNumReuseCandi, wAllocSlot);

    CChannelMgr *pOppCh = CLayerNetwork::getInst()->GetOppositeChPtr(pCurCh);
    FRAMEMAP_SLOTDATA *pSlot = pCurCh->GetSlotDataPtr(wAllocSlot);
    FRAMEMAP_SLOTDATA *pOppSlot = pOppCh->GetSlotDataPtr(wAllocSlot);
    if(pSlot && pOppSlot)
    {
        if(bGetReuseSlot)
        {
            CUserDirMgr::getInst()->SetLastSlotReuseTimeTick(pSlot->uMMSI);
        }
    }

    return wAllocSlot;
}

/**
 * @brief Get probabilistic persistent allocation slot
 * @param pCurCh Current channel manager
 * @param pwCandiSlotList Candidate slot list
 * @param nNumCandidates Number of candidates
 * @return Allocated slot ID
 */
WORD CSlotMgr::GetProbPersistentAllocSlot(CChannelMgr *pCurCh, WORD *pwCandiSlotList, INT16 nNumCandidates)
{
    int     nRTCSC = 0;
    float   fRTPS  = 0;
    float   fRTP1  = 0;
    float   fRTP2  = 0;
    int     nRTA   = 0;
    float   fRTPI  = 0;

    if(nNumCandidates > 0)
    {
        nRTCSC = nNumCandidates;

        fRTPS = 100.0f / nRTCSC;
        fRTP2 = fRTPS;

        int i = 0;
        while(i < nNumCandidates && nRTCSC > 0)
        {
            fRTP1 = GetRandomValueOneBySEED(0, 100);

            if(fRTP1 <= fRTP2)
            {
                return pwCandiSlotList[i];
            }

            fRTPI = (100 - fRTP2) / (float)nRTCSC;
            fRTP2 += fRTPI;
            nRTCSC--;
            i++;
            if(++nRTA > 150)    // LME.RTA : 0 ~ 149
                break;
        }
        return pwCandiSlotList[nNumCandidates-1];
    }
    return SLOTID_NONE;
}

int CSlotMgr::GetPriorityAsMsgID(int nMsgID)
{
    int nPriority = 4;

    switch (nMsgID)
    {
        case AIS_MSG_NO_ID_01:
        case AIS_MSG_NO_ID_02:
        case AIS_MSG_NO_ID_03:
        case AIS_MSG_NO_ID_04:
        case AIS_MSG_NO_ID_07:
        case AIS_MSG_NO_ID_09:
        case AIS_MSG_NO_ID_13:
        case AIS_MSG_NO_ID_16:
        case AIS_MSG_NO_ID_18:
        case AIS_MSG_NO_ID_19:
        case AIS_MSG_NO_ID_20:
        case AIS_MSG_NO_ID_21:
        case AIS_MSG_NO_ID_22:
        case AIS_MSG_NO_ID_23:
        case AIS_MSG_NO_ID_27:
            nPriority = 1;
            break;
        case AIS_MSG_NO_ID_12:
        case AIS_MSG_NO_ID_14:
        case AIS_MSG_NO_ID_17:
            nPriority = 2;
            break;
        case AIS_MSG_NO_ID_10:
        case AIS_MSG_NO_ID_11:
        case AIS_MSG_NO_ID_15:
            nPriority = 3;
            break;
        case AIS_MSG_NO_ID_05:
        case AIS_MSG_NO_ID_06:
        case AIS_MSG_NO_ID_08:
        case AIS_MSG_NO_ID_24:
        case AIS_MSG_NO_ID_25:
        case AIS_MSG_NO_ID_26:
            nPriority = 4;
            break;
    }

    return nPriority;
}

WORD CSlotMgr::GetInternalSlotAsPriority(CChannelMgr *pCurCh, int nMsgID, WORD wStartSI, int nSizeSI)
{
    WORD wAllocSlot = SLOTID_NONE;
    WORD wResStartSlot = wStartSI;
    int nPriorityMsgId = GetPriorityAsMsgID(nMsgID);

    for(int i = 0 ; i < nSizeSI ; i++)
    {
        FRAMEMAP_SLOTDATA *pSlot = pCurCh->GetSlotDataPtr(wResStartSlot);
        if (pSlot->uMMSI == cShip::getOwnShipInst()->GetOwnShipMMSI())
        {
            if (nPriorityMsgId < GetPriorityAsMsgID(pSlot->uMsgID))
            {
                wAllocSlot = wResStartSlot;
                nPriorityMsgId = GetPriorityAsMsgID(pSlot->uMsgID);
            }
        }

        wResStartSlot = FrameMapSlotIdAdd(wStartSI, i);
    }

    return wAllocSlot;
}

/**
 * @brief Allocate a slot for transmission using random access
 * @param pCurCh Current channel manager
 * @param nMsgID Message ID
 * @param nStartSI Start slot
 * @param nSizeSI Size of the slot
 * @param nNumSlotToReserve Number of slots to reserve
 * @return Allocated slot ID
 */
WORD CSlotMgr::GetTxTdmaSlotAlloc_RA(CChannelMgr *pCurCh, int nMsgID, const int nStartSI, const int nSizeSI, const int nNumSlotToReserve)
{
    //-------------------------------------------------------------------------------------------
    // ******* Random access time division multiple access
    // RATDMA is used when a station needs to allocate a slot, which has not been pre-announced. 
    // This is generally done for the first transmission slot during data link network entry, 
    // or for messages of a non-repeatable character.

    if(nNumSlotToReserve > NUM_MAX_SLOTS_TXBLOCK)
    {
        return SLOTID_NONE;
    }
    if(nSizeSI < 0 || nStartSI < 0 || nStartSI >= NUM_SLOT_FRAMEMAP || nStartSI == SLOTID_NONE)
    {
        return SLOTID_NONE;
    }

    GetSlotAllocPriorityList(pCurCh, nMsgID, nStartSI, nSizeSI);

    WORD    wAllocSlot = SLOTID_NONE;
    WORD    pwFreeCandiSlotList[NUM_SLOT_CANDI_MAX+1];
    WORD    pwReuseCandiSlotList[NUM_SLOT_CANDI_MAX+1];
    WORD    pwCandiSlotList[NUM_SLOT_CANDI_MAX+1];
    int     nNumToGetReuse= 0;
    int     nNumFreeCandi = 0;
    int     nNumReuseCandi= 0;
    int     nNumTotalCandi= 0;

    memset(pwFreeCandiSlotList, 0xFF, sizeof(pwFreeCandiSlotList));
    memset(pwReuseCandiSlotList, 0xFF, sizeof(pwReuseCandiSlotList));
    memset(pwCandiSlotList, 0xFF, sizeof(pwCandiSlotList));

    nNumFreeCandi = GetCandiSlotBuffByPri(pwFreeCandiSlotList, NUM_SLOT_CANDI_MAX, SLOTREUSE_RULE_0, pCurCh, nStartSI, nSizeSI, nNumSlotToReserve);
    memcpy(&pwCandiSlotList[0], pwFreeCandiSlotList, nNumFreeCandi * sizeof(pwCandiSlotList[0]));
    nNumTotalCandi += nNumFreeCandi;

    if(nNumFreeCandi < NUM_SLOT_CANDI_MIN)
    {
        if(CSensorMgr::getInst()->IsGnssFixed())
        {
            nNumToGetReuse= NUM_SLOT_CANDI_MIN-nNumFreeCandi;
            nNumReuseCandi = GetReusableCandiList(pwReuseCandiSlotList, nNumToGetReuse, pCurCh, nMsgID, nStartSI, nSizeSI, nNumSlotToReserve);
            memcpy(&pwCandiSlotList[nNumTotalCandi], pwReuseCandiSlotList, nNumReuseCandi * sizeof(pwCandiSlotList[0]));
            nNumTotalCandi += nNumReuseCandi;
        }
    }

    if(nNumTotalCandi > 0)
    {
        if(nNumTotalCandi <= 1)
            wAllocSlot = pwCandiSlotList[0];
        else
            wAllocSlot = GetProbPersistentAllocSlot(pCurCh, pwCandiSlotList, nNumTotalCandi);
    }
    else
    {
        // reuse 슬롯을 찾았음에도 후보수가 0인 경우 nSizeSI내 우선순위가 낮은 메세지의
        // Internal 슬롯이 존재하는 경우 우선순위가 높은 메세지로 대체하여 송신하도록 수정함.
        // Ivan test report TT17_6_Ed2.leg
        wAllocSlot = GetInternalSlotAsPriority(pCurCh, nMsgID, nStartSI, nSizeSI);
    }

    BOOL bGetReuseSlot = CheckWordDataExisting(pwReuseCandiSlotList, nNumReuseCandi, wAllocSlot);

    CChannelMgr *pOppCh = CLayerNetwork::getInst()->GetOppositeChPtr(pCurCh);
    FRAMEMAP_SLOTDATA *pSlot = pCurCh->GetSlotDataPtr(wAllocSlot);
    FRAMEMAP_SLOTDATA *pOppSlot = pOppCh->GetSlotDataPtr(wAllocSlot);
    if(pSlot && pOppSlot)
    {
        if(bGetReuseSlot)
        {
            CUserDirMgr::getInst()->SetLastSlotReuseTimeTick(pSlot->uMMSI);
        }
    }

    return wAllocSlot;
}

/**
 * @brief Run periodically slot manager
 */
void CSlotMgr::RunPeriodicallySlotMgr()
{
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 60)        // per every 1 min
    {
        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}
