#ifndef __REPORTRATEMGR_H__
#define __REPORTRATEMGR_H__

#include "DataType.h"
#include "AllConst.h"

class CReportRateMgr
{
public:
	CReportRateMgr();
	~CReportRateMgr();

    static std::shared_ptr<CReportRateMgr> getInst() {
        static std::shared_ptr<CReportRateMgr> pInst = std::make_shared<CReportRateMgr>();
        return pInst;
    }

public:
	void	Initialize();
	BOOL	SetTempUseReportByITDMA(BOOL bSet);
	void	GetReportIntervalForEachCh(float fNewReportIntervalTotal, int nNewTxChID1, int nNewTxChID2, float *pfReportIntSecTotal, float *pfReportIntSecCh1, float *pfReportIntSecCh2, UINT16 *puStaticIntSecCh1, UINT16 *puStaticIntSecCh2, UINT16 *puLongRangeIntSecCh1, UINT16 *puLongRangeIntSecCh2);
	void	RecalcReportInterval(float fSecRI);
	void	SetNewReportInterval(float fNewReportIntervalSecSO, int nNewTxChID1, int nNewTxChID2);
	int		GetReportIntervalSec();
	float	GetReportIntSecBySpeed();
	float	GetReportIntSecNextLonger();
	float	GetReportIntSecNextShorter();
	float	GetReportRate();
	BOOL	IsRIValueValid(float fReportIntervalSec);
	BOOL	CheckCurRRValid();
	float	CheckAutoModeReportInterval(BOOL bCheckSpdReduce, BOOL bChangeRR, BOOL *pRet=NULL);
	BOOL	IsReportRateForSOTDMA(float fReportIntervalSec);
	BOOL	IsReportRateForSOTDMA();

	BOOL	SetReportRateDoubleMode(BOOL bReportRateDoubleMode);
	void	RunPeriodicallyReportRateMgr();

public:
	BOOL	m_bReportRateDoubleMode;
	float	m_fReportIntervalSec;		// in seconds, REPORT_INTERVALSEC_MIN ~ REPORT_INTERVALSEC_MAX
	float	m_fReportRate;

	BOOL	m_bFirstCalcRR;

	BOOL	m_bTempUseReportByITDMA;
	DWORD	m_dwHdgDiffAboveTick;
};

#endif//__REPORTRATEMGR_H__
