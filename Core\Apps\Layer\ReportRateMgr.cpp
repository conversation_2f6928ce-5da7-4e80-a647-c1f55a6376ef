#include <stdlib.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "LayerPhysical.h"
#include "LayerNetwork.h"
#include "SyncMgr.h"
#include "Uart.h"
#include "Timer.h"
#include "VdlTxMgr.h"
#include "RosMgr.h"
#include "SetupMgr.h"
#include "LongRange.h"
#include "Ship.h"
#include "ReportRateMgr.h"

CReportRateMgr::CReportRateMgr()
{
    Initialize();
}

CReportRateMgr::~CReportRateMgr()
{
}

void CReportRateMgr::Initialize()
{
    m_fReportIntervalSec    = REPORT_INTERVALSEC_DFLT;
    m_fReportRate           = REPORT_RATE_DFLT;
    m_bReportRateDoubleMode = FALSE;

    m_bFirstCalcRR          = TRUE;
    m_bTempUseReportByITDMA = 0;
    m_dwHdgDiffAboveTick    = 0;
}

/** 
 * @brief Set temporary use report by ITDMA
 * @param bOn TRUE to enable, FALSE to disable
 * @return TRUE if successful, FALSE otherwise
 */
BOOL CReportRateMgr::SetTempUseReportByITDMA(BOOL bOn)
{
    //----------------------------------------------------------------------------------------------------------------
    // Refer to ITU-R 1371-5 Ann.2 ******* 코스의 변경
    // (현재 헤딩 - 30초동안의 평균헤딩) 값이 5도를 초과할때, 보고간격은 계획된 SOTDMA 슬롯 또는 RATDMA access 슬롯 중
    // 하나를 이용하여 다음 150 슬롯 내에(see ?*******.1) 감소되기 시작해야한다.
    // 더 높은 보고율은 SOTDMA 를 보충하기 위해 ITDMA 를 사용함으로써 유지되어야한다.
    // -> 총 보고율 3배로
    //----------------------------------------------------------------------------------------------------------------
    //---------------------------------------------------------------------------------------------
    // 현재 일시적인 ITDMA 에 의한 속도 증가 동작중이더라도 현재 속도에 의해 다시 계산되어야 한다!
    //---------------------------------------------------------------------------------------------

    if(m_bTempUseReportByITDMA == bOn)
        return FALSE;

    BOOL bRet = TRUE;

    if(bOn)
    {
        if(m_fReportIntervalSec >= 3)
        {
            if(CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh() && CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh())
            {
                bRet = CLayerNetwork::getInst()->GetChPrimary()->SetTempUseReportByITDMA_Ch(TRUE) &&
                    CLayerNetwork::getInst()->GetChSecondary()->SetTempUseReportByITDMA_Ch(TRUE);
            }
            else if(CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh())
                bRet = CLayerNetwork::getInst()->GetChPrimary()->SetTempUseReportByITDMA_Ch(TRUE);
            else if(CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh())
                bRet = CLayerNetwork::getInst()->GetChSecondary()->SetTempUseReportByITDMA_Ch(TRUE);
        }
    }
    else
    {
        CLayerNetwork::getInst()->GetChPrimary()->SetTempUseReportByITDMA_Ch(FALSE);
        CLayerNetwork::getInst()->GetChSecondary()->SetTempUseReportByITDMA_Ch(FALSE);
        bRet = TRUE;
    }

    if(bRet)
        m_bTempUseReportByITDMA = bOn;
    return bRet;
}

/** 
 * @brief Get report interval for each channel
 * @param fNewReportIntervalTotal Total report interval
 * @param nNewTxChID1 Tx channel ID 1
 * @param nNewTxChID2 Tx channel ID 2
 * @param pfReportIntSecTotal Total report interval
 * @param pfReportIntSecCh1 Report interval for channel 1
 * @param pfReportIntSecCh2 Report interval for channel 2
 * @param puStaticIntSecCh1 Static report interval for channel 1
 * @param puStaticIntSecCh2 Static report interval for channel 2
 * @param puLongRangeIntSecCh1 Long range report interval for channel 1
 * @param puLongRangeIntSecCh2 Long range report interval for channel 2
 */

void CReportRateMgr::GetReportIntervalForEachCh(float fNewReportIntervalTotal, int nNewTxChID1, int nNewTxChID2, 
                                                float *pfReportIntSecTotal, float *pfReportIntSecCh1, float *pfReportIntSecCh2, 
                                                UINT16 *puStaticIntSecCh1, UINT16 *puStaticIntSecCh2, 
                                                UINT16 *puLongRangeIntSecCh1, UINT16 *puLongRangeIntSecCh2)
{
    *pfReportIntSecCh1 = 0;
    *pfReportIntSecCh2 = 0;

    *puStaticIntSecCh1 = 0;
    *puStaticIntSecCh2 = 0;

    *puLongRangeIntSecCh1 = 0;
    *puLongRangeIntSecCh2 = 0;

    if(nNewTxChID1 != AIS_CH_NUM_NONE && nNewTxChID2 != AIS_CH_NUM_NONE)
    {
        fNewReportIntervalTotal = MAX(fNewReportIntervalTotal, REPORT_INTERVALSEC_MIN);
        *pfReportIntSecTotal = fNewReportIntervalTotal;

        *pfReportIntSecCh1 = *pfReportIntSecCh2 = fNewReportIntervalTotal * 2.0f;
        *puStaticIntSecCh1 = *puStaticIntSecCh2 = STATIC_REPORT_INTSEC * 2;
    }
    else
    {
        fNewReportIntervalTotal = MAX(fNewReportIntervalTotal, REPORT_INTERVALSEC_MIN);
        *pfReportIntSecTotal = fNewReportIntervalTotal;

        if(nNewTxChID1 != AIS_CH_NUM_NONE)
        {
            *pfReportIntSecCh1 = fNewReportIntervalTotal;
            *puStaticIntSecCh1 = STATIC_REPORT_INTSEC;
        }
        else if(nNewTxChID2 != AIS_CH_NUM_NONE)
        {
            *pfReportIntSecCh2= fNewReportIntervalTotal;
            *puStaticIntSecCh2 = STATIC_REPORT_INTSEC;
        }
    }

    if(m_bReportRateDoubleMode)
    {
        *puStaticIntSecCh1 = *puStaticIntSecCh1 >> 1;
        *puStaticIntSecCh2 = *puStaticIntSecCh2 >> 1;
    }

    // long range 는 항상 두채널(CH 75, 76) 상에서 3분 마다 송신하도록 한다!
    *puLongRangeIntSecCh1 = *puLongRangeIntSecCh2 = LR_REPORT_INTERVAL_SEC * 2;   
}

void CReportRateMgr::RecalcReportInterval(float fSecRI)
{
    // 1채널 모드로 동작할때 예를 들어 AIS-1 은 TRX 모드이고 AIS-2 는 RX only 모드로 설정되었을때 : AIS-1 만으로 정해진 보고주기를 맞춰야한다.
    // -> 전체 보고주기와 채널별 보고주기 관련 변수들이 서로 다른 값을 갖게된다!
    // -> 채널 별로 보고주기 관련 변수들을 따라 관리해야 할듯!

    float fReportIntSecCh1, fReportIntSecCh2;
    UINT16 uStaticIntSecCh1, uStaticIntSecCh2;
    UINT16 uLongRangeIntSecCh1, uLongRangeIntSecCh2;

    GetReportIntervalForEachCh(fSecRI, CLayerNetwork::getInst()->GetChPrimary()->m_uChNumTx, CLayerNetwork::getInst()->GetChSecondary()->m_uChNumTx, 
                                &m_fReportIntervalSec, &fReportIntSecCh1, &fReportIntSecCh2, &uStaticIntSecCh1, &uStaticIntSecCh2, &uLongRangeIntSecCh1, &uLongRangeIntSecCh2);

    DEBUG_LOG("RecalcRR-0] setRI:%.1f, double:%d, RI : %.1f, txAvail : %d,%d, chRI : %.2f,%.2f, staticRI : %d,%d, LRRI : %d,%d\r\n",
            fSecRI, m_bReportRateDoubleMode, m_fReportIntervalSec, CLayerNetwork::getInst()->GetChPrimary()->IsTxAvailableCh(), CLayerNetwork::getInst()->GetChSecondary()->IsTxAvailableCh(),
            fReportIntSecCh1, fReportIntSecCh2, uStaticIntSecCh1, uStaticIntSecCh2, uLongRangeIntSecCh1, uLongRangeIntSecCh2);

    m_fReportRate = 60.0f / m_fReportIntervalSec;
    int nTotalNI = CAisLib::GetNIfromRR(m_fReportRate);

    CLayerNetwork::getInst()->GetChPrimary()->SetChReportInterval(nTotalNI, fReportIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetChReportInterval(nTotalNI, fReportIntSecCh2);

    BOOL bStaticRRchg = (uStaticIntSecCh1 != CLayerNetwork::getInst()->GetChPrimary()->m_dwStaticReportIntervalSec ||
                        uStaticIntSecCh2 != CLayerNetwork::getInst()->GetChSecondary()->m_dwStaticReportIntervalSec);
    CLayerNetwork::getInst()->GetChPrimary()->SetStaticReportIntervalSec(uStaticIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetStaticReportIntervalSec(uStaticIntSecCh2);

    if(bStaticRRchg)
        CLayerNetwork::getInst()->InitStaticReportSec();

    BOOL bLongRangeRRchg = (uLongRangeIntSecCh1 != CLayerNetwork::getInst()->GetChPrimary()->m_dwLongRangeReportIntervalSec ||
                            uLongRangeIntSecCh2 != CLayerNetwork::getInst()->GetChSecondary()->m_dwLongRangeReportIntervalSec);
    CLayerNetwork::getInst()->GetChPrimary()->SetLongRangeReportInterval(uLongRangeIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetLongRangeReportInterval(uLongRangeIntSecCh2);

    if(bLongRangeRRchg)
        CLayerNetwork::getInst()->InitLongRangeReportSec();

    DEBUG_LOG("RecalcRI] RI : %.2f, chRR : %.2f(%.2f), chRI : %.2f(%.2f), staticRI : %d(%d)\r\n",
            m_fReportIntervalSec,
            CLayerNetwork::getInst()->GetChPrimary()->m_fChReportRate, CLayerNetwork::getInst()->GetChSecondary()->m_fChReportRate,
            CLayerNetwork::getInst()->GetChPrimary()->m_fChReportIntervalSec, CLayerNetwork::getInst()->GetChSecondary()->m_fChReportIntervalSec,
            uStaticIntSecCh1, uStaticIntSecCh2);
    m_bFirstCalcRR = FALSE;
}

void CReportRateMgr::SetNewReportInterval(float fNewReportIntervalSecSO, int nNewTxChID1, int nNewTxChID2)
{
    //-------------------------------------------------------------------------------------------------------
    // OPPHASE_CHG_RR_PREP_FIRST 단계를 거쳐 보고율을 변경하려면 RecalcReportInterval 을 직접 호출하면 안되고
    // SetNewReportInterval 함수를 호출하여야한다.
    // 이후 ProcessPhaseChgReportRateFirst 함수 내에서 m_fNewReportIntervalSecSO 등의 변수들을 사용하기 위해
    //-------------------------------------------------------------------------------------------------------

    float fReportIntSecCh1, fReportIntSecCh2;
    UINT16 uStaticIntSecCh1, uStaticIntSecCh2;
    UINT16 uLongRangeIntSecCh1, uLongRangeIntSecCh2;
    GetReportIntervalForEachCh(fNewReportIntervalSecSO, nNewTxChID1, nNewTxChID2,
                            &m_fReportIntervalSec, &fReportIntSecCh1, &fReportIntSecCh2, &uStaticIntSecCh1, &uStaticIntSecCh2, &uLongRangeIntSecCh1, &uLongRangeIntSecCh2);

    m_fReportRate = 60.0f / m_fReportIntervalSec;

    CLayerNetwork::getInst()->GetChPrimary()->SetChNewReportIntervalSec(fReportIntSecCh1);                            // ProcessPhaseChgReportRateFirst 에서 SetChReportInterval(m_fNewReportIntervalSecSO) 을 호출하기 위해 임시 저장용
    CLayerNetwork::getInst()->GetChSecondary()->SetChNewReportIntervalSec(fReportIntSecCh2);

    CLayerNetwork::getInst()->GetChPrimary()->SetStaticReportIntervalSec(uStaticIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetStaticReportIntervalSec(uStaticIntSecCh2);

    CLayerNetwork::getInst()->GetChPrimary()->SetLongRangeReportInterval(uLongRangeIntSecCh1);
    CLayerNetwork::getInst()->GetChSecondary()->SetLongRangeReportInterval(uLongRangeIntSecCh2);

    DEBUG_LOG("SetNewReportInt] RI : %.2f, txCH : %d(%d), chRI : %.2f(%.2f), staticRI : %d(%d), LRRI : %d(%d)\r\n",
            m_fReportIntervalSec, nNewTxChID1, nNewTxChID2, fReportIntSecCh1, fReportIntSecCh2, uStaticIntSecCh1, uStaticIntSecCh2, uLongRangeIntSecCh1, uLongRangeIntSecCh2);
}

int CReportRateMgr::GetReportIntervalSec()
{
    return m_fReportIntervalSec;
}

float CReportRateMgr::GetReportRate()
{
    return m_fReportRate;
}

BOOL CReportRateMgr::IsRIValueValid(float fReportIntervalSec)
{
    return (REPORT_INTERVALSEC_MIN <= fReportIntervalSec && fReportIntervalSec <= REPORT_INTERVALSEC_MAX);
}

BOOL CReportRateMgr::CheckCurRRValid()
{
    BOOL bRet = m_fReportRate > 0 && IsRIValueValid(m_fReportIntervalSec);
    return bRet;
}

BOOL CReportRateMgr::IsReportRateForSOTDMA(float fReportIntervalSec)
{
    // ITDMA : 보고율이 2 미만일때 == 보고간격이 30초 초과일때
    // SOTDMA : 보고율이 2 이상일때 == 보고 간격이 30초 이하일때
    return (fReportIntervalSec <= 30);                // SOTDMA : 보고율이 2 이상일때 == 보고 간격이 30초 이하일때
}

BOOL CReportRateMgr::IsReportRateForSOTDMA()
{
    return IsReportRateForSOTDMA(m_fReportIntervalSec);
}

// Return을 float로 변경하지 않으면 3분 시간값 마이너스값으로 인식하여 송신안됨.
float CReportRateMgr::GetReportIntSecBySpeed()
{
    const int nSOG = cShip::getOwnShipInst()->xDynamicData.nSOG;

    if (CSetupMgr::getInst()->IsAisAClass())
    {
        if(nSOG == NMEA_SOG_NULL)
        {
            if(IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus))
                return  180.0f; // 3 min
            return 10.0f;
        }
        if(nSOG < 140)          // below 14 knots, 10 sec
            return 10.0f;
        if(nSOG < 230)          // below 23 knots, 6 sec
            return 6.0f;
        return 2.0f;            // elsewhere, 2 sec
    }

    // Class B
    if(nSOG == NMEA_SOG_NULL || nSOG < 20)  // below 2 knots, 3 min
        return NOMINAL_REPORT_180SEC;
    if(nSOG < 140)                          // below 14 knots, 30 sec
        return NOMINAL_REPORT_30SEC;
    if(nSOG < 230)                          // below 23 knots, 15 sec
        return NOMINAL_REPORT_15SEC;
    return NOMINAL_REPORT_5SEC;             // elsewhere, 5 sec
}

float CReportRateMgr::GetReportIntSecNextLonger()
{
    if (CSetupMgr::getInst()->IsAisAClass())
    {
        if(m_fReportIntervalSec <= 2)
            return 6.0f;        // 6 sec
        if(m_fReportIntervalSec <= 6)
            return 10.0f;       // 10 sec
        return 180.0f;          // 3 min
    }

    // Class B
    if(m_fReportIntervalSec <= NOMINAL_REPORT_5SEC)
        return NOMINAL_REPORT_15SEC;    // 15 sec
    if(m_fReportIntervalSec <= NOMINAL_REPORT_15SEC)
        return NOMINAL_REPORT_30SEC;    // 30 sec
    return NOMINAL_REPORT_180SEC;       // 3 min
}

float CReportRateMgr::GetReportIntSecNextShorter()
{
    if (CSetupMgr::getInst()->IsAisAClass())
    {
        if(m_fReportIntervalSec <= 6)
            return 2.0f;        // 2 sec
        if(m_fReportIntervalSec <= 10)
            return 6.0f;        // 6 sec
        return 10.0f;           // 10 sec
    }

    // Class B
    if(m_fReportIntervalSec <= NOMINAL_REPORT_15SEC)
        return NOMINAL_REPORT_5SEC;     // 5 sec
    if(m_fReportIntervalSec <= NOMINAL_REPORT_30SEC)
        return NOMINAL_REPORT_15SEC;    // 15 sec
    return NOMINAL_REPORT_30SEC;        // 10 sec
}

BOOL CReportRateMgr::SetReportRateDoubleMode(BOOL bReportRateDoubleMode)
{
    BOOL bRet = (m_bReportRateDoubleMode != bReportRateDoubleMode);
    m_bReportRateDoubleMode = bReportRateDoubleMode;
    return bRet;
}

float CReportRateMgr::CheckAutoModeReportInterval(BOOL bCheckSpdReduce, BOOL bDoChangeRR, BOOL *pRet)
{
    static float fSpeedReduceIntSec = 0;
    static DWORD dwSpeedReduceStartSec = 0;
    static float fOldReportIntSecBySpeed = 0;

    float fReportIntSecBySpeed    = 0;
    float fFinalRIsecSO = -1;

    if(pRet)
    {
        *pRet = FALSE;
    }

    // 자선이 세마포어일때는 보고주기 2초
    if(CSyncMgr::getInst()->IsOwnShipSemaphoreMode())
    {
        fFinalRIsecSO = 2;
    }
    else
    {
        //----------------------------------------------------------------------------------------------------
        // Refer to 1371-5 Ann.2 4.3 Changing reporting interval
        // Refer to 1371-5 Ann.2 *******
        // 속도 증가 시 즉시 Change RR Phase 수행, 속도 감소시 감소된 속도로 3분 유지 후 Change RR phase 수행
        //----------------------------------------------------------------------------------------------------
        // Ivan test report p.136 IEC-61993-2 ********
        // Ivan test report p.141 IEC-61993-2 ********
        // Ivan test report p.305 IEC-61993-2 ********.1
        // Ivan test report p.628 IEC-61993-2 ********.1 TT16-6-7-9-Ed2.scn
        //----------------------------------------------------------------------------------------------------
        fReportIntSecBySpeed = GetReportIntSecBySpeed();

        if (CSetupMgr::getInst()->IsAisAClass())
        {
            //----------------------------------------------------------------------------
            // if NavStatus is Anchor/Moored, speed is not moving faster than 3 knots
            //----------------------------------------------------------------------------
            if(    IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus) 
                && cShip::getOwnShipInst()->xDynamicData.nSOG <= SOG_THRESHOLD_DONTMOVE)
            {
                fReportIntSecBySpeed = 180.0f;
            }

            fFinalRIsecSO = fReportIntSecBySpeed;

            //----------------------------------------------------------------------------
            // if NavStatus is Anchor/Moored, speed is moving faster than 3 knots
            //----------------------------------------------------------------------------
            if(IsNavStatusNotUnderway(cShip::getOwnShipInst()->xNavData.uNavStatus) 
                && cShip::getOwnShipInst()->xDynamicData.nSOG > SOG_THRESHOLD_DONTMOVE)
                fFinalRIsecSO = 10.0f;
        }
        else
        {
            fFinalRIsecSO = fReportIntSecBySpeed;
        }

        if(bCheckSpdReduce)
        {
            // 속도 감소(==보고간격 증가)
            if(fReportIntSecBySpeed > m_fReportIntervalSec)
            {
                if(fReportIntSecBySpeed > fOldReportIntSecBySpeed)
                {
                    fSpeedReduceIntSec = fReportIntSecBySpeed;
                    // 속도 감소하기 시작하는 시점(==보고간격 증가하기 시작하는 시점) 저장
                    dwSpeedReduceStartSec= cTimerSys::getInst()->GetCurTimerSec();
                }

                if(fSpeedReduceIntSec > 0 && fReportIntSecBySpeed == fSpeedReduceIntSec)
                {
                    // 보고주기 증가 이후 3분 경과 시 반영
                    if(cTimerSys::getInst()->GetTimeDiffSec(dwSpeedReduceStartSec) > 180)
                    {
                        ;
                    }
                    else
                    {
                        // 속도 감소 중에는 이전 보고율 유지
                        fFinalRIsecSO = m_fReportIntervalSec;
                    }
                }
            }
            else if(fReportIntSecBySpeed < m_fReportIntervalSec)    // 속도 증가(==보고간격 감소)
            {
                if(fSpeedReduceIntSec > 0)
                {
                    fSpeedReduceIntSec = 0;                         // End of decreasing
                }
            }
        }

        if(CSetupMgr::getInst()->IsAisAClass() && IsReportRateForSOTDMA())
        {
            //-------------------------------------------------------------------------------------------------------
            // Refer to ITU-R 1371-5 Ann.2 *******
            // The higher Rr should be maintained by using ITDMA to complement SOTDMA scheduled
            // transmissions in order to derive the desired Rr. When 5° is exceeded, the reporting interval should
            // be decreased beginning with a broadcast within the next 150 slots (see § *******.1) using either
            // a scheduled SOTDMA slot, or a RATDMA access slot (see § *******).
            //-------------------------------------------------------------------------------------------------------
            BOOL bHdgDiffAbove = cShip::getOwnShipInst()->xDynamicData.nHDG != NMEA_HDG_NULL && cShip::getOwnShipInst()->xDynamicData.fHdgAvg != NMEA_HDG_NULL &&
                                (CAisLib::GetDiffAbsYaw(cShip::getOwnShipInst()->xDynamicData.fHdgAvg, cShip::getOwnShipInst()->xDynamicData.nHDG) >= 5);

            if(bHdgDiffAbove)
            {
                //------------------------------------------------------------------------------------------------
                // "일시적인 ITDMA 에 의한 속도 증가" 시작 체크
                // 현재 "일시적인 ITDMA 에 의한 속도 증가"가 동작중이더라도 현재 속도에 의해 다시 계산되어야 한다!
                //------------------------------------------------------------------------------------------------
                SetTempUseReportByITDMA(TRUE);
            }

            if(m_bTempUseReportByITDMA)
            {
                //------------------------------------------------------------------------------------------------
                // "일시적인 ITDMA 에 의한 속도 증가" 중단 체크
                //------------------------------------------------------------------------------------------------
                if(!bHdgDiffAbove)
                {
                    if(cShip::getOwnShipInst()->xDynamicData.nHDG == NMEA_HDG_NULL ||            // HDG 정보가 invalid 이면 즉시 중지
                        SysGetDiffTimeScnd(m_dwHdgDiffAboveTick) > 20)
                    {
                        SetTempUseReportByITDMA(FALSE);
                    }
                }
            }

            if(bHdgDiffAbove)
            {
                m_dwHdgDiffAboveTick = SysGetSystemTimer();
            }
        }
    }

    if(m_bReportRateDoubleMode)
        fFinalRIsecSO /= 2;                        // Transitional mode 시 보고율 2배 (보고간격 1/2 배)

    fOldReportIntSecBySpeed = fReportIntSecBySpeed;

    if(bDoChangeRR && fFinalRIsecSO > 0 && (!CheckCurRRValid() || fFinalRIsecSO != m_fReportIntervalSec))
    {
        if(CLayerNetwork::getInst()->CheckAssignedModeRunning())
        {
            //-----------------------------------------------------------------------------------------
            // refer to IEC-61993-2 ********
            // 코스, 속도, navStatus 로 인해 더 짧은 보고간격이 요구될때 : 할당모드 종료, 자동모드 복귀
            //-----------------------------------------------------------------------------------------
            if(fFinalRIsecSO < m_fReportIntervalSec)
            {
                CLayerNetwork::getInst()->RunReturnToAutoMode();

                if(pRet)
                {
                    *pRet = TRUE;
                }
            }
        }
        else
        {
            if(CLayerNetwork::getInst()->IsChangingReportRateAvailable())
            {
                // Report Rate 변경
                CLayerNetwork::getInst()->RunPhaseChangeReportRate(CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, CROSMgr::getInst()->m_sChSetup.uChannelIdA, CROSMgr::getInst()->m_sChSetup.uChannelIdB, 
                                                        fFinalRIsecSO, CLayerNetwork::getInst()->m_nOpMode, CLayerNetwork::getInst()->m_nOpMode, CLayerNetwork::getInst()->m_bAssignedModeBy);

                if(pRet)
                {
                    *pRet = TRUE;
                }
            }
        }
    }

    return fFinalRIsecSO;
}

void CReportRateMgr::RunPeriodicallyReportRateMgr()
{
    static DWORD dwCheckSec = 0;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > 1)
    {
        {
            BOOL bCheckSpdReduce= (CLayerNetwork::getInst()->m_nOpPhase == OPPHASE_ROUTINE);
            BOOL bDoChangeRR    = (CLayerNetwork::getInst()->m_nOpPhase == OPPHASE_ROUTINE);
            CheckAutoModeReportInterval(bCheckSpdReduce, bDoChangeRR);                    // 보고주기 체크 및 변경
        }

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}
