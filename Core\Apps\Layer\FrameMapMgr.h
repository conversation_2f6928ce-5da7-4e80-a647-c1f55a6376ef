#ifndef __FRAMEMAPMGR_H__
#define __FRAMEMAPMGR_H__

#include "DataType.h"
#include "AllConst.h"
#include "AisMsg.h"
#include "AisLib.h"
#include "ChannelMgr.h"
#include "VdlRxMgr.h"

class CChannelMgr;

class CFrameMapMgr : public CAisLib
{
public:
    CFrameMapMgr(CChannelMgr *pChannel);
    ~CFrameMapMgr();

public:
    CChannelMgr       *m_pChannel;
    FRAMEMAP_SLOTDATA *m_pFrameMap;
    INT16              m_nFrameMapStartSlotID;

public:
    void    ClearFrameMap();
    void    ShiftFrameMap(INT16 nShiftOffset);
    WORD    GetShiftedMapSlotID(WORD wMapSlotID);
    WORD    GetShiftedMapSlotID(WORD wFrameID, WORD wSlotID);
    WORD    GetSlotIdFromFrameSlotID(WORD wMapSlotID);

    FRAMEMAP_SLOTDATA*    GetSlotDataPtr(WORD wFrSlotID);
    void    PrepToChangeFrMMSI(UINT uNewMMSI);

protected:
    void    ClearFrameMapSlotData(FRAMEMAP_SLOTDATA *pSlotPtr);
    void    ClearFrameMapSlotData(int nFrSlotID);
    void    SetFrameMapSlotData(BOOL bCheckProtect, WORD wFrSlotID, INT8 nNumSlots, UINT uMMSI, BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme,
                                UINT16 uMsgID, BOOL bStartSlot, UINT uSlotOffset, BOOL bItdmaKeepFlag, BOOL bProtectAssignedModeSlot, BYTE nDataIdx, BOOL bFAtoSO, WORD wTxChNum=AIS_CH_NUM_NONE);
public:
    void    SetFrameMapOneMsg(BOOL bCheckProtect, WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, BYTE bStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme, UINT16 uMsgID, UINT16 uSlotOffset, BOOL bItdmaKeepFlag, BOOL bProtectAssignedModeSlot, BYTE nDataIdx, BOOL bFAtoSO);
    void    SetFrameMapOneMsgColumn(BOOL bCheckProtect, const WORD wFrSlotID, INT8 nNumSlot, UINT uMMSI, BYTE bSlotStatus, int nSlotTimeOut, BYTE bAccessScheme, BYTE bCommStateScheme, UINT16 uMsgID, UINT16 uSlotOffset, BOOL bItdmaKeepFlag, BOOL bProtectAssignedModeSlot, BYTE nDataIdx, BOOL bFAtoSO);

    void    FreeFrameMapSlotRowByCnt(WORD wStartSlotID, int nNumSlot);
    void    FreeFrameMapSlotRowByRange(WORD wStartSlotID, WORD wEndSlotID);
    int     FreeFrameMapOneMsg(WORD wFrSlotID);
    void    FreeFrameMapMsgColRow(const WORD wFrSlotIDToFree, int uNumSlot, UINT uMMSI);

    void    UpdateFrameMapSOTDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotNo, INT8 nNumSlot, UINT uMMSI, DWORD dwSyncState, DWORD dwTimeOut, DWORD dwSubMsg);
    void    UpdateFrameMapITDMA(UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotNo, INT8 nNumSlot, UINT uMMSI, DWORD dwSyncState, DWORD dwSlotInc, DWORD dwNumSlot, DWORD dwKeepFlag, BYTE cSlotStatus);
    void    UpdateFrameMapFATDMA(BOOL bCheckBastStStat, UINT16 uMsgID, WORD wFrameID, WORD wRcvSlotID, UINT uMMSI, DWORD dwOffset, DWORD dwNumSlot, DWORD dwTimeOut, int nIncrement, BOOL bFAtoSO=FALSE);

    BOOL    UpdateAssignedSlotsFATDMAtoSOTDMA(UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt);
    BOOL    UpdateAssignedSlotsSOTDMA(WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD nIncrement, INT8 nTimeOutCnt);
    void    RestoreAssginedSlotsSOTDMAtoFATDMA(UINT uBaseStMMSI, WORD wStartSlotID);
    WORD    GetInternalAllocatedSlot(WORD wStartSlot, INT16 nNumSlotToScan, BYTE bTdmaScheme, WORD *pwOutputSlot, int nMsgID=AIS_MSG_NO_ID_UNDEFINED, BOOL bExceptTmoZeroSlot=FALSE);
    WORD    GetInternalAllocatedSlot_SO(WORD wStartSI, int nSizeSI, WORD *pwOutputSlot, int nMsgID=AIS_MSG_NO_ID_UNDEFINED, BOOL bExceptTmoZeroSlot=FALSE, BOOL bFindMsg3SO=FALSE);
    WORD    GetInternalAllocatedSlot_IT(WORD wStartSI, int nSizeSI, WORD *pwOutputSlot, int nMsgID=AIS_MSG_NO_ID_UNDEFINED, BOOL bExceptTmoZeroSlot=FALSE);
    WORD    GetSoTdmaSlotForItdmaTx(WORD wStartSI, int nSizeSI, WORD *pwOutputSlot);

    BOOL    IsIntTxReservedSlot(FRAMEMAP_SLOTDATA *pSlotData);
    BOOL    IsInternalAllocSlot(FRAMEMAP_SLOTDATA *pSlotPtr);
    BOOL    IsInternalAllocSlotSO(FRAMEMAP_SLOTDATA *pSlotPtr, BOOL bCheckMsgID=FALSE);
    BOOL    IsSlotAvailableForMMSI(WORD wFrSlotID, int nNumSlot, UINT uMMSI);
    void    SetLastTxFrameAndFree(WORD wTxSlotId, int nNumSlotCol=NUM_SLOT_PER_FRAME, BOOL bCheckMsgID=FALSE);

    void    SetLastTxSlotWithRange(int nStartSlot, int nEndSlot, BOOL bExceptBorder);

    void    FrChangeFrMapPosMsgMode(WORD wFrSlotIDStart, int nOpMode);
    void    FrChangePosMsgModeTimeOut(WORD wFrSlotIDStart, int nNumSlotsToCheck, int nTimeOut);

    static  BOOL    CheckSlotIntAlloc(FRAMEMAP_SLOTDATA *pSlot);
    BOOL    CheckSlotIntAlloc(WORD wFrSlotID);

    static  BOOL    CheckSlotExtAlloc(FRAMEMAP_SLOTDATA *pSlot);
    BOOL    CheckSlotExtAlloc(WORD wFrSlotID);

    void    ProcessFrameChg_FrameMap();
    void    PrepToChgCh(WORD wFrSlotIDStart);
};
#endif//__FRAMEMAPMGR_H__
