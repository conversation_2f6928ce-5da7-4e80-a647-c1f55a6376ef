#ifndef __LAYERTRANSPORT_H__
#define __LAYERTRANSPORT_H__

#include "DataType.h"
#include "AllConst.h"

class CPI;

class CLayerTransport
{
public:
    CLayerTransport();
    ~CLayerTransport();

    static std::shared_ptr<CLayerTransport> getInst() {
        static std::shared_ptr<CLayerTransport> pInst = std::make_shared<CLayerTransport>();
        return pInst;
    }

public:
    void    Initialize();
    void    InitAllChannels();
};

#endif//__LAYERTRANSPORT_H__
