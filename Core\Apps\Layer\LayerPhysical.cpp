#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
//#include "ChannelMgr.h"
#include "AisLib.h"
#include "LayerNetwork.h"
#include "ADC.h"
#include "DAC.h"
#include "GPIOExt.h"
#include "PLL.h"
#include "Timer.h"
#include "RosMgr.h"
#include "SetupMgr.h"
#include "AisModem.h"
#include "AlarmMgr.h"
#include "BuiltInTestMgr.h"
#include "TestModeMgr.h"
#include "MKD.h"
#include "LayerPhysical.h"


#define RAMPUP_NUM_STEP        (RAMPUP_TIME_SAMPLECNT)
#define RAMPUP_SCALE_CNT    ((RAMPUP_TIME_SAMPLECNT) / RAMPUP_NUM_STEP)


CLayerPhysical::CLayerPhysical()
{
    m_uTxPowerMode              = AIS_TX_POWER_HIGH;

    m_nTxPwrLevelToSet          = 0;
    m_fRampUpStep               = 0;
    m_nRampUpCnt                = 0;
    m_fLevelSum                 = 0;

    m_nCurPwrLevel              = 0;

    m_bTxRfOn                   = FALSE;
    m_dwTxRfOnSec               = 0;

    m_bTxRunning                = FALSE;
    m_nPrepTxStep               = PREPTX_STEP_NONE;

    m_bVswrMonitoringByMKD      = FALSE;
    m_wFwdPowerAdValue          = 0;
    m_wRefPowerAdValue          = 0;

    m_bEnableCheckVswr          = FALSE;
    m_dwVswrCheckEnableTick     = 0;
    m_dwVswrCheckRunTick        = 0;
    m_bVswrErrorOccurred        = FALSE;

    m_bEnableCheckRxMalFuncRX1  = FALSE;
    m_dwRxLockStartTickSec1     = 0;
    m_bEnableCheckRxMalFuncRX2  = FALSE;
    m_dwRxLockStartTickSec2     = 0;

    m_bTxHwShutdownOccured      = FALSE;
    m_dwTxHwPwrStartTick        = 0;

    m_nRegTxFreq                = 0;
    m_nSetModemTxFreq           = 0;
    m_dwTxFreqTick              = 0;
}

CLayerPhysical::~CLayerPhysical()
{
}

void CLayerPhysical::InitHW()
{
    TurnTxOff(TRUE);
    SetTxRfOn(FALSE);
}

BOOL CLayerPhysical::SetRxLocalFreq(int nHwLocalRcvrID, UINT uChNum)
{
    int nFrequency = CAisLib::GetAisFreqByChannelNo(uChNum);
    if(nFrequency <= 0)
        return FALSE;

    switch(nHwLocalRcvrID)
    {
    case AIS_HW_RX_LOCAL_ID_RX1:
        m_bEnableCheckRxMalFuncRX1 = FALSE;
        cPllSYS::getInstPLL1()->SetCh1Frequency(nFrequency);
        m_dwRxLockStartTickSec1 = cTimerSys::getInst()->GetCurTimerSec();
        break;
    case AIS_HW_RX_LOCAL_ID_RX2:
        m_bEnableCheckRxMalFuncRX2 = FALSE;
        cPllSYS::getInstPLL2()->SetCh1Frequency(nFrequency);
        m_dwRxLockStartTickSec2 = cTimerSys::getInst()->GetCurTimerSec();
        break;
    case AIS_HW_RX_LOCAL_ID_RX3:
        // RX2와 동일한 PLL 사용하므로, DSC 구현전까지 막음.
        //m_bEnableCheckRxMalFuncRX2 = FALSE;
        //cPllSYS::getInstPLL3()->SetCh1Frequency(nFrequency);
        //m_dwRxLockStartTickSec2 = cTimerSys::getInst()->GetCurTimerSec();
        break;
    }
    return TRUE;
}

BOOL CLayerPhysical::SetTxPowerMode(UINT8 uTxPower)
{
    //------------------------------------------------
    // uTxPower : AIS_TX_POWER_LOW / AIS_TX_POWER_HIGH
    //------------------------------------------------
    if(uTxPower == AIS_TX_POWER_HIGH || uTxPower == AIS_TX_POWER_LOW)
    {
        if(m_uTxPowerMode != uTxPower)
        {
            m_uTxPowerMode = uTxPower;
            CMKD::getInst()->SendPowerMode();

            DEBUG_LOG("SetTxPwrModePhy] power:%d, setup1:%d(%d),%d(%d), setup2:%d,%d, s:%d\r\n",
                uTxPower, CSetupMgr::getInst()->GetTxPowerLevelCh1High(), CSetupMgr::getInst()->GetTxPowerLevelCh1Low(),
                CSetupMgr::getInst()->GetTxPowerLevelCh2High(), CSetupMgr::getInst()->GetTxPowerLevelCh2Low(), cTimerSys::getInst()->GetCurTimerSec());
        }
        return TRUE;
    }

    return FALSE;
}

WORD CLayerPhysical::GetTxPowerLevel()
{
    WORD pw_level;
    DWORD tx_freq  = cAisModem::getInst()->GetAisTxFrequency();

    if(!tx_freq)
        return 0;

    if(tx_freq < VCO_BASIC_FREQ)
    {
        //-------------------------
        // Low freq. power level
        //-------------------------
        if(m_uTxPowerMode == AIS_TX_POWER_HIGH)
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh1High();
        else
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh1Low();
    }
    else
    {
        //-------------------------
        // High freq. power level
        //-------------------------
        if(m_uTxPowerMode == AIS_TX_POWER_HIGH)
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh2High();
        else
            pw_level = CSetupMgr::getInst()->GetTxPowerLevelCh2Low();
    }

    DEBUG_LOG("TxStep-GetTxPwr] setup1:%d,%d, setup2:%d,%d, pwrHigh:%d, level:%d\r\n",
            CSetupMgr::getInst()->GetTxPowerLevelCh1High(), CSetupMgr::getInst()->GetTxPowerLevelCh1Low(),
            CSetupMgr::getInst()->GetTxPowerLevelCh2High(), CSetupMgr::getInst()->GetTxPowerLevelCh2Low(),
            m_uTxPowerMode, pw_level);

    return pw_level;
}

void CLayerPhysical::SetTxPowerRamp(int nLevel)
{
    m_nCurPwrLevel    = nLevel;

    if(!CTestModeMgr::getInst()->IsRunTxLoopbackTest())
    {
        cDac::getInst()->SetDAC1Data((HWORD)m_nCurPwrLevel);
    }
}

void CLayerPhysical::ReserveTxFreq(int nFrequency)
{
    if(nFrequency > 0)
        m_nSetModemTxFreq = nFrequency;

    m_nRegTxFreq = nFrequency;
    m_dwTxFreqTick = (nFrequency > 0 ? SysGetSystemTimer() : 0);
}

BOOL CLayerPhysical::RunTransmitStep1()
{
    BOOL bRet = FALSE;
    if(m_nRegTxFreq > 0)
    {
        if(cAisModem::getInst()->GetAisTxFrequency() != m_nRegTxFreq)
            bRet = TRUE;

        PrepToTransmitStep1(m_nRegTxFreq);

        ReserveTxFreq(0);
    }
    return bRet;
}

void CLayerPhysical::PrepToTransmitStep1(int nFrequency)
{
    if(!nFrequency)
        return;

    //------------------------------------
    // 다음 송신 한 슬롯 전에서 호출됨
    //------------------------------------

    if(m_nPrepTxStep == PREPTX_STEP_NONE)
    {
        if(nFrequency > 0)
        {
            cAisModem::getInst()->SetAisTxFrequency(nFrequency);
        }

        RecalcRampUpPrepSet();

        m_nPrepTxStep = PREPTX_STEP1;
        m_bTxRunning = TRUE;
    }
}

void CLayerPhysical::SetTxRfOn(BOOL bOn)
{
    m_bTxRfOn = bOn;

    if(bOn)
        m_dwTxRfOnSec = cTimerSys::getInst()->GetCurTimerSec();
    else
        m_dwTxRfOnSec = 0;
}

void CLayerPhysical::PrepToTransmitStep2()
{
    if(m_nPrepTxStep == PREPTX_STEP1)
    {
        // Turn off RX when transmit data if not loopback test mode 
        if(!CTestModeMgr::getInst()->IsRunTxLoopbackTest())
            SetGpioTurnOnRx(FALSE);

        SetGpioLed_Tx(TRUE);

        SetTxRfOn(TRUE);

        SetGpioTxPowerOn(TRUE);

        m_nPrepTxStep = PREPTX_STEP2;
    }
}

void CLayerPhysical::PrepToTransmitStep3()
{
    //------------------------------------------------
    // packet 시작 전 1 ms 전에서 호출됨
    // PrepToTransmitStep2 호출 후 0.4 us 후 호출되어야 함!
    //------------------------------------------------

    if(m_nPrepTxStep == PREPTX_STEP2)
    {
        SetGpioTurnOnTx(TRUE);
        InitToRampUp();

        m_nPrepTxStep = PREPTX_STEP3;
    }
    else if(m_nPrepTxStep == PREPTX_STEP3)
        RunToRampUp();
}

void CLayerPhysical::RecalcRampUpPrepSet()
{
    m_nTxPwrLevelToSet = GetTxPowerLevel();

    m_fRampUpStep = 0;
    if(m_nTxPwrLevelToSet > 0)
        m_fRampUpStep = (float)m_nTxPwrLevelToSet / RAMPUP_NUM_STEP;

    DEBUG_LOG("TxStep-RecalcRampUpPrepSet] toSet:%d, step:%.2f, numStep:%d, scaleCnt:%d\r\n",
            m_nTxPwrLevelToSet, m_fRampUpStep, RAMPUP_NUM_STEP, RAMPUP_SCALE_CNT);
}

void CLayerPhysical::InitToRampUp()
{
    m_nRampUpCnt    = 0;
    m_fLevelSum        = 0;
}

void CLayerPhysical::RunToRampUp()
{
    if(m_nCurPwrLevel < m_nTxPwrLevelToSet)
    {
        if(++m_nRampUpCnt >= RAMPUP_SCALE_CNT)
        {
            m_nRampUpCnt = 0;

            m_fLevelSum += m_fRampUpStep;
            m_fLevelSum = MIN(m_fLevelSum, m_nTxPwrLevelToSet);

            SetTxPowerRamp((int)m_fLevelSum);

            DEBUG_LOG("TxStep-RunRampUp] toSet:%d, step:%.2f, cur:%d, sum:%.2f\r\n", m_nTxPwrLevelToSet, m_fRampUpStep, m_nCurPwrLevel, m_fLevelSum);
        }
    }
    else
    {
        m_nPrepTxStep = PREPTX_STEP4;

        m_bEnableCheckVswr        = TRUE;
        m_dwVswrCheckEnableTick = SysGetSystemTimer();

        DEBUG_LOG("TxStep-RunRampUp] pwrLevel, cur:%d, sum:%.2f\r\n", m_nCurPwrLevel, m_fLevelSum);
    }
}

void CLayerPhysical::TurnTxOff(BOOL bUnconditionally)
{
    DEBUG_LOG("TxStep-TurnTxOff] pwrLevel, cur:%d\r\n", m_nCurPwrLevel);

    if(bUnconditionally || m_bTxRunning)
    {
        m_nPrepTxStep = PREPTX_STEP_NONE;
        m_bTxRunning  = FALSE;

        m_bEnableCheckVswr        = FALSE;

        InitToRampUp();
        SetTxPowerRamp(0);

        SetGpioTurnOnTx(FALSE);
        SetGpioTxPowerOn(FALSE);

        SetTxRfOn(FALSE);

        SetGpioTurnOnRx(TRUE);

        SetGpioLed_Tx(FALSE);
    }
}

BOOL CLayerPhysical::CheckReadyToTransmit()
{
    if(m_nPrepTxStep != PREPTX_STEP3)
        return TRUE;

    BOOL bRet = ((CTestModeMgr::getInst()->IsRunTxLoopbackTest() || !GetGpioTurnOnRx()) &&
                GetGpioTurnOnTx() &&
                GetGpioTxPowerOn()
                );

    return bRet;
}

void CLayerPhysical::SetVswrMonitoringByMKD(BOOL bEnable)
{
    m_bVswrMonitoringByMKD = bEnable;
}

BOOL CLayerPhysical::CheckVswrOnTx()
{
    BOOL bCheckDone = FALSE;

    if(m_bEnableCheckVswr)
    {
        m_wFwdPowerAdValue = cAdc::getInstAdc2()->GetADCData(ADC_2_FWD_VSWR_CH_NO);
        m_wRefPowerAdValue = cAdc::getInstAdc2()->GetADCData(ADC_2_REV_VSWR_CH_NO);
        m_dwVswrCheckRunTick = SysGetSystemTimer();
        m_bVswrErrorOccurred = CBuiltInTestMgr::getInst()->IsVswrFail();

        if(!CTestModeMgr::getInst()->IsTestModeRunning() && m_bVswrErrorOccurred)
        {
            /* 
            TurnTxOff(TRUE);
            */
        }

        //SetGpioLed_Error(m_bVswrErrorOccurred);
        bCheckDone = TRUE;
    }

    return bCheckDone;
}

BOOL CLayerPhysical::IsTxHwShutdownOccurred()
{
    return m_bTxHwShutdownOccured;
}

void CLayerPhysical::RunPeriodicallyPhysical()
{
    if(!m_bEnableCheckRxMalFuncRX1)
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwRxLockStartTickSec1) > 1)
            m_bEnableCheckRxMalFuncRX1 = TRUE;
    }

    if(!m_bEnableCheckRxMalFuncRX2)
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(m_dwRxLockStartTickSec2) > 1)
            m_bEnableCheckRxMalFuncRX2 = TRUE;
    }
}

void CLayerPhysical::RunCriticalPhysical()
{
    static DWORD dwCheckTick = 0;
    const int dwCheckTermMs = CTestModeMgr::getInst()->IsTestModeRunning() ? 200 : 5;

    if(SysGetDiffTimeMili(dwCheckTick) > dwCheckTermMs)
    {
        if(CheckVswrOnTx())
        {
            CMKD::getInst()->SendDataTxSWR();
        }

        dwCheckTick = SysGetSystemTimer();
    }
}
