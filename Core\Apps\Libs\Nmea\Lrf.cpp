/**
 * @file    Lrf.cpp
 * @brief   Lrf class implementation
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include <string>
#include <stdio.h>
#include "AisLib.h" 
#include "SysLog.h"
#include "RosMgr.h"
#include "Lrf.h"

/******************************************************************************
*
* LRF - Long Range Function
*
* $--LRF,x,xxxxxxxxx,c--c,c--c,c--c*hh<CR><LF>
*        | |         |    |    |
*        1 2         3    4    5
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of requester
* 3.  Name of requester, 1 to 20 character string
* 4.  Function request , 1 to 26 characters
* 5.  Function reply status
*
******************************************************************************/
// Define static member variables
int8_t CLrf::m_nSequentialId = 0;

CLrf::CLrf() : CSentence()
{

}
    
/**
 * @brief Parse the sentence
 * @param pszSentence The sentence to be parsed
 * @return True if the sentence is parsed successfully, false otherwise
 */
bool CLrf::Parse(const char *pszSentence)
{
    UINT    uLrfReqMMSI;
    char    pstrLrfReqName[30];
    char    pstrLrfFuncReq[30];
    char    pstrLrReplyStat[30];
    WORD    wReqSeqNum;
    char    pstrSubData[30];

    CSentence::GetFieldString(pszSentence, 1, pstrSubData, sizeof(pstrSubData));
    if(strlen(pstrSubData) != 1)
    {
        return FALSE;
    }
    wReqSeqNum = atoi(pstrSubData);

    CSentence::GetFieldString(pszSentence, 2, pstrSubData, sizeof(pstrSubData));
    uLrfReqMMSI = atoi(pstrSubData);

    CSentence::GetFieldString(pszSentence, 3, pstrLrfReqName, sizeof(pstrLrfReqName));
    CSentence::GetFieldString(pszSentence, 4, pstrLrfFuncReq, sizeof(pstrLrfFuncReq));
    CSentence::GetFieldString(pszSentence, 5, pstrLrReplyStat, sizeof(pstrLrReplyStat));

    if(strlen(pstrLrReplyStat) <= 0)
    {
        return FALSE;
    }

    return true;
}

/**
 * @brief Make the LRF sentence
 * @param pszSentence The sentence to be made
 * @return The length of the sentence
 */
int32_t CLrf::MakeSentence(char *pszSentence)
{
    return strlen(pszSentence);
}




