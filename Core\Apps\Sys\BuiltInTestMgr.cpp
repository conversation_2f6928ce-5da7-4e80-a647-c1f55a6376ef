#include <stdio.h>
#include "SysConst.h"
#include "SysLib.h"
#include "ADC.h"
#include "GPIOExt.h"
#include "LayerPhysical.h"
#include "SetupMgr.h"
#include "GnssInternal.h"
#include "AisModem.h"
#include "LayerNetwork.h"
//#include "ChannelMgr.h"
#include "MKD.h"
#include "TestModeMgr.h"
#include "BuiltInTestMgr.h"

CBuiltInTestMgr::CBuiltInTestMgr()
{
    m_bErrorROM    = FALSE;
    m_bErrorEEPROM = FALSE;
}

CBuiltInTestMgr::~CBuiltInTestMgr()
{

}

void CBuiltInTestMgr::SetROMError(BOOL bError)
{
    m_bErrorROM = bError;
}

void CBuiltInTestMgr::SetEEPROMError(BOOL bError)
{
    m_bErrorEEPROM = bError;
}

BOOL CBuiltInTestMgr::IsErrorROM()
{
    return m_bErrorROM;
}

BOOL CBuiltInTestMgr::IsErrorEEPROM()
{
    return m_bErrorEEPROM;
}

BOOL CBuiltInTestMgr::IsVswrFail()
{
    //---------------------------------------------------------------------------------------------
    // 14.6.2.2.1 Method of measurement
    // Prevent the EUT from radiating with full power by mismatching the antenna for a VSWR of 3:1.
    // During the mismatch the output power is not required to be the rated output power.
    //---------------------------------------------------------------------------------------------

    int nRefPower = CLayerPhysical::getInst()->m_wRefPowerAdValue;
    return (nRefPower > CSetupMgr::getInst()->GetVswrLimit());
}

BOOL CBuiltInTestMgr::IsTxMalFunction()
{
    return !GetGpioLockStatPLL1();
}

BOOL CBuiltInTestMgr::IsRxMalFunctionCH1()
{
    if(CLayerPhysical::getInst()->m_bEnableCheckRxMalFuncRX1)
        return !GetGpioLockStatPLL1();
    return FALSE;
}

BOOL CBuiltInTestMgr::IsRxMalFunctionCH2()
{
    if(CLayerPhysical::getInst()->m_bEnableCheckRxMalFuncRX2)
        return !GetGpioLockStatPLL2();
    return FALSE;
}

BOOL CBuiltInTestMgr::IsRxMalFunctionChDSC()
{
    if(CLayerPhysical::getInst()->m_bEnableCheckRxMalFuncRX1)
        return !GetGpioLockStatPLL3();
    return FALSE;
}

WORD CBuiltInTestMgr::GetDcVoltageAdcData()
{
    return DC_VOLTAGE_MIN;
}

BOOL CBuiltInTestMgr::IsDcVoltageFail()
{
    WORD wVoltageAdc = GetDcVoltageAdcData();
    return (SYS_ADC_TO_VOLT(wVoltageAdc) < DC_VOLTAGE_MIN);
}
