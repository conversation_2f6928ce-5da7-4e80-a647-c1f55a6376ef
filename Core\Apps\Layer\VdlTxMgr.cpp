#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "RxModem.h"
#include "ChannelMgr.h"
#include "FrameMapMgr.h"
#include "UserDirMgr.h"
#include "SensorMgr.h"
#include "SetupMgr.h"
#include "RosMgr.h"
#include "TestModeMgr.h"
#include "LayerPhysical.h"
#include "LayerNetwork.h"
#include "Ship.h"
#include "MKD.h"
#include "AisModem.h"
#include "Timer.h"
#include "SysOpStatus.h"
#include "VdlTxMgr.h"


#ifdef __ENABLE_CHECK_MSG15_CNT__
int     CVdlTxMgr::m_nTxMsg15CntFrame     = 0;
#else
int    *CVdlTxMgr::m_pTxSlotLogMsg15;
int     CVdlTxMgr::m_nTxSlotLogHeadMsg15  = 0;
int     CVdlTxMgr::m_nTxSlotLogTailMsg15  = 0;
#endif

int    *CVdlTxMgr::m_pTxSlotLogBinMsg;
int     CVdlTxMgr::m_nTxSlotLogHeadBinMsg = 0;
int     CVdlTxMgr::m_nTxSlotLogTailBinMsg = 0;

DWORD   CVdlTxMgr::m_dwSendSecVDO         = 0;


#define GetByteBoundaryBitSize(nSizeBits)        (((nSizeBits) & 0x07) ? (((nSizeBits) & 0xFFF8) + 8) : (nSizeBits))

CVdlTxMgr::CVdlTxMgr(CChannelMgr *pChannel)
{
    m_pChannel        = pChannel;

    // Allocate Tx Buffers
    m_pMsgList_03 = (TDMA_UNSCHE_INFO*)SysAllocMemory(sizeof(TDMA_UNSCHE_INFO) * NUM_BROADMSG);
    m_pMsgList_05 = (TDMA_UNSCHE_INFO*)SysAllocMemory(sizeof(TDMA_UNSCHE_INFO) * NUM_BROADMSG);
    m_pMsgList_11 = (TDMA_UNSCHE_INFO*)SysAllocMemory(sizeof(TDMA_UNSCHE_INFO) * NUM_BROADMSG);
    m_pMsgList_24 = (TDMA_UNSCHE_INFO*)SysAllocMemory(sizeof(TDMA_UNSCHE_INFO) * NUM_BROADMSG);

    m_pMsgList_15 = (TDMA_UNSCHE15_INFO*)SysAllocMemory(sizeof(TDMA_UNSCHE15_INFO) * NUM_BROADMSG);

    m_pMsgList_08 = (TDMA_UNSCHE_BROADMSG*)SysAllocMemory(sizeof(TDMA_UNSCHE_BROADMSG) * MAX_UNSCHE_BROADMSG);
    m_pMsgList_14 = (TDMA_UNSCHE_BROADMSG*)SysAllocMemory(sizeof(TDMA_UNSCHE_BROADMSG) * MAX_UNSCHE_BROADMSG);

    m_pMsgList_06 = (TDMA_UNSCHE_ADDRMSG*)SysAllocMemory(sizeof(TDMA_UNSCHE_ADDRMSG) * MAX_UNSCHE_ADDRMSG);
    m_pMsgList_12 = (TDMA_UNSCHE_ADDRMSG*)SysAllocMemory(sizeof(TDMA_UNSCHE_ADDRMSG) * MAX_UNSCHE_ADDRMSG);

    m_pMsgList_07 = (TDMA_UNSCHE_ACKMSG*)SysAllocMemory(sizeof(TDMA_UNSCHE_ACKMSG) * MAX_UNSCHE_ADDRMSG);
    m_pMsgList_13 = (TDMA_UNSCHE_ACKMSG*)SysAllocMemory(sizeof(TDMA_UNSCHE_ACKMSG) * MAX_UNSCHE_ADDRMSG);

    m_pMsgList_10 = (TDMA_ONESLOT_ADDRMSG*)SysAllocMemory(sizeof(TDMA_ONESLOT_ADDRMSG) * MAX_UNSCHE_ADDRMSG);

    m_pMsgList_25 = (TDMA_UNSCHE_MSG25*)SysAllocMemory(sizeof(TDMA_UNSCHE_MSG25) * MAX_UNSCHE_MSG25);
    m_pMsgList_26 = (TDMA_UNSCHE_MSG26*)SysAllocMemory(sizeof(TDMA_UNSCHE_MSG26) * MAX_UNSCHE_MSG26);

    m_pTxSlotBuff = (BYTE*)SysAllocMemory(sizeof(BYTE) * (NUM_MAX_BITS_TXBLOCK+10));

#ifdef __ENABLE_CHECK_MSG15_CNT__
#else
    m_pTxSlotLogMsg15 = (int*)SysAllocMemory(sizeof(int) * MAX_NUM_TX_PER_FRAME_MSG15);
    for(int i = 0 ; i < MAX_NUM_TX_PER_FRAME_MSG15 ; i++)
        m_pTxSlotLogMsg15[i] = SLOTID_NONE;
    m_nTxSlotLogHeadMsg15 = 0;
    m_nTxSlotLogTailMsg15 = 0;
#endif

    m_pTxSlotLogBinMsg= (int*)SysAllocMemory(sizeof(int) * MAX_NUM_TX_PER_FRAME_BINMSG);
    for(int i = 0 ; i < MAX_NUM_TX_PER_FRAME_BINMSG ; i++)
    {
        m_pTxSlotLogBinMsg[i] = SLOTID_NONE;
    }

    m_nTxSlotLogHeadBinMsg = 0;
    m_nTxSlotLogTailBinMsg = 0;

    InitAllTxBuff();
}

CVdlTxMgr::~CVdlTxMgr()
{
}

/**
 * @brief Initialize all Tx buffers
 */
void CVdlTxMgr::InitAllTxBuff()
{
    ResetAllBinBroadBuff();
    ResetAllAddrMsgBuff();
    ResetAllMsg25Buff();
    ResetAllMsg26Buff();
    ResetAllBinUnscheBroadBuff();
    ResetAllMsg15Buff();
    ResetAllAckMsgBuff();
    ResetAllOneSlotAddrMsgBuff();
}

/**
 * @brief Clear unscheduled Tx message info
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearUnScheTxMsgInfo(TDMA_UNSCHE_INFO *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = SLOTID_NONE;
    pTxBuff->nSizeSI        = 0;
    pTxBuff->nTimeOutSlot   = 0;
}

/**
 * @brief Reset all unscheduled Tx buffers
 */
void CVdlTxMgr::ResetAllBinUnscheBroadBuff()
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        ClearUnScheTxMsgInfo(&m_pMsgList_03[i]);
        ClearUnScheTxMsgInfo(&m_pMsgList_05[i]);
        ClearUnScheTxMsgInfo(&m_pMsgList_11[i]);
        ClearUnScheTxMsgInfo(&m_pMsgList_24[i]);
    }
}

/**
 * @brief Set unscheduled Tx message data buffer
 * @param nTxMsgID Tx message ID
 * @param bCheckExsitingTxBuff Flag to check existing Tx buffer
 * @param nSizeSI Size of SI
 * @param nTimeOutSlot Timeout slot
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::SetUnScheTxMsgDataBuff(UINT16 nTxMsgID, bool bCheckExsitingTxBuff, int nSizeSI, int nTimeOutSlot)
{
    //--------------------------------------------------
    // Unscheduled transmission
    // Class A : MSG 3, 5, 11, 24
    // Class B : MSG 18, 24
    //--------------------------------------------------

    TDMA_UNSCHE_INFO *pMsgDat = nullptr;
    if(bCheckExsitingTxBuff)
        pMsgDat = GetUnscheduledUsedTxBuffPtr(nTxMsgID);
    if(!pMsgDat)
        pMsgDat = GetUnscheduledEmptyTxBuffPtr(nTxMsgID);

    if(pMsgDat)
    {
        pMsgDat->bReserveTx     = true;
        pMsgDat->bAllocSlot     = false;
        pMsgDat->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;
        pMsgDat->uNumTxSlot     = (nTxMsgID == AIS_MSG_NO_ID_05 ? 2 : 1);
        pMsgDat->nTxMsgID       = nTxMsgID;
        pMsgDat->nSizeSI        = nSizeSI;
        pMsgDat->nTimeOutSlot   = nTimeOutSlot;
        return true;
    }

    return false;
}

/**
 * @brief Get unscheduled Tx buffer list
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer list
 */
TDMA_UNSCHE_INFO* CVdlTxMgr::GetUnscheduledTxBuffList(UINT16 nTxMsgID)
{
    TDMA_UNSCHE_INFO *pBuffList = NULL;

    if (CSetupMgr::getInst()->IsAisAClass() && 
        (nTxMsgID != AIS_MSG_NO_ID_03 && nTxMsgID != AIS_MSG_NO_ID_05 && nTxMsgID != AIS_MSG_NO_ID_11 && nTxMsgID != AIS_MSG_NO_ID_24))
    {
        return NULL;
    }

    if (CSetupMgr::getInst()->IsAisBClass() &&
        (nTxMsgID != AIS_MSG_NO_ID_18 && nTxMsgID != AIS_MSG_NO_ID_19 && nTxMsgID != AIS_MSG_NO_ID_24 && nTxMsgID != AIS_MSG_NO_ID_24_A))
        return NULL;

    switch (nTxMsgID)
    {
    case AIS_MSG_NO_ID_03:
        pBuffList = m_pMsgList_03;
        break;
    case AIS_MSG_NO_ID_05:
        pBuffList = m_pMsgList_05;
        break;
    case AIS_MSG_NO_ID_11:
        pBuffList = m_pMsgList_11;
        break;
    case AIS_MSG_NO_ID_24:
    case AIS_MSG_NO_ID_24_A:
        pBuffList = m_pMsgList_24;
        break;
    }

    return pBuffList;
}

/**
 * @brief Get empty unscheduled Tx buffer
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_INFO* CVdlTxMgr::GetUnscheduledEmptyTxBuffPtr(UINT16 nTxMsgID)
{
    TDMA_UNSCHE_INFO *pBuffList = GetUnscheduledTxBuffList(nTxMsgID);

    if(pBuffList)
    {
        for(int i = 0 ; i < NUM_BROADMSG ; i++)
        {
            if(!pBuffList[i].bReserveTx)
            {
                return &(pBuffList[i]);
            }
        }
    }

    return nullptr;
}

/**
 * @brief Get used unscheduled Tx buffer
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_INFO* CVdlTxMgr::GetUnscheduledUsedTxBuffPtr(UINT16 nTxMsgID)
{
    TDMA_UNSCHE_INFO *pBuffList = GetUnscheduledTxBuffList(nTxMsgID);

    if(pBuffList)
    {
        for(int i = 0 ; i < NUM_BROADMSG ; i++)
        {
            if(pBuffList[i].bReserveTx)
            {
                return &(pBuffList[i]);
            }
        }
    }

    return nullptr;
}

/**
 * @brief Clear Tx buffer for message 15
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearMsg15Buff(TDMA_UNSCHE15_INFO *pTxBuff)
{
    memset((void*)pTxBuff, 0, sizeof(TDMA_UNSCHE15_INFO));
}

/**
 * @brief Reset all Tx buffers for message 15
 */
void CVdlTxMgr::ResetAllMsg15Buff()
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        ClearMsg15Buff(&m_pMsgList_15[i]);
    }
}

/**
 * @brief Set Tx buffer for message 15
 * @param pTxBuff Pointer to Tx buffer
 * @param bAirType Air type
 * @param uMMSI1 MMSI 1
 * @param bMsgID11 Message ID 11
 * @param bMsgID12 Message ID 12
 * @param uMMSI2 MMSI 2 
 * @param bMsgID21 Message ID 21
 */
void CVdlTxMgr::SetUnScheTxMsg15Info(TDMA_UNSCHE15_INFO *pTxBuff, UINT8 bAirType, UINT uMMSI1, int bMsgID11, int bMsgID12, UINT uMMSI2, int bMsgID21)
{
    //------------------------------------------------------------------------------------------------------------------------------
    // refer to ITU-R-M 1371-5 Annex 8 3.13 Table 65 Class A interrogator 일때 다음 메시지에 대한 interrogation 메시지 15 송신
    // 다음 메시지에 대한 interrogation 메시지 15 송신
    // - Class A      :  3,  5, 24
    //   Class B-SO   : 18, 24, 19(Ivan recommencation)
    //   Class B-CS   : 18, 24, 19(Ivan recommencation)
    //   SAR-aircraft :  9, 24
    //   AtoN          : 21
    //   Base Station :  4, 24
    // - AIS Msg 15번의 Data 길이가 가변적임을 고려해야 함.
    //------------------------------------------------------------------------------------------------------------------------------

    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;
    pTxBuff->nTxMsgID       = AIS_MSG_NO_ID_15;

    pTxBuff->bAirType       = bAirType;
    pTxBuff->uMMSI1         = uMMSI1;
    pTxBuff->bMsgID11       = bMsgID11;

    switch(bAirType)
    {
    case AIR_ID1_MSG1:
        pTxBuff->bMsgID12     = 0;
        pTxBuff->uMMSI2       = 0;
        pTxBuff->bMsgID21     = 0;
        pTxBuff->nNumDataBits = 100;
        break;
    case AIR_ID1_MSG12:
        pTxBuff->bMsgID12     = bMsgID12;
        pTxBuff->uMMSI2       = 0;
        pTxBuff->bMsgID21     = 0;
        pTxBuff->nNumDataBits = 110;
        break;
    case AIR_ID12_MSG13:
        pTxBuff->bMsgID12     = 0;
        pTxBuff->uMMSI2       = uMMSI2;
        pTxBuff->bMsgID21     = bMsgID21;
        pTxBuff->nNumDataBits = 160;
        break;
    case AIR_ID12_MSG123:
        pTxBuff->bMsgID12     = bMsgID12;
        pTxBuff->uMMSI2       = uMMSI2;
        pTxBuff->bMsgID21     = bMsgID21;
        pTxBuff->nNumDataBits = 160;
        break;
    }
}

/**
 * @brief Get empty Tx buffer for message 15
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE15_INFO* CVdlTxMgr::GetEmptyMsg15BuffPtr(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(!m_pMsgList_15[i].bReserveTx)
        {
            return &(m_pMsgList_15[i]);
        }
    }
    return nullptr;
}

/**
 * @brief Clear Tx buffer for message 15
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearBinMsgBuff_Broadcast(TDMA_UNSCHE_BROADMSG *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;

    pTxBuff->nCurSetSlotID  = SLOTID_NONE;
    pTxBuff->nTxMsgID       = AIS_MSG_NO_ID_UNDEFINED;
    pTxBuff->nTxSeqNum      = ABMBBM_SEQNUM_NA;
    pTxBuff->nNumDataBits   = 0;

    pTxBuff->pstrEncAisciiData[0] = '\0';
}

/**
 * @brief Reset all Tx buffers for broadcast binary message
 */
void CVdlTxMgr::ResetAllBinBroadBuff()
{
    for(int i = 0 ; i < MAX_UNSCHE_BROADMSG ; i++)
    {
        ClearBinMsgBuff_Broadcast(&m_pMsgList_08[i]);
        ClearBinMsgBuff_Broadcast(&m_pMsgList_14[i]);
    }
}

/**
 * @brief Set Tx buffer for broadcast binary message
 * @param pTxBuff Pointer to Tx buffer
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @param uNumTxSlot Number of Tx slots
 */
void CVdlTxMgr::SetUnScheBroadTxMsgInfo(TDMA_UNSCHE_BROADMSG *pTxBuff, UINT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot)
{
    //------------------------------------------------------------------------------
    // BBM (Broadcast binary message) 에 의한 메시지 8, 14 의 송신
    // * - 선호하는 채널이 없을 때(AUTO)에는 Alternative Tx 채널로 전송해야 함
    //------------------------------------------------------------------------------

    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;

    pTxBuff->nTxMsgID       = nTxMsgID;
    pTxBuff->nTxSeqNum      = nTxSeqNum;
    pTxBuff->uNumTxSlot     = uNumTxSlot;
}

/**
 * @brief Get Tx buffer list for broadcast binary message
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer list
 */
TDMA_UNSCHE_BROADMSG* CVdlTxMgr::GetBinMsgBuffListBroadcast(int nTxMsgID)
{
    if(nTxMsgID == AIS_MSG_NO_ID_08)
        return m_pMsgList_08;
    else if(nTxMsgID == AIS_MSG_NO_ID_14)
        return m_pMsgList_14;
    return NULL;
}

/**
 * @brief Get empty Tx buffer for broadcast binary message
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_BROADMSG* CVdlTxMgr::GetBinMsgEmptyBuffBroadcast(int nTxMsgID, int nTxSeqNum)
{
    TDMA_UNSCHE_BROADMSG *pBuffList = GetBinMsgBuffListBroadcast(nTxMsgID);

    if(pBuffList)
    {
        for(int i = 0 ; i < MAX_UNSCHE_BROADMSG ; i++)
        {
            if(pBuffList[i].nTxMsgID == nTxMsgID && pBuffList[i].nTxSeqNum == nTxSeqNum)
            {
                DEBUG_LOG("GetBuffBroad] seq, #%d, msg:%d, seq:%d, %x\r\n",
                        i, nTxMsgID, nTxSeqNum, pBuffList);
                return &(pBuffList[i]);
            }
        }
        for(int i = 0 ; i < MAX_UNSCHE_BROADMSG ; i++)
        {
            DEBUG_LOG("GetBuffBroad] checkNew, #%d, msg:%d, seq:%d, %x\r\n",
                    i, nTxMsgID, nTxSeqNum, pBuffList);

            if(!pBuffList[i].bReserveTx)
            {
                DEBUG_LOG("GetBuffBroad] new, #%d, msg:%d, seq:%d, %x\r\n",
                        i, nTxMsgID, nTxSeqNum, pBuffList);
                return &(pBuffList[i]);
            }
        }
    }

    return nullptr;
}

/**
 * @brief Clear Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearBinMsgBuff_Addressed(TDMA_UNSCHE_ADDRMSG *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = SLOTID_NONE;

    pTxBuff->nTxSeqNum      = ABMBBM_SEQNUM_NA;
    pTxBuff->nNumDataBits   = 0;
    pTxBuff->uDestMMSI      = AIS_AB_MMSI_NULL;
    pTxBuff->nTxMsgID       = AIS_MSG_NO_ID_UNDEFINED;
    pTxBuff->nTxRetryCnt    = 0;
}

/**
 * @brief Reset all Tx buffers for addressed binary message
 */
void CVdlTxMgr::ResetAllAddrMsgBuff(void)
{
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        ClearBinMsgBuff_Addressed(&m_pMsgList_06[i]);
        ClearBinMsgBuff_Addressed(&m_pMsgList_12[i]);
    }
}

/**
 * @brief Set Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 * @param uDestMMSI Destination MMSI
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @param uNumTxSlot Number of Tx slots
 */
void CVdlTxMgr::SetUnScheAddrTxMsgInfo(TDMA_UNSCHE_ADDRMSG *pTxBuff, UINT32 uDestMMSI, UINT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot)
{
    //------------------------------------------------------------------------------
    // ABM 으로 부터의 메시지 6, 12, 25, 26 의 송신요구
    //------------------------------------------------------------------------------

    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;

    pTxBuff->uDestMMSI      = uDestMMSI;
    pTxBuff->nTxMsgID       = nTxMsgID;
    pTxBuff->nTxSeqNum      = nTxSeqNum;

    pTxBuff->nTxRetryCnt    = 0;
    pTxBuff->uNumTxSlot     = uNumTxSlot;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param uMsgID Tx message ID
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_ADDRMSG* CVdlTxMgr::GetAddrMsgBuffPtr(UINT16 uMsgID)
{
    if(uMsgID == AIS_MSG_NO_ID_06)
        return m_pMsgList_06;
    else if(uMsgID == AIS_MSG_NO_ID_12)
        return m_pMsgList_12;
    return nullptr;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param uDestMMSI Destination MMSI
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_ADDRMSG* CVdlTxMgr::GetBinMsgBuffPtr_Addressed(UINT uDestMMSI, int nTxMsgID, int nTxSeqNum)
{
    TDMA_UNSCHE_ADDRMSG *pBuffList;

    if(!(pBuffList = GetAddrMsgBuffPtr(nTxMsgID)))
    {
        return nullptr;
    }

    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].nTxMsgID == nTxMsgID && pBuffList[i].uDestMMSI == uDestMMSI && pBuffList[i].nTxSeqNum == nTxSeqNum)    // 기존에 할당된 해당 메시지가 있으면 리턴
            return &(pBuffList[i]);
    }
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(!pBuffList[i].bReserveTx)
            return &(pBuffList[i]);
    }

    return nullptr;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param nTxMsgID Tx message ID
 * @param uDestMMSI Destination MMSI
 * @param nTxSeqNum Tx sequence number
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_ADDRMSG* CVdlTxMgr::GetAddrMsgTxInfoByUniqueID(UINT16 nTxMsgID, UINT uDestMMSI, UINT8 nTxSeqNum)
{
    TDMA_UNSCHE_ADDRMSG *pBuffList;

    if((pBuffList = GetAddrMsgBuffPtr(nTxMsgID)))
    {
        for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
        {
            if(pBuffList[i].bReserveTx && pBuffList[i].uDestMMSI == uDestMMSI && pBuffList[i].nTxSeqNum == nTxSeqNum)
                return &pBuffList[i];
        }
    }
    return nullptr;
}

/**
 * @brief Clear Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearAckTxMsgInfo(TDMA_UNSCHE_ACKMSG *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = SLOTID_NONE;
    pTxBuff->nTxMsgID       = AIS_MSG_NO_ID_UNDEFINED;
    pTxBuff->uDestMMSI      = AIS_AB_MMSI_NULL;
    pTxBuff->nTxSeqNum      = 0;
}

/**
 * @brief Reset all Tx buffers for addressed binary message
 */
void CVdlTxMgr::ResetAllAckMsgBuff(void)
{
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        ClearAckTxMsgInfo(&m_pMsgList_07[i]);
        ClearAckTxMsgInfo(&m_pMsgList_13[i]);
    }
}

/**
 * @brief Set Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 * @param wRespStartSI Response start slot ID
 * @param uDestMMSI Destination MMSI
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 */
void CVdlTxMgr::SetAckMsgBuffPtr(TDMA_UNSCHE_ACKMSG *pTxBuff, WORD wRespStartSI, UINT uDestMMSI, UINT16 nTxMsgID, UINT8 nTxSeqNum)
{
    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;

    pTxBuff->nTxMsgID       = nTxMsgID;         // VDL 07, 13
    pTxBuff->uDestMMSI      = uDestMMSI;        // Using nTxMsgID=7,13
    pTxBuff->nTxSeqNum      = nTxSeqNum;        // sequ_num(0~3)

    pTxBuff->wTxStartSI     = wRespStartSI;
}

/**
 * @brief Get Tx buffer list for addressed binary message
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer list
 */
TDMA_UNSCHE_ACKMSG* CVdlTxMgr::GetAckMsgBuffList(UINT16 nTxMsgID)
{
    TDMA_UNSCHE_ACKMSG* pBuffList = nullptr;

    if(nTxMsgID == AIS_MSG_NO_ID_07)
        pBuffList = m_pMsgList_07;
    else if(nTxMsgID == AIS_MSG_NO_ID_13)
        pBuffList = m_pMsgList_13;
    return pBuffList;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_ACKMSG* CVdlTxMgr::GetAckMsgEmptyBuff(UINT16 nTxMsgID)
{
    TDMA_UNSCHE_ACKMSG* pBuffList = GetAckMsgBuffList(nTxMsgID);

    if(pBuffList)
    {
        for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
        {
            if(!pBuffList[i].bReserveTx)
                return &pBuffList[i];
        }
    }

    ERROR_LOG("ProcTx] Alloc Fail, Ack, msg:%d\r\n", nTxMsgID);

    return nullptr;
}

/**
 * @brief Clear Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearMsg25Buff(TDMA_UNSCHE_MSG25 *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = SLOTID_NONE;

    pTxBuff->nTxSeqNum      = ABMBBM_SEQNUM_NA;
    pTxBuff->nNumDataBits   = 0;
    pTxBuff->uDestMMSI      = AIS_AB_MMSI_NULL;
}

/**
 * @brief Reset all Tx buffers for addressed binary message
 */
void CVdlTxMgr::ResetAllMsg25Buff()
{
    for(int i = 0 ; i < MAX_UNSCHE_MSG25 ; i++)
    {
        ClearMsg25Buff(&m_pMsgList_25[i]);
    }
}

/**
 * @brief Set Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 * @param uDestMMSI Destination MMSI
 * @param nABMMsgID ABM message ID
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @param uNumTxSlot Number of Tx slots
 * @param bBinDataFlag Binary data flag
 */
void CVdlTxMgr::SetTxMsg25Info(TDMA_UNSCHE_MSG25 *pTxBuff, UINT32 uDestMMSI, INT16 nABMMsgID, INT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot, BYTE bBinDataFlag)
{
    //------------------------------------------------------------------------------
    // ABM, BBM 으로 부터의 메시지 25 의 송신요구
    //------------------------------------------------------------------------------

    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;

    pTxBuff->uDestMMSI      = uDestMMSI;
    pTxBuff->nABMMsgID      = nABMMsgID;
    pTxBuff->nTxMsgID       = nTxMsgID;
    pTxBuff->nTxSeqNum      = nTxSeqNum;

    pTxBuff->uNumTxSlot     = uNumTxSlot;
    pTxBuff->bBinDataFlag   = bBinDataFlag;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param uDestMMSI Destination MMSI
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_MSG25* CVdlTxMgr::GetMsg25BuffPtr(UINT uDestMMSI, int nTxMsgID, int nTxSeqNum)
{
    if(nTxMsgID != AIS_MSG_NO_ID_25)
        return NULL;

    for(int i = 0 ; i < MAX_UNSCHE_MSG25 ; i++)
    {
        if(m_pMsgList_25[i].uDestMMSI == uDestMMSI && m_pMsgList_25[i].nTxMsgID == nTxMsgID && m_pMsgList_25[i].nTxSeqNum == nTxSeqNum)        // 기존에 할당된 해당 메시지가 있으면 리턴
            return &(m_pMsgList_25[i]);
    }
    for(int i = 0 ; i < MAX_UNSCHE_MSG25 ; i++)
    {
        if(!m_pMsgList_25[i].bReserveTx)// && m_pMsgList_25[i].nTxSeqNum == ABMBBM_SEQNUM_NA)            // 아니면 빈 버퍼를 리턴
            return &(m_pMsgList_25[i]);
    }

    ERROR_LOG("ProcTx] Alloc Fail, 25, msg:%d\r\n", nTxMsgID);
    return nullptr;
}

void CVdlTxMgr::ClearMsg26Buff(TDMA_UNSCHE_MSG26 *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = SLOTID_NONE;

    pTxBuff->nTxSeqNum      = ABMBBM_SEQNUM_NA;
    pTxBuff->nNumDataBits   = 0;
    pTxBuff->uDestMMSI      = AIS_AB_MMSI_NULL;
}

/**
 * @brief Reset all Tx buffers for addressed binary message
 */
void CVdlTxMgr::ResetAllMsg26Buff()
{
    for(int i = 0 ; i < MAX_UNSCHE_MSG26 ; i++)
    {
        ClearMsg26Buff(&m_pMsgList_26[i]);
    }
}

/**
 * @brief Set Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 * @param uDestMMSI Destination MMSI
 * @param nBBMMsgID BBM message ID
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @param uNumTxSlot Number of Tx slots
 * @param bBinDataFlag Binary data flag
 */
void CVdlTxMgr::SetTxMsg26Info(TDMA_UNSCHE_MSG26 *pTxBuff, UINT32 uDestMMSI, INT16 nBBMMsgID, INT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot, BYTE bBinDataFlag)
{
    //------------------------------------------------------------------------------
    // ABM, BBM 으로 부터의 메시지 26 의 송신요구
    //------------------------------------------------------------------------------

    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;

    pTxBuff->uDestMMSI      = uDestMMSI;
    pTxBuff->nBBMMsgID      = nBBMMsgID;
    pTxBuff->nTxMsgID       = nTxMsgID;
    pTxBuff->nTxSeqNum      = nTxSeqNum;

    pTxBuff->uNumTxSlot     = uNumTxSlot;
    pTxBuff->bBinDataFlag   = bBinDataFlag;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param uDestMMSI Destination MMSI
 * @param nTxMsgID Tx message ID
 * @param nTxSeqNum Tx sequence number
 * @return Pointer to Tx buffer
 */
TDMA_UNSCHE_MSG26* CVdlTxMgr::GetMsg26BuffPtr(UINT uDestMMSI, int nTxMsgID, int nTxSeqNum)
{
    if(nTxMsgID != AIS_MSG_NO_ID_26)
        return NULL;

    for(int i = 0 ; i < MAX_UNSCHE_MSG26 ; i++)
    {
        if(m_pMsgList_26[i].uDestMMSI == uDestMMSI && m_pMsgList_26[i].nTxMsgID == nTxMsgID && m_pMsgList_26[i].nTxSeqNum == nTxSeqNum)    // 기존에 할당된 해당 메시지가 있으면 리턴
            return &(m_pMsgList_26[i]);
    }
    for(int i = 0 ; i < MAX_UNSCHE_MSG26 ; i++)
    {
        if(!m_pMsgList_26[i].bReserveTx)
            return &(m_pMsgList_26[i]);
    }

    ERROR_LOG("ProcTx] Alloc Fail, 26, msg:%d\r\n", nTxMsgID);
    return nullptr;
}

/**
 * @brief Clear Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 */
void CVdlTxMgr::ClearOneSlotAddrTxMsgInfo(TDMA_ONESLOT_ADDRMSG *pTxBuff)
{
    pTxBuff->bReserveTx     = false;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = SLOTID_NONE;
    pTxBuff->nAirMsgID      = AIS_MSG_NO_ID_UNDEFINED;
    pTxBuff->nTxMsgID       = AIS_MSG_NO_ID_UNDEFINED;
    pTxBuff->uDestMMSI      = AIS_AB_MMSI_NULL;
}

/**
 * @brief Reset all Tx buffers for addressed binary message
 */
void CVdlTxMgr::ResetAllOneSlotAddrMsgBuff()
{
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        ClearOneSlotAddrTxMsgInfo(&m_pMsgList_10[i]);
    }
}

/**
 * @brief Set Tx buffer for addressed binary message
 * @param pTxBuff Pointer to Tx buffer
 * @param uDestMMSI Destination MMSI
 * @param nAirMsgID Air message ID
 * @param nTxMsgID Tx message ID
 */
void CVdlTxMgr::SetOneSlotAddrTxMsgInfo(TDMA_ONESLOT_ADDRMSG *pTxBuff, UINT uDestMMSI, UINT16 nAirMsgID, UINT16 nTxMsgID)
{
    pTxBuff->bReserveTx     = true;
    pTxBuff->bAllocSlot     = false;
    pTxBuff->nCurSetSlotID  = OPSTATUS::nCurFrameMapSlotID;

    pTxBuff->nAirMsgID      = nAirMsgID;
    pTxBuff->nTxMsgID       = nTxMsgID;     // VDL 07, 13
    pTxBuff->uDestMMSI      = uDestMMSI;    // Using nTxMsgID=7,13
}

/**
 * @brief Get Tx buffer list for addressed binary message
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer list
 */
TDMA_ONESLOT_ADDRMSG* CVdlTxMgr::GetOneSlotAddrTxMsgBuffList(UINT16 nTxMsgID)
{
    TDMA_ONESLOT_ADDRMSG* pBuffList = NULL;

    if(nTxMsgID == AIS_MSG_NO_ID_10)
        pBuffList = m_pMsgList_10;
    return pBuffList;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param nTxMsgID Tx message ID
 * @return Pointer to Tx buffer
 */
TDMA_ONESLOT_ADDRMSG* CVdlTxMgr::GetOneSlotAddrTxMsgEmptyBuff(UINT16 nTxMsgID)
{
    TDMA_ONESLOT_ADDRMSG* pBuffList = GetOneSlotAddrTxMsgBuffList(nTxMsgID);

    if(pBuffList)
    {
        for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
        {
            if(!pBuffList[i].bReserveTx)
                return &pBuffList[i];
        }
    }

    ERROR_LOG(" ProcTx] Alloc Fail, oneSlot, msg:%d\r\n", nTxMsgID);

    return nullptr;
}

/**
 * @brief Get Tx buffer for addressed binary message
 * @param nTxMsgID Tx message ID
 * @param uDestMMSI Destination MMSI
 * @return Pointer to Tx buffer
 */
TDMA_ONESLOT_ADDRMSG* CVdlTxMgr::GetOneSlotAddrTxMsgByUniqueID(UINT16 nTxMsgID, UINT uDestMMSI)
{
    TDMA_ONESLOT_ADDRMSG *pBuffList;

    if((pBuffList = GetOneSlotAddrTxMsgBuffList(nTxMsgID)))
    {
        for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
        {
            if(pBuffList[i].bReserveTx && pBuffList[i].uDestMMSI == uDestMMSI)
                return &pBuffList[i];
        }
    }
    return NULL;
}

/**
 * @brief Set Tx slot ID for binary message
 * @param nSentenceType Sentence type
 * @param nNumTxSlot Number of Tx slots
 */
void CVdlTxMgr::SetTxSlotIdBinMsg(BYTE nSentenceType, int nNumTxSlot)
{
    for(int i = 0 ; i < nNumTxSlot ; i++)
    {
        m_pTxSlotLogBinMsg[m_nTxSlotLogHeadBinMsg] = OPSTATUS::nCurFrameMapSlotID;
        if(++m_nTxSlotLogHeadBinMsg >= MAX_NUM_TX_PER_FRAME_BINMSG)
            m_nTxSlotLogHeadBinMsg = 0;
    }
}

/**
 * @brief Check number of Tx slots for binary message
 */
void CVdlTxMgr::CheckLogNumTxSlotBinMsg()
{
    // 1분 경과된 슬롯은 SLOTID_NONE 으로 마킹한다!
    for(int i = 0 ; i < MAX_NUM_TX_PER_FRAME_BINMSG ; i++)
    {
        if(GetFrMapElapTimeSlot(m_pTxSlotLogBinMsg[i], OPSTATUS::nCurFrameMapSlotID) >= NUM_SLOT_PER_FRAME)
        {
            m_pTxSlotLogBinMsg[i] = SLOTID_NONE;
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for one slot binary message
 * @param pBuff Pointer to Tx buffer
 * @param nBuffIdx Buffer index
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledOneSlotBroadTxMsg(TDMA_UNSCHE_INFO *pBuff, BYTE nBuffIdx)
{
    //----------------------------------------
    // 메시지 3, 11, 24 송신용 버퍼 처리
    //----------------------------------------
    if(pBuff->bReserveTx)
    {
        if(!pBuff->bAllocSlot)
        {
            WORD wStartSI = FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, SLOT_SPACE_1SEC);
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(pBuff->nTxMsgID, wStartSI, 1, false, nBuffIdx))
            {
                return false;
            }
            pBuff->bAllocSlot = true;
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for one slot binary message
 * @param pBuff Pointer to Tx buffer
 */
void CVdlTxMgr::PeriodicUnscheduledOneSlotBroadTxMsg(TDMA_UNSCHE_INFO *pBuff)
{
    //----------------------------------------
    // 메시지 3, 11, 24 송신용 버퍼 처리
    //----------------------------------------
    if(pBuff && pBuff->bReserveTx && GetFrMapElapTimeSlot(pBuff->nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
    {
        DEBUG_LOG("Tx-UnscheOneSlotBroad] Alloc time-out, release msg:%d, %d\r\n",
            pBuff->nTxMsgID, GetFrMapElapTimeSlot(pBuff->nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID));

        ClearUnScheTxMsgInfo(pBuff);
    }
}

/**
 * @brief Process unscheduled Tx buffer for one slot addressed binary message
 * @param pBuffList Pointer to Tx buffer list
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledOneSlotAddrTxMsg(TDMA_ONESLOT_ADDRMSG *pBuffList)
{
    //---------------------------------
    // 메시지 10 송신용 버퍼 처리
    //---------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && !pBuffList[i].bAllocSlot)
        {
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(pBuffList[i].nTxMsgID, SLOTID_NONE, 1, false, i))
                return false;
            pBuffList[i].bAllocSlot = true;
            // refer to IEC-61162-1 ed4.0 AIR
            CMKD::getInst()->SendABKtoPI(pBuffList[i].uDestMMSI, AIS_CHANNEL_NONE, pBuffList[i].nAirMsgID, 0, ABK_BROADCAST_OK);
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for one slot addressed binary message
 * @param pBuffList Pointer to Tx buffer list
 */
void CVdlTxMgr::PeriodicUnscheduledOneSlotAddrTxMsg(TDMA_ONESLOT_ADDRMSG *pBuffList)
{
    //---------------------------------
    // 메시지 10 송신용 버퍼 처리
    //---------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && pBuffList[i].bAllocSlot)
        {
            if(GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
            {
                ClearOneSlotAddrTxMsgInfo(&pBuffList[i]);
            }
        }
    }

    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            if(!pBuffList[i].bAllocSlot)
            {
                CMKD::getInst()->SendABKtoPI(pBuffList[i].uDestMMSI, AIS_CHANNEL_NONE, pBuffList[i].nAirMsgID, 0, ABK_TX_FAIL);
            }
            ClearOneSlotAddrTxMsgInfo(&pBuffList[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for broadcast binary message
 * @param pBuffList Pointer to Tx buffer list
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledBroadcastTxMsg(TDMA_UNSCHE_BROADMSG *pBuffList)
{
    //-------------------------------------------------------------
    // 메시지 8, 14 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_BROADMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && !pBuffList[i].bAllocSlot)
        {
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(pBuffList[i].nTxMsgID, SLOTID_NONE, pBuffList[i].uNumTxSlot, true, i))
            {
                return false;
            }
            pBuffList[i].bAllocSlot = true;
            CMKD::getInst()->SendABKtoPI(AIS_AB_MMSI_NULL, AIS_CHANNEL_NONE, pBuffList[i].nTxMsgID, pBuffList[i].nTxSeqNum, ABK_BROADCAST_OK);
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for broadcast binary message
 * @param pBuffList Pointer to Tx buffer list
 */
void CVdlTxMgr::PeriodicUnscheduledBroadcastTxMsg(TDMA_UNSCHE_BROADMSG *pBuffList)
{
    //-------------------------------------------------------------
    // 메시지 8, 14 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_BROADMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            if(!pBuffList[i].bAllocSlot)
            {
                //refer to IEC-61162-1 ed4.0 Type of acknowledgement field of ABK
                CMKD::getInst()->SendABKtoPI(AIS_AB_MMSI_NULL, AIS_CHANNEL_NONE, pBuffList[i].nTxMsgID, pBuffList[i].nTxSeqNum, ABK_TX_FAIL);
            }
            ClearBinMsgBuff_Broadcast(&(pBuffList[i]));
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for addressed binary message
 * @param pBuffList Pointer to Tx buffer list
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledAddressedTxMsg(TDMA_UNSCHE_ADDRMSG *pBuffList)
{
    //-------------------------------------------------------------
    // 메시지 6, 12, 송신용 버퍼 처리
    // Refer to ITU-R.1371-5 Annex.2 5.3.1
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx)
        {
            if(!pBuffList[i].bAllocSlot)
            {
                if(!m_pChannel->AllocUnscheduledMultiSlotMsg(pBuffList[i].nTxMsgID, SLOTID_NONE, pBuffList[i].uNumTxSlot, true,
                                                                            i, RATDMA_SI, &(pBuffList[i].nTxSlot)))
                {
                    return false;
                }
                pBuffList[i].bAllocSlot = true;
            }
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for addressed binary message
 * @param pBuffList Pointer to Tx buffer list
 */
void CVdlTxMgr::PeriodicUnscheduledAddressedTxMsg(TDMA_UNSCHE_ADDRMSG *pBuffList)
{
    //-------------------------------------------------------------
    // 메시지 6, 12, 송신용 버퍼 처리
    // Refer to ITU-R.1371-5 Annex.2 5.3.1
    //-------------------------------------------------------------

    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && pBuffList[i].bAllocSlot)
        {
            int nDiff1 = GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID);
            int nDiff2 = GetFrMapElapTimeSlot(pBuffList[i].nTxSlot, OPSTATUS::nCurFrameMapSlotID);
            if(nDiff1 >= RATDMA_SI && nDiff2 >= RATDMA_SI)
            {
                pBuffList[i].bAllocSlot = false;

                if(pBuffList[i].nTxRetryCnt < CSetupMgr::getInst()->GetAddrMsgTxRetryCnt())
                {
                    pBuffList[i].nTxRetryCnt++;
                    pBuffList[i].nCurSetSlotID = OPSTATUS::nCurFrameMapSlotID;

                    DEBUG_LOG("TxMsgAddr] Proc, ACK inc retry, #%d, MSG : %d, retry : %d(%d), setBufS: %d, txS: %d, elapSlot: %d,%d, sys : %d\r\n",
                        i, pBuffList[i].nTxMsgID, pBuffList[i].nTxRetryCnt, CSetupMgr::getInst()->GetAddrMsgTxRetryCnt(),
                        pBuffList[i].nCurSetSlotID, pBuffList[i].nTxSlot,
                        nDiff1, nDiff2, cTimerSys::getInst()->GetCurTimerSec());
                }
                else
                {
                    DEBUG_LOG("TxMsgAddr] Proc, ACK time-out, #%d, MSG : %d, retry : %d(%d), elapSlot: %d,%d sys : %d\r\n",
                        i, pBuffList[i].nTxMsgID, pBuffList[i].nTxRetryCnt, CSetupMgr::getInst()->GetAddrMsgTxRetryCnt(),
                        nDiff1, nDiff2, cTimerSys::getInst()->GetCurTimerSec());

                    CMKD::getInst()->SendABKtoPI(pBuffList[i].uDestMMSI, AIS_CHANNEL_NONE, pBuffList[i].nTxMsgID, pBuffList[i].nTxSeqNum, ABK_ADDRESSED_NACK);
                    ClearBinMsgBuff_Addressed(&pBuffList[i]);
                }
            }
        }

        const int TIMEOUT_SLOTS = CSetupMgr::getInst()->GetAddrMsgTxRetryCnt()*RATDMA_SI + RATDMA_SI;
        if(pBuffList[i].bReserveTx && GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) > TIMEOUT_SLOTS)
        {
            if(!pBuffList[i].bAllocSlot)
                CMKD::getInst()->SendABKtoPI(pBuffList[i].uDestMMSI, AIS_CHANNEL_NONE, pBuffList[i].nTxMsgID, pBuffList[i].nTxSeqNum, ABK_TX_FAIL);
            ClearBinMsgBuff_Addressed(&pBuffList[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for addressed binary message
 * @param pBuffList Pointer to Tx buffer list
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledAckTxMsg(TDMA_UNSCHE_ACKMSG *pBuffList)
{
    //---------------------------------
    // 메시지 7, 13 송신용 버퍼 처리
    //---------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && !pBuffList[i].bAllocSlot)
        {
            // 응답할 슬롯선택을 위한 SI 를 현재 시점과 비교해 재계산한다
            int nSizeSI = RATDMA_SI;
            WORD wModemCurSlot = GetFrameMapSlotID(cAisModem::getInst()->GetCurrentFrameNo(), cAisModem::getInst()->GetSlotNoCounter());
            wModemCurSlot = FrameMapSlotIdAdd(wModemCurSlot, TX_RESERVE_SLOT_SPACE);
            WORD wStartSI = pBuffList[i].wTxStartSI;
            int nDiff1 = FrameMapGetDiffSlotID(wModemCurSlot, wStartSI);
            int nDiff2 = FrameMapGetDiffSlotID(wStartSI, wModemCurSlot);
            if(nDiff2 < nDiff1)
            {
                wStartSI = wModemCurSlot;
                nSizeSI = RATDMA_SI - nDiff2;
                nSizeSI = MAX(nSizeSI, 0);
                nSizeSI = MIN(nSizeSI, RATDMA_SI);
            }

            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(pBuffList[i].nTxMsgID, wStartSI, 1, false, i, nSizeSI))
                return false;
            pBuffList[i].bAllocSlot = true;
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for addressed binary message
 * @param pBuffList Pointer to Tx buffer list
 */
void CVdlTxMgr::PeriodicUnscheduledAckTxMsg(TDMA_UNSCHE_ACKMSG *pBuffList)
{
    //---------------------------------
    // 메시지 7, 13 송신용 버퍼 처리
    //---------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_ADDRMSG ; i++)
    {
        if(pBuffList[i].bReserveTx && GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            DEBUG_LOG("Tx-UnscheAck] Alloc time-out, release msg:%d, #%d, %d\r\n",
                pBuffList[i].nTxMsgID, i, GetFrMapElapTimeSlot(pBuffList[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID));

            ClearAckTxMsgInfo(&pBuffList[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 03
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg03(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(!ProcessUnscheduledOneSlotBroadTxMsg(&m_pMsgList_03[i], i))
            return false;
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 03
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg03(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_03[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_03[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            ClearUnScheTxMsgInfo(&m_pMsgList_03[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 05
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg05(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_05[i].bReserveTx && !m_pMsgList_05[i].bAllocSlot)
        {
            bool bFailNoExistingSO = (m_pMsgList_05[i].nTimeOutSlot > RATDMA_SLOT_TIMEOUT);
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(m_pMsgList_05[i].nTxMsgID, SLOTID_NONE, m_pMsgList_05[i].uNumTxSlot,
                                                                        true, i, m_pMsgList_05[i].nSizeSI, NULL, bFailNoExistingSO))
            {
                return false;
            }
            m_pMsgList_05[i].bAllocSlot = true;
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 05
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg05(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_05[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_05[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= m_pMsgList_05[i].nTimeOutSlot)
        {
            ClearUnScheTxMsgInfo(&m_pMsgList_05[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 06
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg06(void)
{
    return ProcessUnscheduledAddressedTxMsg(m_pMsgList_06);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 06
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg06()
{
    PeriodicUnscheduledAddressedTxMsg(m_pMsgList_06);
}

/**
 * @brief Process unscheduled Tx buffer for message 07
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg07()
{
    return ProcessUnscheduledAckTxMsg(m_pMsgList_07);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 07
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg07(void)
{
    PeriodicUnscheduledAckTxMsg(m_pMsgList_07);
}

/**
 * @brief Process unscheduled Tx buffer for message 08
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg08(void)
{
    return ProcessUnscheduledBroadcastTxMsg(m_pMsgList_08);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 08
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg08(void)
{
    PeriodicUnscheduledBroadcastTxMsg(m_pMsgList_08);
}

/**
 * @brief Process unscheduled Tx buffer for message 10
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg10(void)
{
    return ProcessUnscheduledOneSlotAddrTxMsg(m_pMsgList_10);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 10
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg10(void)
{
    PeriodicUnscheduledOneSlotAddrTxMsg(m_pMsgList_10);
}

/**
 * @brief Process unscheduled Tx buffer for message 11
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg11(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(!ProcessUnscheduledOneSlotBroadTxMsg(&m_pMsgList_11[i], i))
            return false;
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 11
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg11(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_11[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_11[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            ClearUnScheTxMsgInfo(&m_pMsgList_11[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 12
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg12(void)
{
    return ProcessUnscheduledAddressedTxMsg(m_pMsgList_12);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 12
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg12(void)
{
    PeriodicUnscheduledAddressedTxMsg(m_pMsgList_12);
}

/**
 * @brief Process unscheduled Tx buffer for message 13
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg13(void)
{
    return ProcessUnscheduledAckTxMsg(m_pMsgList_13);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 13
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg13(void)
{
    PeriodicUnscheduledAckTxMsg(m_pMsgList_13);
}

/**
 * @brief Process unscheduled Tx buffer for message 14
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg14(void)
{
    return ProcessUnscheduledBroadcastTxMsg(m_pMsgList_14);
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 14
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg14(void)
{
    PeriodicUnscheduledBroadcastTxMsg(m_pMsgList_14);
}

/**
 * @brief Process unscheduled Tx buffer for message 15
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg15(void)
{
    //-------------------------------------------------------------
    // 메시지 15 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_15[i].bReserveTx && !m_pMsgList_15[i].bAllocSlot)
        {
            WORD wStartSI = FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, SLOT_SPACE_1SEC);
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(m_pMsgList_15[i].nTxMsgID, wStartSI, 1, true, i))
                return false;
            m_pMsgList_15[i].bAllocSlot = true;
            CMKD::getInst()->SendABKtoPI(m_pMsgList_15[i].uMMSI1, AIS_CHANNEL_NONE, AIS_MSG_NO_ID_15, 0, ABK_BROADCAST_OK);     // refer to IEC-61162-1 ed4.0 Type of acknowledgement field of ABK
        }
    }
    return true;
}

/**
 * @brief Process response for message 15
 * @param nMMSI MMSI
 * @param nAckMsgID Acknowledgement message ID
 */
void CVdlTxMgr::ProcessRespForMsg15(int nMMSI, int nAckMsgID)
{
    // refer to IEC-61162-1 ed4.0 Type of acknowledgement field of ABK
#if 0 
    //--------------------------------
    // refer to IEC-61162-1 ed4.0
    //--------------------------------

    TDMA_UNSCHE15_INFO *pTxData = NULL;
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_15[i].uMMSI1 == nMMSI)
        {
            pTxData = &m_pMsgList_15[i];
            break;
        }
    }

    if(pTxData)
    {
        if(pTxData->bReserveTx && pTxData->bAllocSlot && pTxData->nCurSetSlotID != SLOTID_NONE)
        {
            CMKD::getInst()->SendABKtoPI(pTxData->uMMSI1, AIS_CHANNEL_NONE, AIS_MSG_NO_ID_15, 0, ABK_ADDRESSED_ACK_OK);
            ClearMsg15Buff(pTxData);
        }
    }
#endif
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 15
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg15(void)
{
    //-------------------------------------------------------------
    // 메시지 15 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_15[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_15[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            if(!m_pMsgList_15[i].bAllocSlot)
            {
                // refer to IEC-61162-1 ed4.0 Type of acknowledgement field of ABK
                CMKD::getInst()->SendABKtoPI(m_pMsgList_15[i].uMMSI1, AIS_CHANNEL_NONE, AIS_MSG_NO_ID_15, 0, ABK_TX_FAIL);
            }
            ClearMsg15Buff(&m_pMsgList_15[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 24
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg24(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(!ProcessUnscheduledOneSlotBroadTxMsg(&m_pMsgList_24[i], i))
            return false;
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 24
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg24(void)
{
    for(int i = 0 ; i < NUM_BROADMSG ; i++)
    {
        if(m_pMsgList_24[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_24[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            ClearUnScheTxMsgInfo(&m_pMsgList_24[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 25
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg25(void)
{
    //-------------------------------------------------------------
    // 메시지 25(addressed, broadcast) 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_MSG25 ; i++)
    {
        if(m_pMsgList_25[i].bReserveTx && !m_pMsgList_25[i].bAllocSlot)
        {
            WORD wStartSI = FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, SLOT_SPACE_1SEC);
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(m_pMsgList_25[i].nTxMsgID, wStartSI, 1, true, i))
                return false;
            CMKD::getInst()->SendABKtoPI(m_pMsgList_25[i].uDestMMSI, AIS_CHANNEL_NONE, m_pMsgList_25[i].nABMMsgID, m_pMsgList_25[i].nTxSeqNum, ABK_BROADCAST_OK);
            m_pMsgList_25[i].bAllocSlot = true;
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 25
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg25(void)
{
    //-------------------------------------------------------------
    // 메시지 25(addressed, broadcast) 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_MSG25 ; i++)
    {
        if(m_pMsgList_25[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_25[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            DEBUG_LOG("Tx-Msg25] Alloc time-out, release #%d, %d\r\n",
                i, GetFrMapElapTimeSlot(m_pMsgList_25[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID));

            if(!m_pMsgList_25[i].bAllocSlot)
                CMKD::getInst()->SendABKtoPI(m_pMsgList_25[i].uDestMMSI, AIS_CHANNEL_NONE, m_pMsgList_25[i].nABMMsgID, m_pMsgList_25[i].nTxSeqNum, ABK_TX_FAIL);
            ClearMsg25Buff(&m_pMsgList_25[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer for message 26
 * @return true if success, false otherwise
 */
bool CVdlTxMgr::ProcessUnscheduledTxBuffMsg26(void)
{
    //-------------------------------------------------------------
    // 메시지 26(addressed, broadcast) 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_MSG26 ; i++)
    {
        if(m_pMsgList_26[i].bReserveTx && !m_pMsgList_26[i].bAllocSlot)
        {
            if(!m_pChannel->AllocUnscheduledMultiSlotMsg(m_pMsgList_26[i].nTxMsgID, SLOTID_NONE, m_pMsgList_26[i].uNumTxSlot, true, i))
                return false;
            m_pMsgList_26[i].bAllocSlot = true;
            CMKD::getInst()->SendABKtoPI(m_pMsgList_26[i].uDestMMSI, AIS_CHANNEL_NONE, m_pMsgList_26[i].nBBMMsgID, m_pMsgList_26[i].nTxSeqNum, ABK_BROADCAST_OK);
        }
    }
    return true;
}

/**
 * @brief Periodic processing of unscheduled Tx buffer for message 26
 */
void CVdlTxMgr::PeriodicUnscheduledTxBuffMsg26(void)
{
    //-------------------------------------------------------------
    // 메시지 26(addressed, broadcast) 송신용 버퍼 처리
    //-------------------------------------------------------------
    for(int i = 0 ; i < MAX_UNSCHE_MSG26 ; i++)
    {
        if(m_pMsgList_26[i].bReserveTx && GetFrMapElapTimeSlot(m_pMsgList_26[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID) >= RATDMA_SLOT_TIMEOUT)
        {
            DEBUG_LOG("Tx-Msg26] Alloc time-out, release #%d, %d\r\n",
                    i, GetFrMapElapTimeSlot(m_pMsgList_26[i].nCurSetSlotID, OPSTATUS::nCurFrameMapSlotID));

            if(!m_pMsgList_26[i].bAllocSlot)
                CMKD::getInst()->SendABKtoPI(m_pMsgList_26[i].uDestMMSI, AIS_CHANNEL_NONE, m_pMsgList_26[i].nBBMMsgID, m_pMsgList_26[i].nTxSeqNum, ABK_TX_FAIL);
            ClearMsg26Buff(&m_pMsgList_26[i]);
        }
    }
}

/**
 * @brief Process unscheduled Tx buffer
 */
void CVdlTxMgr::ProcessUnscheduledTxBuffer(void)
{
    //-------------------------------------------------------------------------
    // priority 1 : MSG 1, 2, 3, 7, 13, 27
    // priority 2 : MSG 12, 14
    // priority 3 : MSG 10, 11, 15
    // priority 4 : MSG 5, 6, 8, 24, 25, 26
    // * 4초 내 송신 : 5, 8, 24, 25, 26
    //-------------------------------------------------------------------------

    //------------------------------------------------------------------------------------------------------
    // ITU-R 1371-5 *******.1 Random access time division multiple access algorithm
    // The RATDMA access scheme should use a probability persistent (p-persistent) algorithm as
    // described in this paragraph (see Table 14).
    // An AIS station should avoid using RATDMA. A scheduled message should primarily be used to
    // announce a future transmission to avoid RATDMA transmissions.
    // "Messages, which use the RATDMA access scheme, are stored in a priority first-in first-out (FIFO)."
    //
    // => RATDMA access scheme을 사용하는 메시지 송신시(위치보고 MSG 1,2,3,27은 priority 1 이므로 제외) FIFO 에 저장 후 우선순위에 따라 처리 순서를 결정해야한다!
    // 예) 메시지 5(priority 4) 를 송신해야할 타이밍에 메시지 10 이 수신되면 메시지 11(priority 3)을 먼저 송신한후 나중에 메시지 5를 송신해야함
    //------------------------------------------------------------------------------------------------------

    //----------------------------------------------------------------------------------------------------------------------------------------------------
    // ITU-R 1371-5 4.2.3 Management of priority assignments for messages
    // There are 4 (four) levels of message priority, namely:
    // Priority 1 (highest priority): Critical link management messages including position report messages in order to ensure the viability of the link.
    // Priority 2 (highest service priority): Safety related messages. These messages should be transmitted with a minimum of delay.
    // Priority 3: Assignment, interrogation and responses to interrogation messages.
    // Priority 4 (lowest priority): All other messages.
    // For details, refer to Table 46, Annex 8.
    // The above priorities are assigned to the relevant type of messages, thereby providing a mechanism
    // for sequencing specific messages in order of priority. The messages are serviced in order of priority.
    // This applies to both messages received and messages to be transmitted.
    // Messages with the same priority are dealt with in an FIFO order.
    // => 송신 및 수신 시 메시지 우선순위에 따라 처리해야한다. 우선순위가 같을때는 FIFO 순서로 처리해야한다.
    // => 수신 처리는 수신 시간 간격이 있으므로 priority 고려하지 않고도 실시간 처리 가능할듯!
    //---------------------------------------------------------------------------------------------------------------------------------------------------

    // Priority 1
    PeriodicUnscheduledTxBuffMsg03();
    PeriodicUnscheduledTxBuffMsg07();
    PeriodicUnscheduledTxBuffMsg13();

    // Priority 2
    PeriodicUnscheduledTxBuffMsg12();
    PeriodicUnscheduledTxBuffMsg14();

    // Priority 3
    PeriodicUnscheduledTxBuffMsg10();
    PeriodicUnscheduledTxBuffMsg11();
    PeriodicUnscheduledTxBuffMsg15();

    // Priority 4
    PeriodicUnscheduledTxBuffMsg05();
    PeriodicUnscheduledTxBuffMsg06();
    PeriodicUnscheduledTxBuffMsg08();
    PeriodicUnscheduledTxBuffMsg24();
    PeriodicUnscheduledTxBuffMsg25();
    PeriodicUnscheduledTxBuffMsg26();


    // Priority 1
    if(!ProcessUnscheduledTxBuffMsg03()) return;
    if(!ProcessUnscheduledTxBuffMsg07()) return;
    if(!ProcessUnscheduledTxBuffMsg13()) return;

    // Priority 2
    if(!ProcessUnscheduledTxBuffMsg12()) return;
    if(!ProcessUnscheduledTxBuffMsg14()) return;

    // Priority 3
    if(!ProcessUnscheduledTxBuffMsg10()) return;
    if(!ProcessUnscheduledTxBuffMsg11()) return;
    if(!ProcessUnscheduledTxBuffMsg15()) return;

    // Priority 4
    if(!ProcessUnscheduledTxBuffMsg05()) return;
    if(!ProcessUnscheduledTxBuffMsg06()) return;
    if(!ProcessUnscheduledTxBuffMsg08()) return;
    if(!ProcessUnscheduledTxBuffMsg24()) return;
    if(!ProcessUnscheduledTxBuffMsg25()) return;
    if(!ProcessUnscheduledTxBuffMsg26()) return;
}

/**
 * @brief Process frame change for Tx manager
 */
void CVdlTxMgr::ProcessFrameChg_TxMgr(void)
{
    //----------------------------------------
    // 프레임이 바뀔때마다 처리해야할것들
    //----------------------------------------

#ifdef __ENABLE_CHECK_MSG15_CNT__
    ResetMsg15TxCnt();
#else
    CheckLogNumTxSlotMsg15();
#endif

    CheckLogNumTxSlotBinMsg();
}

/**
 * @brief Clear reserved Tx message
 * @param pVdlTxMgr Pointer to Tx manager
 * @param nTxMsgID Tx message ID
 * @param nDataIdx Data index
 */
void CVdlTxMgr::ClearReserveTx(CVdlTxMgr *pVdlTxMgr, UINT16 nTxMsgID, BYTE nDataIdx)
{
    if (!pVdlTxMgr)
        return;

    switch (nTxMsgID)
    {
    case AIS_MSG_NO_ID_03:
        pVdlTxMgr->m_pMsgList_03[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_05:
        pVdlTxMgr->m_pMsgList_05[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_06:
        pVdlTxMgr->m_pMsgList_06[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_07:
        pVdlTxMgr->m_pMsgList_07[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_08:
        pVdlTxMgr->m_pMsgList_08[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_10:
        pVdlTxMgr->m_pMsgList_10[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_11:
        pVdlTxMgr->m_pMsgList_11[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_12:
        pVdlTxMgr->m_pMsgList_12[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_13:
        pVdlTxMgr->m_pMsgList_13[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_14:
        pVdlTxMgr->m_pMsgList_14[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_15:
        pVdlTxMgr->m_pMsgList_15[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_24:
        pVdlTxMgr->m_pMsgList_24[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_25:
        pVdlTxMgr->m_pMsgList_25[nDataIdx].bReserveTx = 0;
        break;
    case AIS_MSG_NO_ID_26:
        pVdlTxMgr->m_pMsgList_26[nDataIdx].bReserveTx = 0;
        break;
    }
}

/**
 * @brief Get sync state data
 * @return Sync state data
 */
BYTE CVdlTxMgr::GetSyncStateData()
{
    //-----------------------------------------------------------------------------------------------------------
    // refer to ITU-R-1371-5 Annex2 3.3.7.2.2 Table 18
    // sync state 에서 AIS_SYNC_MODE_MOBILE_SEMAPHORE 는 AIS_SYNC_MODE_BASE_INDIRECT 과 같은 값 3 으로 송신해야한다!
    //-----------------------------------------------------------------------------------------------------------
    return MIN(CSyncMgr::getInst()->m_uSyncSource, AIS_SYNC_MODE_FIELD_MAX);
}

/**
 * @brief Get position dimension data
 * @return Position dimension data
 */
DWORD CVdlTxMgr::GetPosDimensionData()
{
    DWORD dwDimension = 0;
    xANTPOS sExtendAntPos;

    // IEC 61993-2:2018 Annex I
    // 
    memset(&sExtendAntPos, 0x00, sizeof(xANTPOS));
    if (cShip::getOwnShipInst()->xNavData.uNavStatus == AIS_NAV_STATUS_PUSHING_TOWING_ALONGSIDE)
    {
        sExtendAntPos.wA = CSetupMgr::getInst()->GetExtendAntennaPosA();
        sExtendAntPos.wB = CSetupMgr::getInst()->GetExtendAntennaPosB();
        sExtendAntPos.wC = CSetupMgr::getInst()->GetExtendAntennaPosC();
        sExtendAntPos.wD = CSetupMgr::getInst()->GetExtendAntennaPosD();
    }

    switch(CSensorMgr::getInst()->GetAntType())
    {
    case EPFD_ANTENNA_TYPE_NONE:
        dwDimension = 0;
        break;
    case EPFD_ANTENNA_TYPE_INT:
        dwDimension = GetDimensionOfShip(CSetupMgr::getInst()->GetIntAntennaPos(), &sExtendAntPos);
        break;
    case EPFD_ANTENNA_TYPE_EXT:
        dwDimension = GetDimensionOfShip(CSetupMgr::getInst()->GetExtAntennaPos(), &sExtendAntPos);
        break;
    }

    return dwDimension;
}

/**
 * @brief Get communication state data for SOTDMA
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @return Communication state data
 */
DWORD CVdlTxMgr::GetCommStateData_SOTDMA(CChannelMgr *pChannel, WORD wMapSlotID)
{
    //-------------------------------------
    // refer to ITU-R-1371-5 Annex2 3.3.7
    //-------------------------------------
    DWORD dwCommStat = 0;
    WORD  wSubMsg = 0;
    FRAMEMAP_SLOTDATA *pSlot = NULL;

    if((pSlot = pChannel->GetSlotDataPtr(wMapSlotID)))
    {
        dwCommStat |= (GetSyncStateData() & 0x03) << 17;        // 2 bits
        dwCommStat |= (pSlot->nSlotTimeOut & 0x07) << 14;       // 3 bits

        switch(pSlot->nSlotTimeOut)
        {
        case 0:
            wSubMsg = pSlot->uSlotOffset;
            break;
        case 1:
            {
                int nHour = CSyncMgr::getInst()->m_utcSyncTime.nHour;
                int nMin  = CSyncMgr::getInst()->m_utcSyncTime.nMin;
                wSubMsg = (nHour & 0x1F) << 9;
                wSubMsg |= (nMin & 0x7F) << 2;
            }
            break;
        case 2:
        case 4:
        case 6:
            wSubMsg = pChannel->GetSlotIdFromFrameSlotID(wMapSlotID);
            break;
        case 3:
        case 5:
        case 7:
            wSubMsg = CUserDirMgr::getInst()->GetOwnShipNumOfRcvStFor1Min();
            break;
        }

        wSubMsg &= 0x3FFF;            // sub message : 14 bit
        dwCommStat |= wSubMsg;
    }

    return dwCommStat;
}

/**
 * @brief Get communication state data for ITDMA
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @return Communication state data
 */
DWORD CVdlTxMgr::GetCommStateData_ITDMA(CChannelMgr *pChannel, WORD wMapSlotID)
{
    //-------------------------------------
    // refer to ITU-R-1371-5 Annex2 3.3.7
    //-------------------------------------
    DWORD dwCommStat = 0;
    FRAMEMAP_SLOTDATA *pSlot = NULL;
    FRAMEMAP_SLOTDATA *pAllocSlot = NULL;
    UINT uNumAllocSlots    = 0;
    WORD wAllocSlot = SLOTID_NONE;
    UINT uSlotOffset= 0;

    if((pSlot = pChannel->GetSlotDataPtr(wMapSlotID)))
    {
        uSlotOffset= pSlot->uSlotOffset;

        if(uSlotOffset > 0)
        {
            wAllocSlot = FrameMapSlotIdAdd(wMapSlotID, uSlotOffset);
            if(pSlot->uMsgID == AIS_MSG_NO_ID_03 &&
                (pAllocSlot = pChannel->GetSlotDataPtr(wAllocSlot)) &&
                pChannel->IsInternalAllocSlot(pAllocSlot))
            {
                uNumAllocSlots = MAX(pAllocSlot->uNumSlots-1, 0);
                if(uSlotOffset > MAX_ITDMA_SLOT_OFFSET)
                {
                    uNumAllocSlots = uNumAllocSlots + 4;
                    uSlotOffset -= MAX_ITDMA_SLOT_OFFSET;
                }
            }
        }

        dwCommStat |= (GetSyncStateData() & 0x03) << 17;    // sync state, 2 bits
        dwCommStat |= (uSlotOffset & 0x1FFF) << 4;          // slot increment, 13 bits
        dwCommStat |= (uNumAllocSlots & 0x07) << 1;         // number of slots, 3 bits
        dwCommStat |= pSlot->bItdmaKeepFlag & 0x01;         // Keep flag, 1 bit

        if(pSlot->uMsgID == AIS_MSG_NO_ID_03 && uSlotOffset == 0 && uNumAllocSlots == 0 && pSlot->bItdmaKeepFlag == 0)
        {
            WARNING_LOG("xxxxxxxxxxxxx CommStat] msg-3 wrong TX-1, S: %d(%d), allocSlot: %d(%d), allocInt: %d, allocNumSlot: %d, allocMsg: %d\r\n",
                    wMapSlotID, pChannel->GetSlotIdFromFrameSlotID(wMapSlotID), wAllocSlot, pChannel->GetSlotIdFromFrameSlotID(wAllocSlot),
                    pChannel->IsInternalAllocSlot(pAllocSlot), uNumAllocSlots, pAllocSlot ? pAllocSlot->uMsgID : -1);
        }
    }

    return dwCommStat;
}

/**
 * @brief Check if Tx message is waiting for ACK
 * @param pVdlTxMgr Pointer to Tx manager
 * @param pSlot Pointer to frame map slot data
 * @return true if message is waiting for ACK, false otherwise
 */
bool CVdlTxMgr::IsTxMsgWaitingAck(CVdlTxMgr *pVdlTxMgr, FRAMEMAP_SLOTDATA *pSlot)
{
    if(pVdlTxMgr && pSlot)
    {
        if(pSlot->uMsgID == AIS_MSG_NO_ID_06 || pSlot->uMsgID == AIS_MSG_NO_ID_12)
            return true;
        if(pSlot->uMsgID == AIS_MSG_NO_ID_15 && pVdlTxMgr->m_pMsgList_15)
        {
            return (pVdlTxMgr->m_pMsgList_15[pSlot->nDataIdx].uMMSI1 != AIS_AB_MMSI_NULL);
        }
        if(pSlot->uMsgID == AIS_MSG_NO_ID_25 && pVdlTxMgr->m_pMsgList_25)
        {
            return (pVdlTxMgr->m_pMsgList_25[pSlot->nDataIdx].uDestMMSI != AIS_AB_MMSI_NULL);
        }
        if(pSlot->uMsgID == AIS_MSG_NO_ID_26 && pVdlTxMgr->m_pMsgList_26)
        {
            return (pVdlTxMgr->m_pMsgList_26[pSlot->nDataIdx].uDestMMSI != AIS_AB_MMSI_NULL);
        }
    }
    return false;
}

/**
 * @brief Get position, COG, SOG, and timestamp for Tx message
 * @param pFullPos Pointer to position data
 * @param pSOG Pointer to SOG data
 * @param pCOG Pointer to COG data
 * @param pSEC Pointer to timestamp data
 */
void  GetAisPosCogSogTxValueMsg_01_02_03(POS_HIGH *pFullPos, int *pSOG, int *pCOG, int *pSEC)
{
    if(!CSensorMgr::getInst()->m_pPosSensor)
    {
        pFullPos->nLON = AIS_LON_VDL_NULL;
        pFullPos->nLAT = AIS_LAT_VDL_NULL;
    }
    else
        *pFullPos = cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosH;

    *pSEC = cShip::getOwnShipInst()->xDynamicData.nTimeStamp;
    *pCOG = cShip::getOwnShipInst()->xDynamicData.nCOG;
    *pSOG = cShip::getOwnShipInst()->xDynamicData.nSOG;
}

/**
 * @brief Make AIS Tx slot message for Message 1, 2, or 3
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg_01_02_03(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot)
        return 0;
    return MakeAisTxMsg_01_02_03(pChannel, wMapSlotID, pSlot->uMsgID, pTxSlotData);
}

/**
 * @brief Make AIS Tx message for Message 1, 2, or 3
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param uMsgID Message ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxMsg_01_02_03(CChannelMgr *pChannel, WORD wMapSlotID, UINT16 uMsgID, BYTE *pTxSlotData)
{
    //------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ********
    // If the valid position does not have a timestamp (time stamp not available = 60) transmit the position report with time stamp set to 60.
    //------------------------------------------------------------------------------------------------------------------------------------------
    const    int SIZE_TX_BITS = 168;

    DWORD    dwCommStat = 0;

    POS_HIGH xPos;
    int   nSOG, nCOG, nSEC;
    GetAisPosCogSogTxValueMsg_01_02_03(&xPos, &nSOG, &nCOG, &nSEC);

    int nHDG = GetHdgAisFieldData();
    int nRotData = GetRotAisFieldData();

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6, uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2, 0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30, cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  4, cShip::getOwnShipInst()->xNavData.uNavStatus);
    SetAisTxMsgFieldPack(pTxSlotData,  42,  8, nRotData);
    SetAisTxMsgFieldPack(pTxSlotData,  50, 10, nSOG);
    SetAisTxMsgFieldPack(pTxSlotData,  60,  1, cShip::getOwnShipInst()->xDynamicData.bPosAccFlag);
    SetAisTxMsgFieldPack(pTxSlotData,  61, 28, xPos.nLON);
    SetAisTxMsgFieldPack(pTxSlotData,  89, 27, xPos.nLAT);
    SetAisTxMsgFieldPack(pTxSlotData, 116, 12, nCOG);
    SetAisTxMsgFieldPack(pTxSlotData, 128,  9, nHDG);
    SetAisTxMsgFieldPack(pTxSlotData, 137,  6, nSEC);
    SetAisTxMsgFieldPack(pTxSlotData, 143,  2, cShip::getOwnShipInst()->xNavData.uManoeuvre);
    SetAisTxMsgFieldPack(pTxSlotData, 145,  3, 0);
    SetAisTxMsgFieldPack(pTxSlotData, 148,  1, cShip::getOwnShipInst()->xDynamicData.bRaimFlag);

    if(uMsgID == AIS_MSG_NO_ID_01 || uMsgID == AIS_MSG_NO_ID_02)
        dwCommStat = GetCommStateData_SOTDMA(pChannel, wMapSlotID);
    else if(uMsgID == AIS_MSG_NO_ID_03)
    {
        FRAMEMAP_SLOTDATA *pSlot = NULL;
        if(!(pSlot = pChannel->GetSlotDataPtr(wMapSlotID)) || pSlot->uMsgID != AIS_MSG_NO_ID_03)
            return 0;
        if(pSlot->uNumSlots <= 0 && pSlot->bItdmaKeepFlag == 0 && pSlot->uSlotOffset == 0)
            return 0;

        if((dwCommStat = GetCommStateData_ITDMA(pChannel, wMapSlotID)) == (DWORD)-1)
            return 0;
    }

    SetAisTxMsgFieldPack(pTxSlotData, 149, 19, dwCommStat);
    cShip::getOwnShipInst()->xOwnShipPosReportSec[1] = cShip::getOwnShipInst()->xOwnShipPosReportSec[0];
    cShip::getOwnShipInst()->xOwnShipPosReportSec[0] = cTimerSys::getInst()->GetCurTimerSec();

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 5
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg05(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //-------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ********
    // On changeover from one status to another a new Message 5 shall be transmitted immediately
    // when the reference point for the reported position has changed and an "ALR" sentence as
    // described above shall be output to the presentation interface.
    //-------------------------------------------------------------------------------------------------------

    const  int SIZE_TX_BITS = 424;
    char   pstrTmp[32];

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,  0,  6, AIS_MSG_NO_ID_05);
    SetAisTxMsgFieldPack(pTxSlotData,  6,  2, 0);
    SetAisTxMsgFieldPack(pTxSlotData,  8, 30, cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData, 38,  2, AIS_VERSION_INDICATOR);

    SetAisTxMsgFieldPack(pTxSlotData, 40, 30, cShip::getOwnShipInst()->xStaticData.dwImoID);

    memset(pstrTmp, '\0', LEN_MAX_CALLSIGN+1);
    strcpy(pstrTmp, cShip::getOwnShipInst()->xStaticData.vCallSign);
    MakeValidAisAsciiString(pstrTmp, LEN_MAX_CALLSIGN);

    SetAisTxMsgFieldPackStr(pTxSlotData, 70, 42, pstrTmp, LEN_MAX_CALLSIGN);

    memset(pstrTmp, '\0', LEN_MAX_SHIP_NAME+1);
    strcpy(pstrTmp, cShip::getOwnShipInst()->xStaticData.vShipName);
    MakeValidAisAsciiString(pstrTmp, LEN_MAX_SHIP_NAME);
    SetAisTxMsgFieldPackStr(pTxSlotData, 112, 120, pstrTmp, LEN_MAX_SHIP_NAME);

    SetAisTxMsgFieldPack(pTxSlotData, 232,  8, cShip::getOwnShipInst()->xNavData.uShipType);

    SetAisTxMsgFieldPack(pTxSlotData, 240, 30, GetPosDimensionData());
    SetAisTxMsgFieldPack(pTxSlotData, 270,  4, CSensorMgr::getInst()->GetEpfDeviceType());

    SetAisTxMsgFieldPack(pTxSlotData, 274, 20, SetETAData(&cShip::getOwnShipInst()->xNavData.xETA));
    SetAisTxMsgFieldPack(pTxSlotData, 294,  8, cShip::getOwnShipInst()->xNavData.uDraught);

    memset(pstrTmp, '\0', LEN_MAX_DESTINATION+1);
    strcpy(pstrTmp, cShip::getOwnShipInst()->xNavData.pstrDestination);
    MakeValidAisAsciiString(pstrTmp, LEN_MAX_DESTINATION);
    SetAisTxMsgFieldPackStr(pTxSlotData, 302,120, pstrTmp, LEN_MAX_DESTINATION);

    SetAisTxMsgFieldPack(pTxSlotData, 422, 1, cShip::getOwnShipInst()->xStaticData.nDTE);
    SetAisTxMsgFieldPack(pTxSlotData, 423, 1, 0);

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 6
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg06(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_ADDRMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE_ADDRMSG *pParam = (TDMA_UNSCHE_ADDRMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_06[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        return 0;
    }

    int SIZE_TX_BITS = GetByteBoundaryBitSize(pParam->nNumDataBits+72);

    DEBUG_LOG("MakeMSG-6] mSlot : %d, param : 0x%x, size : %d, seq : %d, dest : %d, retry : %d, msgData : 0x%x, msgLen : %d\r\n",
            wMapSlotID, pParam, SIZE_TX_BITS, pParam->nTxSeqNum, pParam->uDestMMSI, pParam->nTxRetryCnt, pParam->pMsgBinData, pParam->nNumDataBits);

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,  0,  6, pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,  6,  2, 0);
    SetAisTxMsgFieldPack(pTxSlotData,  8, 30, cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData, 38,  2, pParam->nTxSeqNum);
    SetAisTxMsgFieldPack(pTxSlotData, 40, 30, pParam->uDestMMSI);
    SetAisTxMsgFieldPack(pTxSlotData, 70,  1, (pParam->nTxRetryCnt > 0) ? 1 : 0);
    SetAisTxMsgFieldPack(pTxSlotData, 71,  1, 0);
    SetAisTxMsgFieldPackY(pTxSlotData, 72, pParam->nNumDataBits,  (UCHAR*)pParam->pMsgBinData);

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 7 or 13
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg_07_13(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    TDMA_UNSCHE_ACKMSG *pParam;
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_ADDRMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    if (pSlot->uMsgID == AIS_MSG_NO_ID_07)
        pParam = (TDMA_UNSCHE_ACKMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_07[pSlot->nDataIdx];
    else
        pParam = (TDMA_UNSCHE_ACKMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_13[pSlot->nDataIdx];

    if(!pParam || !pParam->bAllocSlot)
    {
        return 0;
    }

    int SIZE_TX_BITS = 72;

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,  40, 30,  pParam->uDestMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  70,  2,  pParam->nTxSeqNum);

    //-------------------------------------
    // refer to ITU-R-1371-5 Annex8. 3.5
    // Fields below are omitted
    //-------------------------------------

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 8
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg08(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_BROADMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE_BROADMSG *pParam = (TDMA_UNSCHE_BROADMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_08[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        ERROR_LOG("MakeMSG-8] Error, param NULL or not alloc!\r\n");
        return 0;
    }

    int SIZE_TX_BITS = GetByteBoundaryBitSize(pParam->nNumDataBits+40);

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  0);
    SetAisTxMsgFieldPackY(pTxSlotData,  40, pParam->nNumDataBits,  (UCHAR*)(pParam->pMsgBinData));
    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 10
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg_10(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_ADDRMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_ONESLOT_ADDRMSG *pParam = (TDMA_ONESLOT_ADDRMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_10[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        return 0;
    }

    int SIZE_TX_BITS = 72;

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,  40, 30,  pParam->uDestMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  70,  2,  0);

    //-------------------------------------
    // refer to ITU-R-1371-5 Annex8. 3.5
    // Fields below are omitted
    //-------------------------------------

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 11
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg11(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    const    int SIZE_TX_BITS = 168;

    POS_HIGH xPos = cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosH;
    if (CSensorMgr::getInst()->IsGnssLost())
    {
        xPos.nLON = AIS_LON_VDL_NULL;
        xPos.nLAT = AIS_LAT_VDL_NULL;
    }

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6, AIS_MSG_NO_ID_11);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2, 0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30, cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38, 14, CSyncMgr::getInst()->m_utcSyncDate.nYear);
    SetAisTxMsgFieldPack(pTxSlotData,  52,  4, CSyncMgr::getInst()->m_utcSyncDate.nMon);
    SetAisTxMsgFieldPack(pTxSlotData,  56,  5, CSyncMgr::getInst()->m_utcSyncDate.nDay);
    SetAisTxMsgFieldPack(pTxSlotData,  61,  5, CSyncMgr::getInst()->m_utcSyncTime.nHour);
    SetAisTxMsgFieldPack(pTxSlotData,  66,  6, CSyncMgr::getInst()->m_utcSyncTime.nMin);
    SetAisTxMsgFieldPack(pTxSlotData,  72,  6, CSyncMgr::getInst()->m_utcSyncTime.nSec);
    SetAisTxMsgFieldPack(pTxSlotData,  78,  1, cShip::getOwnShipInst()->xDynamicData.bPosAccFlag);
    SetAisTxMsgFieldPack(pTxSlotData,  79, 28, xPos.nLON);
    SetAisTxMsgFieldPack(pTxSlotData, 107, 27, xPos.nLAT);
    SetAisTxMsgFieldPack(pTxSlotData, 134,  4, CSensorMgr::getInst()->GetEpfDeviceType());
    SetAisTxMsgFieldPack(pTxSlotData, 138,  1, 0);
    SetAisTxMsgFieldPack(pTxSlotData, 139,  9, 0);
    SetAisTxMsgFieldPack(pTxSlotData, 148,  1, cShip::getOwnShipInst()->xDynamicData.bRaimFlag);

    DWORD dwCommStat;
    if((dwCommStat = GetCommStateData_ITDMA(pChannel, wMapSlotID)) == (DWORD)-1)
        return 0;

    SetAisTxMsgFieldPack(pTxSlotData, 149, 19, dwCommStat);
    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 12
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg12(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_ADDRMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE_ADDRMSG *pParam = (TDMA_UNSCHE_ADDRMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_12[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        return 0;
    }

    int SIZE_TX_BITS = GetByteBoundaryBitSize(pParam->nNumDataBits+72);
    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  pParam->nTxSeqNum);
    SetAisTxMsgFieldPack(pTxSlotData,  40, 30,  pParam->uDestMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  70,  1,  pParam->nTxRetryCnt > 0 ? 1 : 0);
    SetAisTxMsgFieldPack(pTxSlotData,  71,  1,  0);
    SetAisTxMsgFieldPackY(pTxSlotData,  72, pParam->nNumDataBits, (UCHAR*)(pParam->pMsgBinData));

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 14
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg14(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_BROADMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE_BROADMSG *pParam = (TDMA_UNSCHE_BROADMSG *)&pChannel->m_pVdlTxMgr->m_pMsgList_14[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        return 0;
    }

    int SIZE_TX_BITS = GetByteBoundaryBitSize(pParam->nNumDataBits+40);
    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  0);
    SetAisTxMsgFieldPackY(pTxSlotData,  40, pParam->nNumDataBits, (UCHAR*)(pParam->pMsgBinData));

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 15
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg15(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= NUM_BROADMSG)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE15_INFO *pParam = (TDMA_UNSCHE15_INFO *)&pChannel->m_pVdlTxMgr->m_pMsgList_15[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        return 0;
    }

    int SIZE_TX_BITS = GetByteBoundaryBitSize(pParam->nNumDataBits);
    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,  40, 30,  pParam->uMMSI1);
    SetAisTxMsgFieldPack(pTxSlotData,  70,  6,  pParam->bMsgID11);
    SetAisTxMsgFieldPack(pTxSlotData,  76, 12,  0);
    SetAisTxMsgFieldPack(pTxSlotData,  88,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,  90,  6,  pParam->bMsgID12);
    SetAisTxMsgFieldPack(pTxSlotData,  96, 12,  0);
    SetAisTxMsgFieldPack(pTxSlotData, 108,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData, 110, 30,  pParam->uMMSI2);
    SetAisTxMsgFieldPack(pTxSlotData, 140,  6,  pParam->bMsgID21);
    SetAisTxMsgFieldPack(pTxSlotData, 146, 12,  0);
    SetAisTxMsgFieldPack(pTxSlotData, 158,  2,  0);

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 18
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg18(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //-------------------------------------------------------------------------------------------------------
    // Refer to ITU-R 1371-5 Ann.8
    // When requesting the transmission of a Message 24 from a Class A, the AIS station should respond
    // with part B, which may contain the vendor ID only.
    //-------------------------------------------------------------------------------------------------------

    const    int SIZE_TX_BITS = 168;

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    DWORD    dwCommStat = 0;

    POS_HIGH xPos;
    int   nSOG, nCOG, nSEC;
    GetAisPosCogSogTxValueMsg_01_02_03(&xPos, &nSOG, &nCOG, &nSEC);

    int nHDG = GetHdgAisFieldData();
    int nRotData = GetRotAisFieldData();

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  AIS_MSG_NO_ID_18);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  8,  0);
    SetAisTxMsgFieldPack(pTxSlotData,  46, 10, nSOG);
    SetAisTxMsgFieldPack(pTxSlotData,  56,  1, cShip::getOwnShipInst()->xDynamicData.bPosAccFlag);
    SetAisTxMsgFieldPack(pTxSlotData,  57, 28, xPos.nLON);
    SetAisTxMsgFieldPack(pTxSlotData,  85, 27, xPos.nLAT);
    SetAisTxMsgFieldPack(pTxSlotData, 112, 12, nCOG);
    SetAisTxMsgFieldPack(pTxSlotData, 124,  9, nHDG);
    SetAisTxMsgFieldPack(pTxSlotData, 133,  6, nSEC);                                          // time stamp
    SetAisTxMsgFieldPack(pTxSlotData, 139,  2, 0);                                             // spare
    SetAisTxMsgFieldPack(pTxSlotData, 141,  1, CSetupMgr::getInst()->IsAisBClassSO() ? 0 : 1); // Class B unit flag, 0 = "SO", 1 = "CS"
    SetAisTxMsgFieldPack(pTxSlotData, 142,  1, !CMKD::getInst()->GetDTEFlag());                // Class B display flag, 1 = equipped with integrated display, is capable of displaying message 12 and 14
    SetAisTxMsgFieldPack(pTxSlotData, 143,  1, 0);                                             // Class B DSC flag, 1 = Equipped with DSC function(dedicated or time-shared)
    SetAisTxMsgFieldPack(pTxSlotData, 144,  1, 1);                                             // Class B band flag, 1 = Capable of operating over the wholed marine band
    SetAisTxMsgFieldPack(pTxSlotData, 145,  1, CROSMgr::getInst()->IsTrModeRunning());         // Class B Message 22 flag, 1 = Frequency management via Message 22
    SetAisTxMsgFieldPack(pTxSlotData, 146,  1, CLayerNetwork::getInst()->CheckAssignedModeRunning());    // Mode flag, 1 = Station operating in assigned mode
    SetAisTxMsgFieldPack(pTxSlotData, 147,  1, cShip::getOwnShipInst()->xDynamicData.bRaimFlag);         // RAIM flag
    SetAisTxMsgFieldPack(pTxSlotData, 148,  1, 0);                                             // Communication state selector flag

    dwCommStat = GetCommStateData_SOTDMA(pChannel, wMapSlotID);
    SetAisTxMsgFieldPack(pTxSlotData, 149, 19, dwCommStat);                                    // Communication state

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 19
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg19(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //---------------------------------------------------------------------------------
    // For future equipment: this message is not needed and should not be used.
    // All content is covered by Message 18, Message 24A and 24B.
    //---------------------------------------------------------------------------------
    return 0;
}

/**
 * @brief Make AIS Tx slot message for Message 24 Part A
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg24_PartA(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //-------------------------------------------------------------------------------------------------------
    // Refer to ITU-R 1371-5 Annex.8
    //-------------------------------------------------------------------------------------------------------
    // Equipment that supports Message 24 part A shall transmit once every 6 min alternating between channels.
    // Message 24 Part A may be used by any AIS station to associate a MMSI with a name.
    // Message 24 Part A and Part B should be transmitted once every 6 min by class B "CS" and class B "SO"
    // shipborne mobile equipment. The message consists of two parts. Message 24B should be
    // transmitted within 1 min following Message 24A.
    // When the parameter value of dimension of ship/reference for position or type of electronic position
    // fixing device is changed, Class-B "CS" and Class-B "SO" should transmit Message 24B.
    // When requesting the transmission of a Message 24 from a Class B "CS" or Class B "SO",
    // the AIS    station should respond with part A and part B.
    // When requesting the transmission of a Message 24 from a Class A, the AIS station should respond
    // with part B, which may contain the vendor ID only
    //-------------------------------------------------------------------------------------------------------

    const    int SIZE_TX_BITS = 160;            // 2 slots
    char    pstrTmp[100];

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  AIS_MSG_NO_ID_24);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  MESSAGE24_PART_A);                        // Part number, always 0 for Part A

    memset(pstrTmp, '\0', LEN_MAX_SHIP_NAME+1);
    strcpy(pstrTmp, cShip::getOwnShipInst()->xStaticData.vShipName);
    MakeValidAisAsciiString(pstrTmp, LEN_MAX_SHIP_NAME);
    SetAisTxMsgFieldPackStr(pTxSlotData, 40,120,  pstrTmp, LEN_MAX_SHIP_NAME);

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 24 Part B
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg24_PartB(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //-------------------------------------------------------------------------------------------------------
    // Refer to ITU-R 1371-5 Ann.8
    // When requesting the transmission of a Message 24 from a Class A, the AIS station should respond
    // with part B, which may contain the vendor ID only.
    //-------------------------------------------------------------------------------------------------------

    const    int SIZE_TX_BITS = 168;
    char pstrTmp[100];

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  AIS_MSG_NO_ID_24);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  2,  MESSAGE24_PART_B);
    SetAisTxMsgFieldPack(pTxSlotData,  40,  8,  cShip::getOwnShipInst()->xNavData.uShipType);

    memset(pstrTmp, '\0', LEN_MAX_VENDORID+1);

    //-------------------------------------------------
    // Refer to IEC 61993-2 ed3.0 ******** CCS_witness
    //-------------------------------------------------
    strcpy(pstrTmp, STR_MANUFACTURER_CODE);

    MakeValidAisAsciiString(pstrTmp, LEN_MAX_VENDORID);
    SetAisTxMsgFieldPackStr(pTxSlotData,  48, 42,  pstrTmp, LEN_MAX_VENDORID);

    memset(pstrTmp, '\0', LEN_MAX_CALLSIGN+1);
    strcpy(pstrTmp, cShip::getOwnShipInst()->xStaticData.vCallSign);
    MakeValidAisAsciiString(pstrTmp, LEN_MAX_CALLSIGN);
    SetAisTxMsgFieldPackStr(pTxSlotData,  90, 42,  pstrTmp, LEN_MAX_CALLSIGN);
    SetAisTxMsgFieldPack(pTxSlotData, 132, 30,  GetPosDimensionData());
    SetAisTxMsgFieldPack(pTxSlotData, 162,  4,  CSensorMgr::getInst()->GetEpfDeviceType());
    SetAisTxMsgFieldPack(pTxSlotData, 166,  2, 0);

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx slot message for Message 25
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg25(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //------------------------------------------
    // Message 25 : Single slot binary message
    //------------------------------------------

    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_MSG25)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE_MSG25 *pParam = (TDMA_UNSCHE_MSG25 *)&pChannel->m_pVdlTxMgr->m_pMsgList_25[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        WARNING_LOG(" MakeMSG-25] Error, param NULL or not alloc!\r\n");
        return 0;
    }

    DEBUG_LOG("MakeMsg-25] pParam : 0x%x, dest : %d, binFlag : %d, msgBits : %d\r\n",
            (DWORD)pParam, pParam->uDestMMSI, pParam->bBinDataFlag, pParam->nNumDataBits);

    int nSizeTxBits = GetByteBoundaryBitSize(pParam->nNumDataBits+72);

    memset(pTxSlotData, 0x00, nSizeTxBits / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  1,  pParam->uDestMMSI == AIS_AB_MMSI_NULL ? DESTINATION_BROAST : DESTINATION_ADDRES);
    SetAisTxMsgFieldPack(pTxSlotData,  39,  1,  pParam->bBinDataFlag);

    if(pParam->uDestMMSI == AIS_AB_MMSI_NULL)
    {
        SetAisTxMsgFieldPackY(pTxSlotData,  40, pParam->nNumDataBits,  (UCHAR*)(pParam->pMsgBinData));
        nSizeTxBits = GetByteBoundaryBitSize(pParam->nNumDataBits+40);
    }
    else
    {
        SetAisTxMsgFieldPack(pTxSlotData,  40,  30,  pParam->uDestMMSI);
        SetAisTxMsgFieldPack(pTxSlotData,  70,   2,  0);
        SetAisTxMsgFieldPackY(pTxSlotData,  72, pParam->nNumDataBits,  (UCHAR*)(pParam->pMsgBinData));
    }

    return nSizeTxBits;
}

/**
 * @brief Make AIS Tx slot message for Message 26
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg26(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //-----------------------------------------------------------------------
    // Message 26 : Multiple slot binary message with communication state
    //-----------------------------------------------------------------------

    int nDataBits;
    int nCopyBits;

    FRAMEMAP_SLOTDATA *pSlot = pChannel->GetSlotDataPtr(wMapSlotID);
    if(!pSlot || pSlot->nDataIdx >= MAX_UNSCHE_MSG26)
        return 0;

    if (!pChannel || !pChannel->m_pVdlTxMgr)
        return 0;

    TDMA_UNSCHE_MSG26 *pParam = (TDMA_UNSCHE_MSG26 *)&pChannel->m_pVdlTxMgr->m_pMsgList_26[pSlot->nDataIdx];
    if(!pParam || !pParam->bAllocSlot)
    {
        WARNING_LOG("MakeMSG-26] Error, param NULL or not alloc!\r\n");
        return 0;
    }

    int nSizeTxBits = GetByteBoundaryBitSize(pParam->nNumDataBits+96);

    memset(pTxSlotData, 0x00, nSizeTxBits / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  pSlot->uMsgID);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  0);
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  1,  pParam->uDestMMSI == AIS_AB_MMSI_NULL ? DESTINATION_BROAST : DESTINATION_ADDRES);
    SetAisTxMsgFieldPack(pTxSlotData,  39,  1,  pParam->bBinDataFlag);

    const int nDataBitsPadded = GetByteBoundaryBitSize(pParam->nNumDataBits);
    nDataBits = nDataBitsPadded;
    nCopyBits = 0;

    if(pParam->uDestMMSI == AIS_AB_MMSI_NULL)
    {
        nSizeTxBits = nDataBitsPadded+64;

        SetAisTxMsgFieldPackY(pTxSlotData,  40, nDataBitsPadded,  (UCHAR*)(pParam->pMsgBinData));
        SetAisTxMsgFieldPack(pTxSlotData,  40+nDataBitsPadded,   4,  0);                                                // Spare 4 bits
        SetAisTxMsgFieldPack(pTxSlotData,  44+nDataBitsPadded,   1,  1);                                                // Set to ITDMA commState

        DWORD dwCommStat;
        if((dwCommStat = GetCommStateData_ITDMA(pChannel, wMapSlotID)) == (DWORD)-1)
            return 0;

        SetAisTxMsgFieldPack(pTxSlotData,  45+nDataBitsPadded,  19,  dwCommStat);                                        // ITDMA Communication state
    }
    else
    {
        nSizeTxBits = nDataBitsPadded+96;

        SetAisTxMsgFieldPack(pTxSlotData,  40,  30,  pParam->uDestMMSI);
        SetAisTxMsgFieldPack(pTxSlotData,  70,   2,  0);                        // Spare 2 bits
        SetAisTxMsgFieldPackY(pTxSlotData,  72, nDataBitsPadded,  (UCHAR*)(pParam->pMsgBinData));
        SetAisTxMsgFieldPack(pTxSlotData,  72+nDataBitsPadded,   4,  0);                                                // Spare 4 bits
        SetAisTxMsgFieldPack(pTxSlotData,  76+nDataBitsPadded,   1,  1);                                                // Set to ITDMA commState

        DWORD dwCommStat;
        if((dwCommStat = GetCommStateData_ITDMA(pChannel, wMapSlotID)) == (DWORD)-1)
            return 0;

        SetAisTxMsgFieldPack(pTxSlotData,  77+nDataBitsPadded,  19,  dwCommStat);                                        // ITDMA Communication state
    }

    return nSizeTxBits;
}

/**
 * @brief Get position, COG, SOG, and timestamp for Tx message
 * @param pLowPos Pointer to low precision position data
 * @param pSOG Pointer to SOG data
 * @param pCOG Pointer to COG data
 * @param pSEC Pointer to timestamp data
 */
void  GetAisPosCogSogTxValue_LR(POS_LOW *pLowPos, int *pSOG, int *pCOG, int *pSEC)
{
    if(CSensorMgr::getInst()->IsGnssLost())
    {
        pLowPos->nLON = AIS_LON_NULL_INT * AIS_POS_LOW_SCALE;
        pLowPos->nLAT = AIS_LAT_NULL_INT * AIS_POS_LOW_SCALE;
    }
    else
    	CAisLib::AisFullPosToLow(&(cShip::getOwnShipInst()->xDynamicData.sShipPosLastValid.xPosH), pLowPos);

    *pSEC    = cShip::getOwnShipInst()->xDynamicData.nTimeStamp;

    if(cShip::getOwnShipInst()->xDynamicData.nCOG == NMEA_COG_NULL)
        *pCOG = 511;
    else
        *pCOG = cShip::getOwnShipInst()->xDynamicData.nCOG / NMEA_SCALE_COG;

    if(cShip::getOwnShipInst()->xDynamicData.nSOG == NMEA_SOG_NULL)
        *pSOG = 63;
    else
        *pSOG = cShip::getOwnShipInst()->xDynamicData.nSOG / NMEA_SCALE_SOG;
}

/**
 * @brief Make AIS Tx slot message for Message 27
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisTxSlotMsg27(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData)
{
    //------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 ********
    // If the valid position does not have a timestamp (time stamp not available = 60) transmit the position report with time stamp set to 60.
    //------------------------------------------------------------------------------------------------------------------------------------------
    const    int SIZE_TX_BITS = 96;

    POS_LOW xPos;
    int   nSOG, nCOG, nSEC;
    GetAisPosCogSogTxValue_LR(&xPos, &nSOG, &nCOG, &nSEC);

    memset(pTxSlotData, 0x00, SIZE_TX_BITS / 8);

    SetAisTxMsgFieldPack(pTxSlotData,   0,  6,  AIS_MSG_NO_ID_27);
    SetAisTxMsgFieldPack(pTxSlotData,   6,  2,  3);                                    // repeat indicator : always 3
    SetAisTxMsgFieldPack(pTxSlotData,   8, 30,  cShip::getOwnShipInst()->xStaticData.dMMSI);
    SetAisTxMsgFieldPack(pTxSlotData,  38,  1,  cShip::getOwnShipInst()->xDynamicData.bPosAccFlag);
    SetAisTxMsgFieldPack(pTxSlotData,  39,  1,  cShip::getOwnShipInst()->xDynamicData.bRaimFlag);
    SetAisTxMsgFieldPack(pTxSlotData,  40,  4,  cShip::getOwnShipInst()->xNavData.uNavStatus);
    SetAisTxMsgFieldPack(pTxSlotData,  44, 18, xPos.nLON);                                // 1 / 10 min
    SetAisTxMsgFieldPack(pTxSlotData,  62, 17, xPos.nLAT);                                // 1 / 10 min
    SetAisTxMsgFieldPack(pTxSlotData,  79, 6, nSOG);
    SetAisTxMsgFieldPack(pTxSlotData,  85, 9, nCOG);
    SetAisTxMsgFieldPack(pTxSlotData,  94,  1, 0);
    SetAisTxMsgFieldPack(pTxSlotData,  95,  1, 0);

    return SIZE_TX_BITS;
}

/**
 * @brief Make AIS Tx message for random PRBS
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisMsgRandomPRBS(BYTE *pTxSlotData)
{
    //-------------------
    // PRBS-9
    //-------------------
    const int TX_BUFF_BYTES = 168;
    WORD start = GetRandomValueOneBySEED(1, 255);
    WORD a = start;
    int i;

    for(i = 1; ; i++)
    {
        int newbit = (((a >> 8) ^ (a >> 4)) & 1);
        a = ((a << 1) | newbit) & 0x1FF;
        pTxSlotData[i] = a;

        if (i >= TX_BUFF_BYTES-1)
        {
            break;
        }
    }

    return TX_BUFF_BYTES;
}

/**
 * @brief Make AIS Tx message for fixed PRBS
 * @param pTxSlotData Pointer to Tx slot data
 * @return Size of Tx slot data in bits
 */
int CVdlTxMgr::MakeAisMsgFixedPRBS(BYTE *pTxSlotData)
{
    pTxSlotData[0] = 0x04;
    pTxSlotData[1] = 0xF6;
    pTxSlotData[2] = 0xD5;
    pTxSlotData[3] = 0x8E;
    pTxSlotData[4] = 0xFB;
    pTxSlotData[5] = 0x01;
    pTxSlotData[6] = 0x4C;
    pTxSlotData[7] = 0xC7;
    pTxSlotData[8] = 0x76;
    pTxSlotData[9] = 0x1E;
    pTxSlotData[10] = 0xBC;
    pTxSlotData[11] = 0x5B;
    pTxSlotData[12] = 0xE5;
    pTxSlotData[13] = 0x92;
    pTxSlotData[14] = 0xA5;
    pTxSlotData[15] = 0x2F;
    pTxSlotData[16] = 0x53;
    pTxSlotData[17] = 0xF9;
    pTxSlotData[18] = 0xD6;
    pTxSlotData[19] = 0xE7;
    pTxSlotData[20] = 0xE0;
    return 168;
}

/**
 * @brief Make VDL TX message
 * @param pChannel Channel manager
 * @param wMapSlotID Map slot ID
 * @return True if message is made, false otherwise
 */
bool CVdlTxMgr::MakeVdlTxMsg(CChannelMgr *pChannel, WORD wMapSlotID)
{
    //-------------------------------------------------------------------------
    // priority 1 : MSG 1, 2, 3, 7, 13, 27
    // priority 2 : MSG 12, 14
    // priority 3 : MSG 10, 11, 15
    // priority 4 : MSG 5, 6, 8, 24, 25, 26
    //-------------------------------------------------------------------------

    int nMsgSizeBits = 0;
    FRAMEMAP_SLOTDATA *pSlot = NULL;
    int nMsgRawBits = 0;
    DWORD dwStartTick = SysGetSystemTimer();

    if(!pChannel->IsTxChHealthy())
        return false;

    if(!(pSlot = pChannel->GetSlotDataPtr(wMapSlotID)))
        return false;

    int nTxChID = pSlot->wTxChNum;

    // If AIS type is B class, Message 18 is used instead of Message 1, 2, 3
    if (CSetupMgr::getInst()->IsAisBClass())
    {
        if(pSlot->uMsgID == AIS_MSG_NO_ID_01 || pSlot->uMsgID == AIS_MSG_NO_ID_02 || pSlot->uMsgID == AIS_MSG_NO_ID_03)
            pSlot->uMsgID = AIS_MSG_NO_ID_18;
    }

    WORD wTxSlotID = pChannel->GetSlotIdFromFrameSlotID(wMapSlotID);
    BYTE *pTxSlotBuff = pChannel->m_pVdlTxMgr->m_pTxSlotBuff;

    DEBUG_LOG("MakeMsg] before, M : %d, freq:%d, Fr : %d, Tx : %d, mSlot : %d, TMO : %d, %d\r\n",
            pSlot->uMsgID,
            cAisModem::getInst()->GetAisTxFrequency(), wMapSlotID, wTxSlotID, cAisModem::getInst()->GetSlotNoCounter(),
            pSlot->nSlotTimeOut, pChannel->m_nLastScheduledTxSlotID);

    cAisModem::getInst()->SetInfinteTxMode(false);

    switch (pSlot->uMsgID)
    {
    case AIS_MSG_NO_ID_01:
    case AIS_MSG_NO_ID_02:
    case AIS_MSG_NO_ID_03:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg_01_02_03(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        pChannel->m_dwChLastPosTxSec = cTimerSys::getInst()->GetCurTimerSec();
        break;

    case AIS_MSG_NO_ID_05:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg05(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_06:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg06(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_07:
    case AIS_MSG_NO_ID_13:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg_07_13(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_10:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg_10(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_08:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg08(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_11:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg11(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_12:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg12(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_14:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg14(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_15:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg15(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_18:
        if (CSetupMgr::getInst()->IsAisBClass())
        {
            if(!(nMsgSizeBits = MakeAisTxSlotMsg18(pChannel, wMapSlotID, pTxSlotBuff)))
            {
                return false;
            }
        }
        break;

    case AIS_MSG_NO_ID_19:
        if(CSetupMgr::getInst()->IsAisBClass())
        {
            if(!(nMsgSizeBits = MakeAisTxSlotMsg19(pChannel, wMapSlotID, pTxSlotBuff)))
            {
                return false;
            }
        }
        break;

    case AIS_MSG_NO_ID_24_A:
        if (CSetupMgr::getInst()->IsAisBClass())
        {
            if(!(nMsgSizeBits = MakeAisTxSlotMsg24_PartA(pChannel, wMapSlotID, pTxSlotBuff)))
            {
                return false;
            }
        }
        break;

    case AIS_MSG_NO_ID_24:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg24_PartB(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_25:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg25(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_26:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg26(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        break;

    case AIS_MSG_NO_ID_27:
        if(!(nMsgSizeBits = MakeAisTxSlotMsg27(pChannel, wMapSlotID, pTxSlotBuff)))
        {
            return false;
        }
        nTxChID = pChannel->m_nLongRangeChID;
        break;

    case AIS_TESTMODE_MSGID_0101:

#ifndef __TESTSIGNAL_USE_PATTERN__
        memset(pTxSlotBuff, 0x55, INFINITE_TESTDATA_BYTES);
        nMsgSizeBits = MakeTestInfiniteSeqAisTxRawData(pTxSlotBuff, INFINITE_TESTDATA_BITS);
        MakeAisGmskTxSendData(nTxChID, wTxSlotID, pSlot->uMsgID);
        cAisModem::getInst()->SetInfinteTxMode(true);
#else
        nMsgSizeBits = MakeAisTestSignal2GmskTxSendData(nTxChID, wTxSlotID);
        cAisModem::getInst()->SetInfinteTxMode(true);
#endif
        break;

    case AIS_TESTMODE_MSGID_00001111:
#ifndef __TESTSIGNAL_USE_PATTERN__
        // 00001111
        memset(pTxSlotBuff, 0x0F, INFINITE_TESTDATA_BYTES);
        nMsgSizeBits = MakeTestInfiniteSeqAisTxRawData(pTxSlotBuff, INFINITE_TESTDATA_BITS);
        MakeAisGmskTxSendData(nTxChID, wTxSlotID, pSlot->uMsgID);
        cAisModem::getInst()->SetInfinteTxMode(true);
#else
        /* // 0000000000000000000000000000000011111111111111111111111111111111 32bit zero 32bit one
        {
            DebugSetStepSecond(620);
            DWORD *pdwTxBuff = (DWORD*)pTxSlotBuff;
            const int BUFFSIZE_DWORD = (INFINITE_TESTDATA_BYTES >> 2);
            for(int i = 0 ; i < BUFFSIZE_DWORD ; i++)
            {
                if(i & 0x01)
                    pdwTxBuff[i] = 0x0;
                else
                    pdwTxBuff[i] = 0xFFFFFFFF;
            }
            nMsgSizeBits = MakeTestInfiniteSeqAisTxRawData(pTxSlotBuff, INFINITE_TESTDATA_BITS);
            MakeAisGmskTxSendData(nTxChID, wTxSlotID, pSlot->uMsgID);
            cAisModem::getInst()->SetInfinteTxMode(true);
            DebugSetStepSecond(621);
        }
        //*/

        nMsgSizeBits = MakeAisTestSignal3GmskTxSendData(nTxChID, wTxSlotID);
        cAisModem::getInst()->SetInfinteTxMode(true);
#endif
        break;

    case AIS_TESTMODE_MSGID_PRBS_ONE:
        nMsgSizeBits = MakeAisMsgRandomPRBS(pTxSlotBuff);
        MakeTestMsgFrameAisTxRawDataRandomPRBS(pTxSlotBuff, nMsgSizeBits);
        MakeAisGmskTxSendData(nTxChID, wTxSlotID, pSlot->uMsgID);
        break;

    case AIS_TESTMODE_MSGID_PRBS_CLUSTER:
        nMsgSizeBits = MakeAisMsgFixedPRBS(pTxSlotBuff);

        // 메모리 사이즈 줄이기 위해 송신시 체크하도록 수정
        switch(CTestModeMgr::getInst()->m_nTxSentCntFixedPRBS % 4)
        {
        case 0:
            CTestModeMgr::getInst()->m_pTxParam[0] = 1;      // Packet #
            CTestModeMgr::getInst()->m_pTxParam[1] = false;
            break;
        case 1:
            CTestModeMgr::getInst()->m_pTxParam[0] = 2;      // Packet #
            CTestModeMgr::getInst()->m_pTxParam[1] = false;
            break;
        case 2:
            CTestModeMgr::getInst()->m_pTxParam[0] = 1;      // Packet #
            CTestModeMgr::getInst()->m_pTxParam[1] = true;   // true : Invert initial NRZI state
            break;
        case 3:
            CTestModeMgr::getInst()->m_pTxParam[0] = 2;      // Packet #
            CTestModeMgr::getInst()->m_pTxParam[1] = true;   // true : Invert initial NRZI state
            break;
        }

        MakeTestMsgFrameAisTxRawDataFixedPRBS(pTxSlotBuff, nMsgSizeBits, CTestModeMgr::getInst()->m_pTxParam[0], CTestModeMgr::getInst()->m_pTxParam[1]);
        MakeAisGmskTxSendData(nTxChID, wTxSlotID, pSlot->uMsgID);
        CTestModeMgr::getInst()->m_nTxSentCntFixedPRBS++;
        break;

    case AIS_TESTMODE_MSGID_UNMOD_CARRIER:
        MakeAisUnmodCarrierGmskTxSendData(nTxChID, wTxSlotID);
        cAisModem::getInst()->SetInfinteTxMode(true);
        break;

    default:
        return false;
    }

    if(pSlot->uMsgID <= AIS_RXMSGID_MAX && nMsgSizeBits > 0)
    {
        if(nMsgSizeBits & 0x07)
        {
            WARNING_LOG("MsgSize Error! msg : %d, size : %d\r\n", pSlot->uMsgID, nMsgSizeBits);
        }

        int nBitStuffBitSize = 4;

        if(    (pSlot->uMsgID == AIS_MSG_NO_ID_06)
            || (pSlot->uMsgID == AIS_MSG_NO_ID_08)
            || (pSlot->uMsgID == AIS_MSG_NO_ID_12)
            || (pSlot->uMsgID == AIS_MSG_NO_ID_14)
            || (pSlot->uMsgID == AIS_MSG_NO_ID_24)
            || (CSetupMgr::getInst()->IsAisBClass() && pSlot->uMsgID == AIS_MSG_NO_ID_24_A))
        {
            //--------------------------------------------------
            // Refer to ITU-R.1371-5 Annex-2 5.2.1 table 21
            //--------------------------------------------------
            if(nMsgSizeBits <= 136)
                nBitStuffBitSize = 36;
            else if(nMsgSizeBits <= 360)
                nBitStuffBitSize = 68;
            else if(nMsgSizeBits <= 584)
                nBitStuffBitSize = 100;
            else if(nMsgSizeBits <= 808)
                nBitStuffBitSize = 132;
            else
                nBitStuffBitSize = 164;
        }

        nMsgRawBits = MakeRealAisTxRawData(pTxSlotBuff, nMsgSizeBits, nBitStuffBitSize);
        MakeAisGmskTxSendData(nTxChID, wTxSlotID, pSlot->uMsgID);
    }

    if(!CTestModeMgr::getInst()->IsTestModeRunning() && pSlot->uMsgID <= AIS_RXMSGID_MAX)
    {
        CMKD::getInst()->SendTxMsgToAllPI(pChannel->GetChOrdinal(), pTxSlotBuff, nMsgSizeBits / 8);
        m_dwSendSecVDO = cTimerSys::getInst()->GetCurTimerSec();
    }

    if(!IsTxMsgWaitingAck(pChannel->m_pVdlTxMgr, pSlot))
    {
        ClearReserveTx(pChannel->m_pVdlTxMgr, pSlot->uMsgID, pSlot->nDataIdx);
    }

    DEBUG_LOG("MakeMsg] after, M : %d, Fr : %d, Tx : %d, mSlot : %d, CH:%d(%d), TMO:%d,off:%d,keep:%d,%d,param:%x, %d, proc : %d, bits:%d, TT-%d:%d:%d, %d\r\n",
            pSlot->uMsgID, 
            wMapSlotID, wTxSlotID, cAisModem::getInst()->GetSlotNoCounter(), nTxChID, pChannel->m_uChNumTx,
            pSlot->nSlotTimeOut, pSlot->uSlotOffset, pSlot->bItdmaKeepFlag, pSlot->bCommStateScheme, pSlot->nDataIdx,
            pChannel->m_nLastScheduledTxSlotID, SysGetDiffTimeMili(dwStartTick), nMsgRawBits,
            cShip::getOwnShipInst()->xPosFixTime.xTime.nHour, cShip::getOwnShipInst()->xPosFixTime.xTime.nMin, cShip::getOwnShipInst()->xPosFixTime.xTime.nSec,
            cTimerSys::getInst()->GetCurTimerSec());

    if((pSlot->uMsgID == AIS_MSG_NO_ID_01 || pSlot->uMsgID == AIS_MSG_NO_ID_02) && pSlot->nSlotTimeOut <= 0)
    {
        WARNING_LOG("MakeMsg] after, Error, TMO zero & no offset, M: %d, S: %d(%d)\r\n", pSlot->uMsgID, wMapSlotID, wTxSlotID);
    }

    return true;
}

#ifdef __ENABLE_CHECK_MSG15_CNT__
/**
 * @brief Increment message 15 transmit count
 */
int CVdlTxMgr::IncMsg15TxCnt()
{
    if(m_nTxMsg15CntFrame < MAX_NUM_TX_PER_FRAME_MSG15)
    {
        m_nTxMsg15CntFrame++;
    }
}

/**
 * @brief Reset message 15 transmit count
 */
int CVdlTxMgr::ResetMsg15TxCnt()
{
    m_nTxMsg15CntFrame = 0;
}

#else
/**
 * @brief Set Tx slot ID for message 15
 * @param nMapSlotID Map slot ID
 */
void CVdlTxMgr::SetTxSlotIdMsg15(int nMapSlotID)
{
    m_pTxSlotLogMsg15[m_nTxSlotLogHeadMsg15] = nMapSlotID;
    if(++m_nTxSlotLogHeadMsg15 >= MAX_NUM_TX_PER_FRAME_MSG15)
        m_nTxSlotLogHeadMsg15 = 0;
}

/**
 * @brief Check number of Tx slots for message 15
 */
void CVdlTxMgr::CheckLogNumTxSlotMsg15()
{
    // 1분 경과된 슬롯은 SLOTID_NONE 으로 마킹한다!
    for(int i = 0 ; i < MAX_NUM_TX_PER_FRAME_MSG15 ; i++)
    {
        if(GetFrMapElapTimeSlot(m_pTxSlotLogMsg15[i], OPSTATUS::nCurFrameMapSlotID) >= NUM_SLOT_PER_FRAME)
            m_pTxSlotLogMsg15[i] = SLOTID_NONE;
    }
}
#endif

/**
 * @brief Check if Tx count is OK for message 15
 * @return true if count is OK, false otherwise
 */
bool CVdlTxMgr::CheckTxCntOkForMsg15()
{
#ifdef __ENABLE_CHECK_MSG15_CNT__
    return (m_nTxMsg15CntFrame < MAX_NUM_TX_PER_FRAME_MSG15);
#else
    CheckLogNumTxSlotMsg15();

    for(int i = 0 ; i < MAX_NUM_TX_PER_FRAME_MSG15 ; i++)
    {
        if(m_pTxSlotLogMsg15[i] == SLOTID_NONE)
            return true;
    }

    return false;
#endif

    return false;
}

/**
 * @brief Check if Tx count is OK for binary message
 * @param nSentenceType Sentence type
 * @param uNumTxSlot Number of Tx slots
 * @return true if count is OK, false otherwise
 */
bool CVdlTxMgr::CheckTxCntOkForBinMsg(BYTE nSentenceType, int uNumTxSlot)
{
    CheckLogNumTxSlotBinMsg();

    int nNumTxWithinFrame = 0;
    for(int i = 0 ; i < MAX_NUM_TX_PER_FRAME_BINMSG ; i++)
    {
        if(m_pTxSlotLogBinMsg[i] == SLOTID_NONE)
        {
            if(++nNumTxWithinFrame >= uNumTxSlot)
                return true;
        }
    }

    return false;
}


/**
 * @brief Check if Tx is available
 * @return true if available, false otherwise
 */
bool CVdlTxMgr::IsTxAvailable()
{
    //--------------------------------------------------------------------------------------------------------------------------------------------------
    // IEC-61993-2 6.10.2.2
    // Alarm ID 001 shall be activated when
    // - the integrity of the VDL would be degraded by incorrect transmitter behavior (for instance in case of the Tx shutdown procedure has operated),
    // - the unit is not able to transmit for technical reasons or missing or invalid MMSI
    //--------------------------------------------------------------------------------------------------------------------------------------------------

    return (!CLayerNetwork::getInst()->IsSilentMode() && !IsTxStatMalfunction());
}

/**
 * @brief Check if Tx is in malfunction state
 * @return true if in malfunction state, false otherwise
 */
bool CVdlTxMgr::IsTxStatMalfunction()
{
    return (!CAisLib::IsValidMMSI_MobileSt(cShip::getOwnShipInst()->xStaticData.dMMSI)
    		|| CLayerPhysical::getInst()->IsTxHwShutdownOccurred());
}