#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "AisLib.h"
#include "SyncMgr.h"
#include "ChannelMgr.h"
#include "ReportRateMgr.h"
#include "LayerPhysical.h"
#include "RosMgr.h"
#include "SlotMgr.h"
#include "VdlTxMgr.h"
#include "VdlRxMgr.h"
#include "SetupMgr.h"
#include "TestModeMgr.h"
#include "LongRange.h"
#include "MKD.h"
#include "Ship.h"
#include "Timer.h"
#include "Txt.h"
#include "SysOpStatus.h"
#include "LayerNetwork.h"

//----------------------
// Externals variables
//----------------------

//----------------------
// Constants
//----------------------

//----------------------
// Prototypes
//----------------------

//----------------------
// Global Variables
//----------------------

//----------------------
// Static Variables
//----------------------
DWORD         CLayerNetwork::m_dwPosReportLastTxSec = 0;
CChannelMgr*  CLayerNetwork::m_pLastTxCH            = NULL;
CChannelMgr*  CLayerNetwork::m_pPosReportLastTxCH   = NULL;
SYS_DATE_TIME CLayerNetwork::m_sPorReportLastTxDateTime;

CChannelMgr*  CLayerNetwork::m_pLastRoutineCH   = NULL;
CChannelMgr*  CLayerNetwork::m_pLastTxChMsg5    = NULL;
DWORD         CLayerNetwork::m_dwLastTxSecMsg5  = 0;
CChannelMgr*  CLayerNetwork::m_pLastTxChMsg27   = NULL;
DWORD         CLayerNetwork::m_dwLastTxSecMsg27 = 0;
CChannelMgr*  CLayerNetwork::m_pChgRRMasterCH   = NULL;

DWORD         CLayerNetwork::m_dwRcvSecMsg23    = 0;
DWORD         CLayerNetwork::m_dwCntTxSecMsg24  = 0;
DWORD         CLayerNetwork::m_dwLastTxSecMsg24 = 0;
CChannelMgr*  CLayerNetwork::m_pLastTxChMsg24   = NULL;

//----------------------
// Implementation
//----------------------
CLayerNetwork::CLayerNetwork()
{
    CLayerPhysical::getInst();

    m_pChPrimary   = new CChannelMgr(AIS1_DEFAULT_CH_NUM, AIS_DEFAULT_TXLR_CH_1, AIS_HW_RX_LOCAL_ID_RX1);
    m_pChSecondary = new CChannelMgr(AIS2_DEFAULT_CH_NUM, AIS_DEFAULT_TXLR_CH_2, AIS_HW_RX_LOCAL_ID_RX2);

    //Initialize();
}

CLayerNetwork::~CLayerNetwork()
{
}

void CLayerNetwork::Initialize()
{
	CSyncMgr::getInst();
    CReportRateMgr::getInst();
 
    SetOpMode(OPMODE_CONTINUOUS, ASSIGNED_MODE_NONE, TRUE);
    SetOpPhase(OPPHASE_ONBOOT_WAITSYNC);
    m_dwOpPhaseStartTick = SysGetSystemTimer();

    m_nChChgPhaseStartSlotID  = SLOTID_NONE;
    m_dwChChgPhaseStartTick   = 0;

    m_nNetworkEntryPrepSlot   = SLOTID_NONE;
    m_nNetworkEntryPrevPosMsg = AIS_MSG_NO_ID_01;
    m_pNetworkEntryCH         = NULL;

    m_pLastTxCH               = NULL;
    m_pPosReportLastTxCH      = NULL;
    m_dwPosReportLastTxSec    = 0;
    CAisLib::SetDefaultSysDateTime(&m_sPorReportLastTxDateTime);

    m_pLastRoutineCH          = NULL;
    m_pChgRRMasterCH          = NULL;
    m_pLastTxChMsg5           = NULL;
    m_dwLastTxSecMsg5         = 0;
    m_pLastTxChMsg27          = NULL;
    m_dwLastTxSecMsg27        = 0;

#ifdef __IEC_RULE_2018_ED3_
    m_dwLCntTxSecMsg24 = 0;
    m_dwLastTxSecMsg24 = 0;
    m_pLastTxChMsg24        = NULL;
#endif

    m_uSlotAssignedModeBaseStMMSI    = AIS_AB_MMSI_NULL;

    m_nAssignedRRStartSlot = SLOTID_NONE;

    m_bNetEntryFromExisting = FALSE;

    m_bEnableRx = TRUE;

    m_bSlotTimeResynced = FALSE;

    CLayerPhysical::getInst()->InitHW();
    // ITU-R 1371-5 Annex 2 4.3 부팅 후 처음 First frame 동안 수행될 보고주기 10초
    CReportRateMgr::getInst()->RecalcReportInterval(10.0f);
}

void CLayerNetwork::EnableRx(BOOL bEnable)
{
    m_bEnableRx = bEnable;
}

void CLayerNetwork::ChangeMMSI(UINT uNewMMSI)
{
    m_pChPrimary->PrepToChangeFrMMSI(uNewMMSI);
    m_pChSecondary->PrepToChangeFrMMSI(uNewMMSI);

    cShip::getOwnShipInst()->xStaticData.dMMSI = uNewMMSI;
}

void CLayerNetwork::SetOpMode(BYTE bOpMode, BYTE bAssignedModeBy, BOOL bSetAllCh)
{
    if (m_nOpMode == bOpMode && m_bAssignedModeBy == bAssignedModeBy &&
        (!bSetAllCh || (m_nOpMode == m_pChPrimary->m_nChOpMode 
                        && m_nOpMode == m_pChSecondary->m_nChOpMode
                        && CROSMgr::getInst()->IsSameChSetupData(&CROSMgr::getInst()->m_sChSetup, &CROSMgr::getInst()->m_sRosData.sChSetup, TRUE, FALSE, FALSE))))
    {
        return;
    }

    // IEC 61993-2:2018 ******** Monitoring sensor data status
    if ((m_nOpMode != bOpMode) 
        || (m_bAssignedModeBy != bAssignedModeBy) 
        || !CROSMgr::getInst()->IsSameChSetupData(&CROSMgr::getInst()->m_sChSetup, &CROSMgr::getInst()->m_sRosData.sChSetup, TRUE, FALSE, FALSE))
    {
        switch (bAssignedModeBy)
        {
            case ASSIGNED_MODE_NONE:
                // ROS영역내에 진입하더라도 기본 동작모드와 동일한 세팅이면 TXT44 메시지 출력
                if (   CROSMgr::getInst()->m_sRosData.nRosMode == ROS_MODE_HIGH_SEA_ZONE 
                    || (CROSMgr::getInst()->m_sRosData.sChSetup.nTxRxMode == TRXMODE_TXARXA_TXBRXB 
                        && CROSMgr::getInst()->m_sRosData.sChSetup.uChannelIdA == AIS1_DEFAULT_CH_NUM
                        && CROSMgr::getInst()->m_sRosData.sChSetup.uChannelIdB == AIS2_DEFAULT_CH_NUM)
                )
                {
                    CMKD::getInst()->SendTXTtoPI(TXT_ID_RTN_DEFAULT_MODE, true);
                }
                break;
            case ASSIGNED_MODE_BY_MSG16:
                CMKD::getInst()->SendTXTtoPI(TXT_ID_ASSIGNED_MODE, true);
                break;
            case ASSIGNED_MODE_BY_MSG23:
                CMKD::getInst()->SendTXTtoPI(TXT_ID_GRP_ASSGNED_MODE, true);
                break;
        }
    }

    m_nOpMode = bOpMode;
    m_bAssignedModeBy = bAssignedModeBy;

    if (bSetAllCh)
    {
        m_pChPrimary->SetChOpMode(m_nOpMode);
        m_pChSecondary->SetChOpMode(m_nOpMode);
    }

    if (m_nOpMode == OPMODE_ASSIGNED_RR)
    {
        CChTxScheduler::SetAssignedRRTimeOut();
    }
}

void CLayerNetwork::SetOpPhase(BYTE bOpPhase, BOOL bSetAllCh)
{
    m_nOpPhase = bOpPhase;
    m_dwOpPhaseStartTick = SysGetSystemTimer();

    if (bOpPhase == OPPHASE_ROUTINE)
    {
        m_pLastRoutineCH = NULL;
        m_nAssignedRRStartSlot = OPSTATUS::nCurFrameMapSlotID;
        OPSTATUS::bFirstFrameOnBootDone = TRUE;
    }

    if (bSetAllCh)
    {
        m_pChPrimary->SetChOpPhase(m_nOpPhase);
        m_pChSecondary->SetChOpPhase(m_nOpPhase);
    }
}

BOOL CLayerNetwork::CheckBothChSetOpPhase(BYTE bOpPhase)
{
    if ((!m_pChPrimary->IsTxAvailableCh() || m_pChPrimary->m_nChOpPhase == bOpPhase) &&
        (!m_pChSecondary->IsTxAvailableCh() || m_pChSecondary->m_nChOpPhase == bOpPhase))
    {
        SetOpPhase(bOpPhase);
        return TRUE;
    }
    return FALSE;
}

CChannelMgr* CLayerNetwork::GetChPrimary()
{
    return m_pChPrimary;
}

CChannelMgr* CLayerNetwork::GetChSecondary()
{
    return m_pChSecondary;
}

CChannelMgr* CLayerNetwork::GetChPtr(UINT8 uChID)
{
    if (uChID == AIS_CHANNEL_AIS1)
        return m_pChPrimary;
    if (uChID == AIS_CHANNEL_AIS2)
        return m_pChSecondary;

    return m_pChPrimary;
}

CChannelMgr* CLayerNetwork::GetOppositeChPtr(CChannelMgr *pCh)
{
    if (pCh == m_pChPrimary)
        return m_pChSecondary;
    if (pCh == m_pChSecondary)
        return m_pChPrimary;

    return m_pChPrimary;
}

CChannelMgr* CLayerNetwork::GetOppositeTxChPtr(CChannelMgr *pCh)
{
    if (m_pChPrimary->IsTxAvailableCh() && m_pChSecondary->IsTxAvailableCh())
    {
        if (pCh && pCh == m_pChPrimary)
            return m_pChSecondary;
        return m_pChPrimary;
    }
    if (m_pChPrimary->IsTxAvailableCh())
        return m_pChPrimary;
    if (m_pChSecondary->IsTxAvailableCh())
        return m_pChSecondary;
    return NULL;
}

void CLayerNetwork::SetLastScheduledPosReportTime(CChannelMgr *pChannel)
{
    m_pPosReportLastTxCH         = pChannel;
    m_dwPosReportLastTxSec       = cTimerSys::getInst()->GetCurTimerSec();
    m_sPorReportLastTxDateTime   = cShip::getOwnShipInst()->xSysTime;
}

BOOL CLayerNetwork::IsMasterChChgRR(CChannelMgr *pChannel)
{
    return (m_pChgRRMasterCH == pChannel);
}

BOOL CLayerNetwork::CheckAssignedModeRunning(void)
{
    return (m_nOpMode == OPMODE_ASSIGNED_RR || m_nOpMode == OPMODE_ASSIGNED_SLOT);
}

BOOL CLayerNetwork::CheckAssignedModeRunningByMsg16(void)
{
    return (CheckAssignedModeRunning() && m_bAssignedModeBy == ASSIGNED_MODE_BY_MSG16);
}

BOOL CLayerNetwork::CheckAssignedModeRunningByMsg23(void)
{
    return (CheckAssignedModeRunning() && m_bAssignedModeBy == ASSIGNED_MODE_BY_MSG23);
}

INT8 CLayerNetwork::GetNewFrameTimeOut(void)
{
    static BYTE bOldOpMode = OPMODE_NONE;
    static BYTE bOldOpPhase = OPPHASE_MONITOR_VDL;
    static DWORD dwNewTimeOutTick = 0;

    // 이 함수는 채널 별로 각각 중복되어 실행되면 안되고 한번만 실행되어야 한다!
    // 채널1 에서 이 함수를 호출한 이후 1분 내에 채널2 에서 이 함수를 부를 경우 무시한다!
    if (bOldOpMode == m_nOpMode && bOldOpPhase == m_nOpPhase && SysGetDiffTimeScnd(dwNewTimeOutTick) < 60)
    {
        WARNING_LOG("NewTMO] ignore, mode:%d,%d, ph:%d,%d, elap:%d\r\n",
                bOldOpMode, m_nOpMode, bOldOpPhase, m_nOpPhase, SysGetDiffTimeScnd(dwNewTimeOutTick));
        return m_nNewFrameTimeOut;
    }

    int nRandValue = CAisLib::GetRandomSlotTimeOut();

    m_nNewFrameTimeOut = nRandValue;

    dwNewTimeOutTick = SysGetSystemTimer();
    bOldOpMode = m_nOpMode;
    bOldOpPhase = m_nOpPhase;

    DEBUG_LOG("NewTMO] Set, tmo:%d\r\n", m_nNewFrameTimeOut);

    return m_nNewFrameTimeOut;
}

BOOL CLayerNetwork::IsRandomTimeoutRequired()
{
    return (CLayerNetwork::getInst()->m_nOpMode != OPMODE_ASSIGNED_SLOT);
}

BOOL CLayerNetwork::IsPhaseTxAvailable()
{
    return (m_nOpPhase != OPPHASE_ONBOOT_WAITSYNC && m_nOpPhase != OPPHASE_MONITOR_VDL && m_nOpPhase != OPPHASE_NETWORKENTRY);
}

BOOL CLayerNetwork::IsAssignedModeOpAvailable()
{
    return (m_nOpPhase != OPPHASE_CH_CHG_PHASE);
}

BOOL CLayerNetwork::IsRosChgOpAvailable()
{
    return TRUE;
}

BOOL CLayerNetwork::IsChangingReportRateAvailable()
{
    return TRUE;
}

BOOL CLayerNetwork::IsHigherOrEqualAssignedRR(CChannelMgr *pCh, float fAssignedRIfor2Ch)
{
    //---------------------------------------------------------------------------------------
    // fAssignedModeRI : 두 채널을 모두 고려한 RI 값 입력할것
    //---------------------------------------------------------------------------------------
    // IEC-61993-2 7.3.4.2
    // An assignment command received by Message 16 or Message 23, with a reporting interval
    // shorter than or equal to the autonomous reporting interval shall be processed and the
    // reporting interval defined by the assignment command be used.
    //---------------------------------------------------------------------------------------
    if (fAssignedRIfor2Ch <= 0)
        return FALSE;

    float fAutoModeReportInterval = CReportRateMgr::getInst()->m_fReportIntervalSec;

    if (CReportRateMgr::getInst()->m_bTempUseReportByITDMA)                            // when temporary ITDMA position report is being performed
        fAutoModeReportInterval /= 3;

    return (fAssignedRIfor2Ch <= fAutoModeReportInterval);
}

BOOL CLayerNetwork::IsSilentMode()
{
    return CSetupMgr::getInst()->GetEnableSilentMode();
}

void CLayerNetwork::InitStaticReportSec()
{
    const int TOTAL_RI    = STATIC_REPORT_INTSEC;
    CChannelMgr *pNextTxCh;
    CChannelMgr *pLastTxCh = m_pLastTxChMsg5;

    DWORD dwNextSec = m_dwLastTxSecMsg5 + TOTAL_RI;
    if (cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg5) > TOTAL_RI)
        dwNextSec = cTimerSys::getInst()->GetCurTimerSec() + 1;

    if (pLastTxCh)
    {
        pNextTxCh = GetOppositeTxChPtr(pLastTxCh);
    }
    else
    {
        if (m_pChPrimary->m_dwStaticReportIntervalSec)
            pLastTxCh = m_pChSecondary;
        else
            pLastTxCh = m_pChPrimary;
        pNextTxCh = GetOppositeTxChPtr(pLastTxCh);
    }

    if (!pNextTxCh || pNextTxCh == pLastTxCh)
    {
        pNextTxCh = pLastTxCh;
        if (pLastTxCh == m_pChPrimary)
            m_pChSecondary->InitStaticReportSecCh(0);
        else
            m_pChPrimary->InitStaticReportSecCh(0);
        pLastTxCh = NULL;
    }

    if (pNextTxCh && pLastTxCh && pNextTxCh != pLastTxCh)
    {
        // 2 channel
        pNextTxCh->InitStaticReportSecCh(dwNextSec);
        pLastTxCh->InitStaticReportSecCh(dwNextSec + TOTAL_RI);
    }
    else
    {
        // 1 channel
        CChannelMgr *pTxCh = pLastTxCh;
        if (!pTxCh)
            pTxCh = pNextTxCh;
        if (pTxCh)
            pTxCh->InitStaticReportSecCh(dwNextSec);
    }

    INFO_LOG("[InitStaticSec] lastCH: %d,lastSec:%d, nextSec:%d, priCH:%d(RI:%d), secCH:%d(RI:%d), lastCH: %d,sec:%d, nextCH: %d,sec:%d s:%d\r\n",
            m_pLastTxChMsg5 ? m_pLastTxChMsg5->GetChOrdinal() : -1, m_dwLastTxSecMsg5, dwNextSec,
            m_pChPrimary->m_dwStaticReportNextSec, m_pChPrimary->m_dwStaticReportIntervalSec,
            m_pChSecondary->m_dwStaticReportNextSec, m_pChSecondary->m_dwStaticReportIntervalSec,
            pLastTxCh ? pLastTxCh->GetChOrdinal() : -1, pLastTxCh ? pLastTxCh->m_dwStaticReportNextSec : -1,
            pNextTxCh ? pNextTxCh->GetChOrdinal() : -1, pNextTxCh ? pNextTxCh->m_dwStaticReportNextSec : -1,
            cTimerSys::getInst()->GetCurTimerSec());
}

void CLayerNetwork::InitLongRangeReportSec()
{
    const int TOTAL_RI = LR_REPORT_INTERVAL_SEC;
    CChannelMgr *pLastTxCh = m_pLastTxChMsg27;
    CChannelMgr *pNextTxCh;
    DWORD dwNextSec = m_dwLastTxSecMsg27 + TOTAL_RI;
    if (cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg27) > TOTAL_RI)
        dwNextSec = cTimerSys::getInst()->GetCurTimerSec() + 1;//TOTAL_RI;

    if (CLongRange::getInst()->IsLRTxAvail())
    {
        if (pLastTxCh)
        {
            pNextTxCh = GetOppositeChPtr(pLastTxCh);
        }
        else
        {
            if (m_pChPrimary->m_dwLongRangeReportIntervalSec)
                pLastTxCh = m_pChSecondary;
            else
                pLastTxCh = m_pChPrimary;
            pNextTxCh = GetOppositeChPtr(pLastTxCh);
        }

        if (!pNextTxCh || pNextTxCh == pLastTxCh)
        {
            pNextTxCh = pLastTxCh;
            if (pLastTxCh == m_pChPrimary)
                m_pChSecondary->InitLongRangeReportSecCh(0);
            else
                m_pChPrimary->InitLongRangeReportSecCh(0);
            pLastTxCh = NULL;
        }

        if (pNextTxCh && pLastTxCh && pNextTxCh != pLastTxCh)
        {
            // 2 channel
            pNextTxCh->InitLongRangeReportSecCh(dwNextSec);
            pLastTxCh->InitLongRangeReportSecCh(dwNextSec + TOTAL_RI);
        }
        else
        {
            // 1 channel
            CChannelMgr *pTxCh = pLastTxCh;
            if (!pTxCh)
                pTxCh = pNextTxCh;
            if (pTxCh)
                pTxCh->InitLongRangeReportSecCh(dwNextSec);
        }

        DEBUG_LOG("[LR-VDL] InitSec] Enable, CH-0:%d(RI:%d), CH-1:%d(RI:%d), lastCH: %d, nextCH: %d, s:%d\r\n",
            m_pChPrimary->m_dwLongRangeTxNextSec, m_pChPrimary->m_dwLongRangeReportIntervalSec,
            m_pChSecondary->m_dwLongRangeTxNextSec, m_pChSecondary->m_dwLongRangeReportIntervalSec,
            pLastTxCh ? pLastTxCh->GetChOrdinal() : -1, pNextTxCh ? pNextTxCh->GetChOrdinal() : -1, cTimerSys::getInst()->GetCurTimerSec());
    }
    else
    {
        m_pChPrimary->InitLongRangeReportSecCh(0);
        m_pChSecondary->InitLongRangeReportSecCh(0);

        DEBUG_LOG("[LR-VDL] InitSec] Disable, EnableMsg27: %d, LongRangeTxCtrl: %d, s:%d\r\n",
                CSetupMgr::getInst()->GetLongRangeTxEnableMsg27(), CLongRange::getInst()->m_nLongRangeTxCtrl,
                cTimerSys::getInst()->GetCurTimerSec());
    }
}

BOOL CLayerNetwork::RunResync(int nShiftSlot)
{
    m_pChPrimary->ResyncChannel(nShiftSlot);
    m_pChSecondary->ResyncChannel(nShiftSlot);

    if (m_nChChgPhaseStartSlotID != SLOTID_NONE)
    {
        m_nChChgPhaseStartSlotID = CAisLib::FrameMapSlotIdAdd(m_nChChgPhaseStartSlotID, nShiftSlot);
    }

    return TRUE;
}

BOOL CLayerNetwork::RunPhaseNetworkEntry()
{
    //-------------------------------------------------------------------------------
    // 채널1, 2에 대해 송신을 위한 최초 슬롯선택, 최초 송신 특별위치보고를 위한 준비
    //
    // m_pNetworkEntryCH : channel where it should start network entry
    // m_nNetworkEntryPrevPosMsg :
    // m_nNetworkEntryPrepSlot :
    //
    // output TRUE : means it should progress the next step
    //-------------------------------------------------------------------------------
    if (!CVdlTxMgr::IsTxAvailable())
    {
        WARNING_LOG("RunPhaseNetworkEntry] Error, TX Invalid, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        return FALSE;
    }

    if (CReportRateMgr::getInst()->m_fReportRate <= 0)
    {
        WARNING_LOG("RunPhaseNetworkEntry] Error, Invalid RR %.1f\r\n", CReportRateMgr::getInst()->m_fReportRate);
        return FALSE;
    }

    const INT8 nNewPosMsgID = CAisLib::GetScheduledPosReportMsgID(m_nOpMode);
    const BOOL bRunChgOpMode = m_nNetworkEntryPrevPosMsg != nNewPosMsgID;
    const int nTotalNI = CAisLib::GetNIfromRR(CReportRateMgr::getInst()->m_fReportRate);

    INT16 nEntryStartSI = SLOTID_NONE;
    int   nRandNum = 0;

    CChannelMgr *pMasterCH = m_pNetworkEntryCH;
    if (!pMasterCH)
        pMasterCH = GetNextPosTxCH();
    CChannelMgr *pSlaveCH = GetOppositeChPtr(pMasterCH);

    if (!pMasterCH)
    {
        WARNING_LOG("RunPhaseNetworkEntry] Error, nextTxCH NULL %x\r\n", m_pLastRoutineCH);
        return FALSE;
    }

    m_bNetEntryFromExisting = FALSE;

    if (m_nNetworkEntryPrepSlot != SLOTID_NONE)
    {
        if (CReportRateMgr::getInst()->IsReportRateForSOTDMA() && CAisLib::FrameMapGetDiffSlotID(OPSTATUS::nCurFrameMapSlotID, m_nNetworkEntryPrepSlot) > NUM_SLOT_PER_FRAME)
        {
            m_nNetworkEntryPrepSlot = SLOTID_NONE;
        }

        if (m_nNetworkEntryPrepSlot != SLOTID_NONE)
        {
            m_bNetEntryFromExisting = TRUE;
        }
    }

    if (m_nNetworkEntryPrepSlot == SLOTID_NONE)
    {
        if (m_pChPrimary->IsTxAvailableCh())
            m_pChPrimary->SetLastTxFrameAndFree(0);
        if (m_pChSecondary->IsTxAvailableCh())
            m_pChSecondary->SetLastTxFrameAndFree(0);

        nEntryStartSI = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, nTotalNI);

        DEBUG_LOG("RunPhaseNetworkEntry] begin-NewEntry, %d, TRX: %d, entryCH:%d, entrySlot:%d, MasterCH : %d(%d), SlaveCH : %d(%d), prevPosMsg : %d, C : %d, RR : %.2f, chRR : %.2f(%.2f), nTotalNI : %d, startSI : %d, nTMO : %d, nRand : %d\r\n",
                bRunChgOpMode, CROSMgr::getInst()->m_sChSetup.nTxRxMode, m_pNetworkEntryCH ? m_pNetworkEntryCH->GetChOrdinal() : -1, m_nNetworkEntryPrepSlot,
                pMasterCH ? pMasterCH->GetChOrdinal() : -1, pMasterCH ? pMasterCH->IsTxAvailableCh() : 0, pSlaveCH ? pSlaveCH->GetChOrdinal() : -1, pSlaveCH ? pSlaveCH->IsTxAvailableCh() : 0,
                m_nNetworkEntryPrevPosMsg, OPSTATUS::nCurFrameMapSlotID, CReportRateMgr::getInst()->m_fReportRate, m_pChPrimary->m_fChReportRate, 
                m_pChSecondary->m_fChReportRate, nTotalNI, nEntryStartSI, m_nNewFrameTimeOut, nRandNum);
    }

    GetNewFrameTimeOut();

    const int MSGID_NETENTRY = AIS_MSG_NO_ID_03;

    BOOL bRet = FALSE;
    int nHalfTotalNI = nTotalNI >> 1;
    WORD wExistingSlot;
    int nPrimaryNSS;

    if (CROSMgr::getInst()->m_sChSetup.nTxRxMode == TRXMODE_TXARXA_TXBRXB)
    {
        //-----------------------
        // Two channel TRX mode
        //-----------------------

        if (m_pChPrimary->m_fChReportRate <= 0 || m_pChSecondary->m_fChReportRate <= 0)
        {
            WARNING_LOG("RunPhaseNetworkEntry] Error, two CH mode, Invalid CH RR %.1f, %.1f, s:%d\r\n",
                m_pChPrimary->m_fChReportRate, m_pChSecondary->m_fChReportRate, cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        if (CReportRateMgr::getInst()->IsReportRateForSOTDMA())
        {
            //-----------------------
            // SOTDMA
            //-----------------------
            BOOL bRetMasterCH= FALSE;
            BOOL bRetSlaveCH = FALSE;

            int nSizeSI = pMasterCH->m_nSizeSI;
            const int nSizeEntrySI = nSizeSI;

            int nEntryStartSI2 = nEntryStartSI;
            int nNetEntrySlot1 = m_nNetworkEntryPrepSlot;
            int nNetEntrySlot2 = m_nNetworkEntryPrepSlot;

            int nPrimaryNSS= SLOTID_NONE;
            int nSecondNSS = SLOTID_NONE;
            int nSizeSI2 = nSizeSI;

            if (nNetEntrySlot1 == SLOTID_NONE)
                nNetEntrySlot1 = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(pMasterCH, MSGID_NETENTRY, nEntryStartSI, nSizeEntrySI, 1);

            if (nNetEntrySlot1 == SLOTID_NONE)
            {
                DEBUG_LOG("RunPhaseNetworkEntry] SOTDMA-2CH entry, CH1 FAIL! masterCH:%d, SI: %d, sizeSI: %d\r\n",
                        pMasterCH ? pMasterCH->GetChOrdinal() : -1, pSlaveCH ? pSlaveCH->GetChOrdinal() : -1, nEntryStartSI, nSizeEntrySI);

                nNetEntrySlot1  = (m_nNetworkEntryPrepSlot == SLOTID_NONE ? nEntryStartSI : m_nNetworkEntryPrepSlot);
                nRandNum        = CAisLib::GetRandomValueOneBySEED(0, nHalfTotalNI);
                int nPrimaryNSS = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nHalfTotalNI+nRandNum);
                pMasterCH->SetNSS(nPrimaryNSS);
                pMasterCH->ResetNextStartSI();
                pMasterCH->DecNextStartSI();
            }
            else
            {
                pMasterCH->SetFrameMapOneMsg(TRUE, nNetEntrySlot1, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC,
                                                            (IsRandomTimeoutRequired() ? 0 : m_nNewFrameTimeOut),
                                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, MSGID_NETENTRY, 0, TRUE, FALSE, 0, FALSE);                        // 일단 offset 0 로 예약해둔다! (다른 메시지 송신에서 할당하지 않도록 하기 위해)

                if (nPrimaryNSS == SLOTID_NONE)
                {
                    nPrimaryNSS = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nTotalNI);
                }

                bRetMasterCH = pMasterCH->ProcessPhaseNetworkEntry(nNetEntrySlot1, nPrimaryNSS);

                nEntryStartSI2 = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nTotalNI);
                nSizeSI2 = CAisLib::FrameMapGetDiffSlotID(nEntryStartSI2, nPrimaryNSS);
                nSecondNSS = CAisLib::FrameMapSlotIdAdd(nPrimaryNSS, nTotalNI);
            }


            WORD wEntrySlot2 = SLOTID_NONE;
            pSlaveCH->GetInternalAllocatedSlot_SO(nEntryStartSI2, nSizeSI2, &wEntrySlot2);

            nNetEntrySlot2 = (int)wEntrySlot2;
            if (nNetEntrySlot2 == SLOTID_NONE)
            {
                nNetEntrySlot2 = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(pSlaveCH, MSGID_NETENTRY, nEntryStartSI2, nHalfTotalNI, 1);

                pSlaveCH->SetFrameMapOneMsg(TRUE, nNetEntrySlot2, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC,
                                                            (IsRandomTimeoutRequired() ? 0 : m_nNewFrameTimeOut),
                                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, MSGID_NETENTRY, 0, TRUE, FALSE, 0, FALSE);                        // 일단 offset 0 로 예약해둔다! (다른 메시지 송신에서 할당하지 않도록 하기 위해)
            }

            if (nNetEntrySlot2 == SLOTID_NONE)
            {
                int nSecondNSS = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nHalfTotalNI);
                pSlaveCH->SetNSS(nSecondNSS);
                pSlaveCH->ResetNextStartSI();
                pSlaveCH->DecNextStartSI();
            }
            else
            {
                if (nSecondNSS == SLOTID_NONE)
                {
                    nRandNum    = CAisLib::GetRandomValueOneBySEED(0, nTotalNI);
                    nSecondNSS    = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot2, nTotalNI+nRandNum);
                }
                bRetSlaveCH = pSlaveCH->ProcessPhaseNetworkEntry(nNetEntrySlot2, nSecondNSS);
            }

            DEBUG_LOG("RunPhaseNetworkEntry] SOTDMA-2CH entry, CH1ok: %d, CH2ok:%d, entryCH:%d, entrySlot:%d, MasterCH : %d, SlaveCH : %d, masterEntry : %d(%d), slaveEntry : %d(%d), masterNSS : %d(%d),SI:%d, slaveNSS : %d(%d),SI:%d, tmo : %d\r\n",
                bRetMasterCH, bRetSlaveCH,
                m_pNetworkEntryCH ? m_pNetworkEntryCH->GetChOrdinal() : -1,
                m_nNetworkEntryPrepSlot, 
                pMasterCH ? pMasterCH->GetChOrdinal() : -1, pSlaveCH ? pSlaveCH->GetChOrdinal() : -1,
                nNetEntrySlot1, pMasterCH->GetSlotIdFromFrameSlotID(nNetEntrySlot1),
                nNetEntrySlot2, pSlaveCH->GetSlotIdFromFrameSlotID(nNetEntrySlot2),
                nPrimaryNSS, pMasterCH->GetSlotIdFromFrameSlotID(nPrimaryNSS), pMasterCH->m_nNextStartSI,
                nSecondNSS, pMasterCH->GetSlotIdFromFrameSlotID(nSecondNSS), pMasterCH->m_nNextStartSI,
                m_nNewFrameTimeOut);

            bRet = (bRetMasterCH || bRetSlaveCH);

            if (bRet)
            {
                SetLastScheduledPosReportTime(pMasterCH);
            }
        }
        else
        {
            //-----------------------
            // ITDMA
            //-----------------------
            // Network Entry에서 SOTDMA가 아닌경우(NI > 30초) 기존에는 NI+HalfNI+(Random(HalfNI))로 계산하게 되어
            // 3분 송신의 경우 첫 송신에 5분이상 소요되게 된다.
            // Network Entry에서 30초를 초과하는 송신 인터벌의 경우 현재 슬롯에서 NI값만 더한 슬롯을 NSS로 지정하도록 하여
            // 첫 송신 타임을 줄이도록 수정함.
            
        #if 0
            int nNetEntrySlot1 = (m_nNetworkEntryPrepSlot == SLOTID_NONE ? nEntryStartSI : m_nNetworkEntryPrepSlot);
            nRandNum        = GetRandomValueOneBySEED(0, nHalfTotalNI);
            int nPrimaryNSS = FrameMapSlotIdAdd(nNetEntrySlot1, nHalfTotalNI+nRandNum);
            pMasterCH->SetNSS(nPrimaryNSS);
            pMasterCH->ResetNextStartSI();
            pMasterCH->DecNextStartSI();
        #else
            int nPrimaryNSS = nEntryStartSI;
            pMasterCH->SetNSS(nPrimaryNSS);
            pMasterCH->ResetNextStartSI();
            pMasterCH->DecNextStartSI();
        #endif

            pSlaveCH->SetNSS(CAisLib::FrameMapSlotIdAdd(nPrimaryNSS, nTotalNI));
            pSlaveCH->ResetNextStartSI();
            pSlaveCH->DecNextStartSI();
            bRet = TRUE;

            DEBUG_LOG("ChNetworkEntry] ITDMA-2CH, MasterCH : %d, SlaveCH : %d, C : %d, PrimaryNSS: %d, SecondaryNSS : %d\r\n",
                    pMasterCH ? pMasterCH->GetChOrdinal() : -1, pSlaveCH ? pSlaveCH->GetChOrdinal() : -1,
                    OPSTATUS::nCurFrameMapSlotID, pMasterCH->m_nNSS, pSlaveCH->m_nNSS);
        }
    }
    else
    {
        //-----------------------
        // one channel TRX mode
        //-----------------------
        pMasterCH = m_pChPrimary;
        if (CROSMgr::getInst()->m_sChSetup.nTxRxMode == TRXMODE____RXA_TXBRXB)
            pMasterCH = m_pChSecondary;

        if (pMasterCH->m_fChReportRate <= 0)
        {
            WARNING_LOG("RunPhaseNetworkEntry] Error, one CH mode, Invalid CH-%d RR %.1f, s:%d\r\n",
                    pMasterCH->GetChOrdinal(), pMasterCH->m_fChReportRate, cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        if (CReportRateMgr::getInst()->IsReportRateForSOTDMA())
        {
            //-----------------------
            // SOTDMA
            //-----------------------

            const int nSizeEntrySI = pMasterCH->m_nSizeSI;

            int nNetEntrySlot1 = m_nNetworkEntryPrepSlot;
            if (nNetEntrySlot1 == SLOTID_NONE)
            {
                nNetEntrySlot1 = CSlotMgr::getInst()->GetTxTdmaSlotAlloc_RA(pMasterCH, MSGID_NETENTRY, nEntryStartSI, nSizeEntrySI, 1);

                pMasterCH->SetFrameMapOneMsg(TRUE, nNetEntrySlot1, 1, cShip::getOwnShipInst()->xStaticData.dMMSI, SLOTSTAT_INT_ALLOC,
                                                            (IsRandomTimeoutRequired() ? 0 : m_nNewFrameTimeOut),
                                                            POS_REPORT_SCHEDULED, TDMA_SOTDMA, MSGID_NETENTRY, 0, TRUE, FALSE, 0, FALSE);                                // 일단 offset 0 로 예약해둔다! (다른 메시지 송신에서 할당하지 않도록 하기 위해)
            }

            if (nNetEntrySlot1 != SLOTID_NONE)
            {
                int nSizeSI = pMasterCH->m_nSizeSI;
                int nSizeHalfSI = pMasterCH->m_nSizeHalfSI;
                int nTotalNI = (pMasterCH->m_nNI >> 1);

                nPrimaryNSS = SLOTID_NONE;

                if (m_nNetworkEntryPrepSlot != SLOTID_NONE)
                {
                    nPrimaryNSS = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nTotalNI);
                    int nNssSI = CAisLib::FrameMapSlotIdAdd(nPrimaryNSS, -nSizeHalfSI);
                    wExistingSlot = SLOTID_NONE;

                    pMasterCH->GetInternalAllocatedSlot_SO(nNssSI, nSizeSI, &wExistingSlot);

                    if (wExistingSlot == SLOTID_NONE)
                    {
                        pMasterCH->GetInternalAllocatedSlot_SO(CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, 2), nTotalNI, &wExistingSlot);
                        if (wExistingSlot == SLOTID_NONE)
                            nPrimaryNSS = SLOTID_NONE;
                        else
                            nPrimaryNSS = wExistingSlot;
                    }
                }
                if (nPrimaryNSS == SLOTID_NONE)
                {
                    nRandNum    = CAisLib::GetRandomValueOneBySEED(0, nTotalNI);
                    nPrimaryNSS = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nTotalNI+nRandNum);
                }

                DEBUG_LOG("RunPhaseNetworkEntry] SOTDMA-1CH entry, entryCH:%d, entrySlot:%d, MasterCH : %d, masterEntry : %d(%d), masterNSS : %d(%d), tmo : %d\r\n",
                    m_pNetworkEntryCH ? m_pNetworkEntryCH->GetChOrdinal() : -1,
                    m_nNetworkEntryPrepSlot, 
                    pMasterCH ? pMasterCH->GetChOrdinal() : -1,
                    nNetEntrySlot1, pMasterCH->GetSlotIdFromFrameSlotID(nNetEntrySlot1),
                    nPrimaryNSS, pMasterCH->GetSlotIdFromFrameSlotID(nPrimaryNSS),
                    m_nNewFrameTimeOut);

                bRet = pMasterCH->ProcessPhaseNetworkEntry(nNetEntrySlot1, nPrimaryNSS);

                if (bRet)
                {
                    SetLastScheduledPosReportTime(pMasterCH);
                }
            }
        }
        else
        {
            //-----------------------
            // ITDMA
            //-----------------------

            int nNetEntrySlot1 = (m_nNetworkEntryPrepSlot == SLOTID_NONE ? nEntryStartSI : m_nNetworkEntryPrepSlot);
            nRandNum        = CAisLib::GetRandomValueOneBySEED(0, nHalfTotalNI);
            int nPrimaryNSS = CAisLib::FrameMapSlotIdAdd(nNetEntrySlot1, nHalfTotalNI+nRandNum);
            pMasterCH->SetNSS(nPrimaryNSS);
            pMasterCH->ResetNextStartSI();
            pMasterCH->DecNextStartSI();
            bRet = TRUE;

            DEBUG_LOG("ChNetworkEntry] ITDMA-1CH, MasterCH : %d, SlaveCH : %d, CH : %d, C : %d, PrimaryNSS: %d\r\n",
                    pMasterCH ? pMasterCH->GetChOrdinal() : -1, pSlaveCH ? pSlaveCH->GetChOrdinal() : -1,
                    pMasterCH->GetChOrdinal(), OPSTATUS::nCurFrameMapSlotID, pMasterCH->m_nNSS);
        }
    }

    if (!bRet)
    {
        WARNING_LOG("ChNetworkEntry] FAIL! s : %d\r\n", cTimerSys::getInst()->GetCurTimerSec());
    }

    return bRet;
}

CChannelMgr* CLayerNetwork::GetNextPosTxCH()
{
    if (m_pChPrimary->IsTxAvailableCh() && m_pChSecondary->IsTxAvailableCh())
    {
        if (m_pLastRoutineCH == m_pChPrimary)
            return m_pChSecondary;
        return m_pChPrimary;
    }
    else if (m_pChPrimary->IsTxAvailableCh())
        return m_pChPrimary;
    else if (m_pChSecondary->IsTxAvailableCh())
        return m_pChSecondary;
    return m_pChPrimary;
}

CChannelMgr* CLayerNetwork::GetNextPosTxCH(int nNewTxCh1, int nNewTxCh2)
{
    if (nNewTxCh1 != AIS_CH_NUM_NONE && nNewTxCh2 != AIS_CH_NUM_NONE)
    {
        if (m_pLastRoutineCH == m_pChPrimary)
            return m_pChSecondary;
        return m_pChPrimary;
    }
    else if (nNewTxCh1 != AIS_CH_NUM_NONE)
        return m_pChPrimary;
    else if (nNewTxCh2 != AIS_CH_NUM_NONE)
        return m_pChSecondary;
    return m_pChPrimary;
}

int CLayerNetwork::GetNewReportRateStartNS()
{
    CChannelMgr *pCH = GetNextPosTxCH();
    if (pCH)
        return pCH->m_nNS;
    return SLOTID_NONE;
}

void CLayerNetwork::PrepFrameMapToChChgNet(BOOL bPrepToChChg, UINT uNewChIdA, UINT uNewChIdB, int nChChgPhaseStartSlotID)
{
    int nStartClearSlotID = nChChgPhaseStartSlotID;
    if (nStartClearSlotID == SLOTID_NONE)
        nStartClearSlotID = OPSTATUS::nCurFrameMapSlotID;
    nStartClearSlotID = CAisLib::FrameMapSlotIdAdd(nStartClearSlotID, TX_CHECK_SLOT_SPACE);

    DEBUG_LOG("PrepFrameMapToChChgNet] ChChg: %d, CH-A : %d(%d) -> %d, CH-B : %d(%d) -> %d, startSlot : %d\r\n",
            bPrepToChChg, CROSMgr::getInst()->m_sChSetup.uChannelIdA, m_pChPrimary->IsTxAvailableCh(), uNewChIdA,
            CROSMgr::getInst()->m_sChSetup.uChannelIdB, m_pChSecondary->IsTxAvailableCh(), uNewChIdB, nStartClearSlotID);

    if (bPrepToChChg || (CROSMgr::getInst()->m_sChSetup.uChannelIdA != AIS_CH_NUM_NONE && uNewChIdA == AIS_CH_NUM_NONE))
    {
        if (uNewChIdA != AIS_CH_NUM_NONE && uNewChIdA == CROSMgr::getInst()->m_sChSetup.uChannelIdA)//m_pChPrimary->m_uChNumRx)
            m_pChPrimary->SetLastTxFrameAndFree(nStartClearSlotID);
        else
            m_pChPrimary->PrepToChgCh(nStartClearSlotID);
    }
    else
    {
        DEBUG_LOG("PrepFrameMapToChChgNet] Skip ClrCH-A, ChChg: %d, CH-A : %d(%d) -> %d\r\n",
                bPrepToChChg, CROSMgr::getInst()->m_sChSetup.uChannelIdA, m_pChPrimary->IsTxAvailableCh(), uNewChIdA);
    }

    if (bPrepToChChg || (CROSMgr::getInst()->m_sChSetup.uChannelIdB != AIS_CH_NUM_NONE && uNewChIdB == AIS_CH_NUM_NONE))
    {
        if (uNewChIdB != AIS_CH_NUM_NONE && uNewChIdB == CROSMgr::getInst()->m_sChSetup.uChannelIdB)//m_pChSecondary->m_uChNumRx)
            m_pChSecondary->SetLastTxFrameAndFree(nStartClearSlotID);
        else
            m_pChSecondary->PrepToChgCh(nStartClearSlotID);
    }
    else
    {
        DEBUG_LOG("PrepFrameMapToChChgNet] Skip ClrCH-B, ChChg: %d, CH-B : %d(%d) -> %d\r\n",
                bPrepToChChg, CROSMgr::getInst()->m_sChSetup.uChannelIdB, m_pChSecondary->IsTxAvailableCh(), uNewChIdB);
    }
}

BOOL CLayerNetwork::RunPhaseChangeReportRate(int nTxRxMode, int nTxRxModeBy, UINT uInputChIdA, UINT uInputChIdB, float fNewReportIntervalSec, int nPrevOpMode, int nNewOpMode, int nNewAssignedBy)
{
    //----------------------------------------------------------------------------------------------------------------
    // TRX mode 변경 시 TxRx mode 1(CH-A TX only) -> 2(CH-B TX only) 또는 2 -> 1 일때만 channel change phase 실행하고
    // 그외의 경우에는 모두 Change RR phase 실행한다!
    //----------------------------------------------------------------------------------------------------------------
    BOOL bProcOK = FALSE;

    if (!IsChangingReportRateAvailable() || !CReportRateMgr::getInst()->IsRIValueValid(fNewReportIntervalSec))
    {
        WARNING_LOG("RunChgRR] ignore, wrong param, %d, %d, %.1f\r\n",
                IsChangingReportRateAvailable(), CReportRateMgr::getInst()->IsRIValueValid(fNewReportIntervalSec), fNewReportIntervalSec);
        return FALSE;
    }

    UINT16 uNewChATx, uNewChARx;
    UINT16 uNewChBTx, uNewChBRx;
    BOOL bChSetupChged = GetNewChToChg(nTxRxMode, nTxRxModeBy, uInputChIdA, uInputChIdB, &uNewChATx, &uNewChARx, &uNewChBTx, &uNewChBRx);

    if (!bChSetupChged && fNewReportIntervalSec == CReportRateMgr::getInst()->m_fReportIntervalSec)
    {
        if (nPrevOpMode == nNewOpMode)
        {
            DEBUG_LOG("RunChgRR] Same setup and RR, %d, %d, %d, %.1f\r\n",
                nTxRxMode, nTxRxModeBy, fNewReportIntervalSec, nPrevOpMode);
        }
        else
        {
        #if 0   //모드만 변경 시에도 change RR phase 를 수행해야한다!
            if (nNewOpMode == OPMODE_ASSIGNED_RR || nNewOpMode == OPMODE_ASSIGNED_SLOT)
            {
                int nTimeOut = GetNewFrameTimeOut();
                ChangeReportRateOnlyMsgByModeTimeout(nNewOpMode, nNewAssignedBy, nTimeOut);
            }
            else
                ChangeReportRateOnlyMsgByMode(nNewOpMode, nNewAssignedBy);
            return TRUE;
        #endif

            DEBUG_LOG("RunChgRR] Only mode chg, %d, %d, %d, %d\r\n",
                    nTxRxMode, nTxRxModeBy, nNewOpMode, nPrevOpMode);
        }
    }

    BOOL bOldRRSO = CReportRateMgr::getInst()->IsReportRateForSOTDMA();
    BOOL bNewRRSO = CReportRateMgr::getInst()->IsReportRateForSOTDMA(fNewReportIntervalSec);

    m_nChgRRphasePrevOpMode = nPrevOpMode;
    m_nChgRRphasePrevPosMsg = CAisLib::GetScheduledPosReportMsgID(m_nChgRRphasePrevOpMode);

    if (bChSetupChged)
    {
        if ((m_nOpMode == OPMODE_ASSIGNED_RR && m_bAssignedModeBy == ASSIGNED_MODE_BY_MSG23) && (nTxRxMode == TRXMODE_BY_MSG22_ADDRSD || nTxRxModeBy == TRXMODE_BY_MSG22_BRDCST))
        {
            //--------------------------------------------------------------------------------------------------------
            // Ivan Test report TT_16_6_7_1_Ed2.scn 에서 07:00 항목
            // 메시지-23에 의한 RR 할당모드 실행 중 ROS 에 의한 채널 설정 변경 요구시에는 자동모드로 돌아가도록 한다!
            //--------------------------------------------------------------------------------------------------------
            nNewOpMode = OPMODE_CONTINUOUS;
            nNewAssignedBy = ASSIGNED_MODE_NONE;

            DEBUG_LOG("RunChgRR] Reverts to Auto mode, curOpMode:%d(%d), trxBy:%d, s:%d\r\n",
                    m_nOpMode, m_bAssignedModeBy, nTxRxModeBy, cTimerSys::getInst()->GetCurTimerSec());
        }
    }

    BOOL bOldTxAvailCh1 = m_pChPrimary->IsTxAvailableCh();
    BOOL bOldTxAvailCh2 = m_pChSecondary->IsTxAvailableCh();
    int nOldTRXmode = CROSMgr::getInst()->m_sChSetup.nTxRxMode;

    BOOL bPrepToChChg = FALSE;
    PrepFrameMapToChChgNet(bPrepToChChg, uNewChATx, uNewChBTx);

    m_pChPrimary->SetTxChannelNumber(uNewChATx);
    m_pChPrimary->SetRxChannelNumber(uNewChARx);
    m_pChSecondary->SetTxChannelNumber(uNewChBTx);
    m_pChSecondary->SetRxChannelNumber(uNewChBRx);
    CROSMgr::getInst()->SetTxRxMode(nTxRxMode, nTxRxModeBy);
    CROSMgr::getInst()->SetTrxModeChData(nTxRxMode, nTxRxModeBy, uInputChIdA, uInputChIdB);

    if (bChSetupChged)
        InitStaticReportSec();

    GetNewFrameTimeOut();                                                                // new slot time-out

    if (bOldRRSO)
    {
        if (bNewRRSO)
        {
            //--------------
            // SOTDMA 유지
            //--------------
            if (nTxRxMode == nOldTRXmode ||
                (bOldTxAvailCh1 && bOldTxAvailCh2 &&
                ((uNewChATx != AIS_CH_NUM_NONE && uNewChBTx == AIS_CH_NUM_NONE) || (uNewChATx == AIS_CH_NUM_NONE && uNewChBTx != AIS_CH_NUM_NONE))))        // 이전 TRX 모드와 같거나 두 채널 모드에서 한 채널 모드로 바뀔때
            {
                CReportRateMgr::getInst()->SetNewReportInterval(fNewReportIntervalSec, uNewChATx, uNewChBTx);            // ProcessPhaseChgReportRateFirst 에서 SetChReportInterval(m_fNewReportIntervalSecSO) 을 호출하기 위해 임시 저장용

                m_pChgRRMasterCH = GetNextPosTxCH(uNewChATx, uNewChBTx);

                if (m_pChgRRMasterCH)
                {
                    DEBUG_LOG("OPPHASE_CHG_RR_PREP_WAIT] ChgRRMasterCH : %d,txAvail:%d, ASmode: %d, TMO: %d, s:%d\r\n",
                            m_pChgRRMasterCH->GetChOrdinal(), m_pChgRRMasterCH->IsTxAvailableCh(), nNewOpMode != OPMODE_CONTINUOUS, m_nNewFrameTimeOut, cTimerSys::getInst()->GetCurTimerSec());

                    SetOpMode(nNewOpMode, nNewAssignedBy, FALSE);   // 각 채널은 아직 할당모드로 설정되지 않았음. 보고율 변경 완료 후 ProcessPhaseChgReportRateSecond 내에서 할당모드로 설정됨
                    SetOpPhase(OPPHASE_CHG_RR_PREP_FIRST);
                    bProcOK = TRUE;
                }
                else
                {
                    WARNING_LOG("OPPHASE_CHG_RR_PREP_WAIT] Error! ChgRRMasterCH NULL! goto ROUTINE, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
                    SetOpPhase(OPPHASE_ROUTINE);
                }
            }
            else
            {
                m_pNetworkEntryCH = NULL;
                if (bOldTxAvailCh1 && uNewChATx != AIS_CH_NUM_NONE)
                    m_pNetworkEntryCH = m_pChPrimary;
                else if (bOldTxAvailCh2 && uNewChBTx != AIS_CH_NUM_NONE)
                    m_pNetworkEntryCH = m_pChSecondary;

                m_nNetworkEntryPrepSlot = SLOTID_NONE;
                PrepFrameMapToChChgNet(TRUE, uNewChATx, uNewChBTx);

                m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(nPrevOpMode);
                CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);

                DEBUG_LOG("RunChgRR] SO, goto NetEntry, EntryCH : %d, TX : %d(%d), C : %d(%d)\r\n",
                        m_pNetworkEntryCH ? m_pNetworkEntryCH->GetChOrdinal() : -1,
                        m_nNetworkEntryPrepSlot, m_pChPrimary->GetSlotIdFromFrameSlotID(m_nNetworkEntryPrepSlot),
                        OPSTATUS::nCurFrameMapSlotID, m_pChPrimary->GetSlotIdFromFrameSlotID(OPSTATUS::nCurFrameMapSlotID));

                SetOpMode(nNewOpMode, nNewAssignedBy, TRUE);
                SetOpPhase(OPPHASE_NETWORKENTRY);
                bProcOK = TRUE;
            }
        }
        else
        {
            //--------------
            // SO -> ITDMA
            //--------------
            m_pChPrimary->TerminatePosReportSO();
            m_pChSecondary->TerminatePosReportSO();

            int nNewTxStartSlot = GetNewReportRateStartNS();

            CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);                    // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.

            DEBUG_LOG("RunChgRR] goto NetEntry, SO -> IT, newRI : %.1f, newNI:%d, entrySlot : %d, C : %d, nextTxSlot : %d,%d\r\n",
                    fNewReportIntervalSec, CAisLib::GetNIfromRI(fNewReportIntervalSec), nNewTxStartSlot, OPSTATUS::nCurFrameMapSlotID,
                    m_pChPrimary->m_nRoutineNextTxSlot, m_pChSecondary->m_nRoutineNextTxSlot);

            SetOpMode(nNewOpMode, nNewAssignedBy, TRUE);

            m_pNetworkEntryCH = NULL;
            m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(nPrevOpMode);

            m_nNetworkEntryPrepSlot = nNewTxStartSlot;
            SetOpPhase(OPPHASE_NETWORKENTRY);
            bProcOK = TRUE;
        }
    }
    else
    {
        if (bNewRRSO)
        {
            //--------------
            // ITDMA -> SO
            //--------------
            m_pChPrimary->TerminatePosReportITDMA();
            m_pChSecondary->TerminatePosReportITDMA();

            CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);                    // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.

            DEBUG_LOG("RunChgRR] IT -> SO, goto NetEntry, ASmode: %d, TMO: %d, RI : %.1f\r\n",
                    m_nNewFrameTimeOut, CReportRateMgr::getInst()->m_fReportIntervalSec);

            SetOpMode(nNewOpMode, nNewAssignedBy, TRUE);

            m_pNetworkEntryCH = NULL;
            m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(nPrevOpMode);
            m_nNetworkEntryPrepSlot = SLOTID_NONE;
            SetOpPhase(OPPHASE_NETWORKENTRY);
            bProcOK = TRUE;
        }
        else
        {
            //--------------
            // ITDMA 유지
            //--------------
            m_pChPrimary->TerminatePosReportITDMA();
            m_pChSecondary->TerminatePosReportITDMA();

            int nNewTxStartSlot = GetNewReportRateStartNS();
            CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);                    // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.

            DEBUG_LOG("RunChgRR] goto NetEntry, IT, newNSS : %d\r\n", nNewTxStartSlot);

            SetOpMode(nNewOpMode, nNewAssignedBy, TRUE);

            m_pNetworkEntryCH = NULL;
            m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(nPrevOpMode);
            m_nNetworkEntryPrepSlot = nNewTxStartSlot;
            SetOpPhase(OPPHASE_NETWORKENTRY);
            bProcOK = TRUE;
        }
    }
    return TRUE;
}

void CLayerNetwork::StartAssignedMode_ReportRate(UINT uBaseStMMSI, BYTE bAssignedModeBy, int nTxRxMode, int nTxRxModeBy, float fReportIntervalSec)
{
    if (!CReportRateMgr::getInst()->IsRIValueValid(fReportIntervalSec))
    {
        WARNING_LOG("[ASSR-StartAssignRR] RI invalid : %d\r\n", fReportIntervalSec);
        return;
    }

    DEBUG_LOG("[ASSR-StartAssignRR] assignBy : %d, TRXmode : %d,by:%d, RI : %.1f, curOpMode:%d\r\n", bAssignedModeBy, nTxRxMode, nTxRxModeBy, fReportIntervalSec, m_nOpMode);

    m_uSlotAssignedModeBaseStMMSI = uBaseStMMSI;

    int nPrevOpMode = m_nOpMode;
    switch(nPrevOpMode)
    {
    case OPMODE_CONTINUOUS:
        if (!IsAssignedModeOpAvailable())
        {
            WARNING_LOG("[ASSR-StartAssignRR] not avail phase : %d\r\n", m_nOpPhase);
            return;
        }

        RunChannelSetup(CROSMgr::getInst()->m_sRosData.nRosMode, nTxRxMode, nTxRxModeBy, CROSMgr::getInst()->m_sChSetup.uChannelIdA, CROSMgr::getInst()->m_sChSetup.uChannelIdB, fReportIntervalSec, nPrevOpMode, OPMODE_ASSIGNED_RR, bAssignedModeBy);
        break;

    case OPMODE_ASSIGNED_RR:
        {
            //---------------------------------------------------------------------------------------------
            // assignment re-issued
            //---------------------------------------------------------------------------------------------
            // refer to ITU-R.1371-5 3.3.6 Assigned operation
            // Assignments are limited in time and will be re-issued by the competent authority as needed.
            // The last received assignment should continue or overwrite the previous assignment.
            //---------------------------------------------------------------------------------------------

            {
                DEBUG_LOG("[ASSR-StartAssignRR] Re-issue : %d, RI : %.1f\r\n", bAssignedModeBy, fReportIntervalSec);

                if (nTxRxMode == CROSMgr::getInst()->m_sChSetup.nTxRxMode && fReportIntervalSec == CReportRateMgr::getInst()->m_fReportIntervalSec && nPrevOpMode == OPMODE_ASSIGNED_RR)
                {
                    CChTxScheduler::SetAssignedRRTimeOut();
                }
                else
                {
                    DEBUG_LOG("[ASSR-StartAssignRR] different setup, Start new assigned mode, TRXmode : %d -> %d, by:%d, RI : %.1f -> %.1f, opMode : %d\r\n",
                            CROSMgr::getInst()->m_sChSetup.nTxRxMode, nTxRxMode, nTxRxModeBy, CReportRateMgr::getInst()->m_fReportIntervalSec, fReportIntervalSec, nPrevOpMode);
                    RunChannelSetup(CROSMgr::getInst()->m_sRosData.nRosMode, nTxRxMode, nTxRxModeBy, CROSMgr::getInst()->m_sChSetup.uChannelIdA, CROSMgr::getInst()->m_sChSetup.uChannelIdB, fReportIntervalSec, nPrevOpMode, OPMODE_ASSIGNED_RR, bAssignedModeBy);
                }
            }
        }
        break;

    case OPMODE_ASSIGNED_SLOT:
        if (!IsAssignedModeOpAvailable())
        {
            WARNING_LOG("[ASSR-StartAssignRR] not avail phase : %d\r\n", m_nOpPhase);
            return;
        }

        if (nTxRxMode != CROSMgr::getInst()->m_sChSetup.nTxRxMode)
            RunChannelSetup(CROSMgr::getInst()->m_sRosData.nRosMode, nTxRxMode, nTxRxModeBy, CROSMgr::getInst()->m_sChSetup.uChannelIdA, CROSMgr::getInst()->m_sChSetup.uChannelIdB, fReportIntervalSec, nPrevOpMode, OPMODE_ASSIGNED_RR, bAssignedModeBy);
        else
            RunReturnFromAssignedMode_Slot(GetNextPosTxCH(), OPMODE_ASSIGNED_RR, bAssignedModeBy, fReportIntervalSec);
        break;
    }
}

void CLayerNetwork::RunReturnToAutoModeFromAssignedMode_RR()
{
    if (m_nOpMode == OPMODE_ASSIGNED_RR)
    {
        SetOpMode(OPMODE_CONTINUOUS, ASSIGNED_MODE_NONE, TRUE);                                // Keep this line!!! It is needed to decide TRX mode below!
        float fAutoRI = CReportRateMgr::getInst()->CheckAutoModeReportInterval(FALSE, FALSE);

        DEBUG_LOG("ReturnToAutoMode] current, RI:%.1f, TRXmode : %d,%d, s:%d\r\n",
                fAutoRI, CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, cTimerSys::getInst()->GetCurTimerSec());

        //---------------------------------------
        // Ivan Test report TT16_6_7_1_Ed1
        //---------------------------------------
        INT8 nTxRxMode = CROSMgr::getInst()->m_sChSetup.nTxRxMode;
        INT8 nTxRxModeBy = CROSMgr::getInst()->m_sChSetup.nTxRxModeBy;
        if (CROSMgr::getInst()->m_sChSetup.nTxRxModeBy == TRXMODE_BY_MSG23)
        {
            CH_SETUP chSetup;
            memcpy(&chSetup, &CROSMgr::getInst()->m_sChSetup, sizeof(CH_SETUP));
            chSetup.nTxRxMode = CROSMgr::getInst()->m_sRosData.sChSetup.nTxRxMode;
            chSetup.nTxRxModeBy = CROSMgr::getInst()->m_sRosData.sChSetup.nTxRxModeBy;
            CROSMgr::getInst()->GetNewTrxModeByPriority(CROSMgr::getInst()->m_sRosData.nRosMode, &chSetup, &nTxRxMode, &nTxRxModeBy);

            DEBUG_LOG("ReturnToAutoMode] TRXmodeByMsg23, TRXmode: %d, by: %d, s:%d\r\n",
                    nTxRxMode, nTxRxModeBy, cTimerSys::getInst()->GetCurTimerSec());
        }

        if (RunChannelSetup(CROSMgr::getInst()->m_sRosData.nRosMode, nTxRxMode, nTxRxModeBy, CROSMgr::getInst()->m_sChSetup.uChannelIdA, CROSMgr::getInst()->m_sChSetup.uChannelIdB,
                            fAutoRI, OPMODE_ASSIGNED_RR, OPMODE_CONTINUOUS, ASSIGNED_MODE_NONE))
        {
            // ROS 영역내 진입하여 채널 변경시 마다 TXT42 메시지 송신하도록 수정
            if (CROSMgr::getInst()->IsRosIndexValid_Ext(CROSMgr::getInst()->m_sRosData.nRosIdx) && CROSMgr::getInst()->m_vAllRosDATA[CROSMgr::getInst()->m_sRosData.nRosIdx].dSrcMMSI != AIS_AB_MMSI_NULL)
            {
                CLayerNetwork::getInst()->m_uChannelManageModeBaseStMMSI = CROSMgr::getInst()->m_vAllRosDATA[CROSMgr::getInst()->m_sRosData.nRosIdx].dSrcMMSI;
                CMKD::getInst()->SendTXTtoPI(TXT_ID_CH_MNG_MODE, true);
            }
        }
    }
}

void CLayerNetwork::StartAssignedMode_Slot(CChannelMgr *pChannel, int nMsgID, UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD wIncrement)
{
    //--------------------------------------------------
    // IEC-61993-2(ed2) 16.6
    // Ivan test report TT16.6.3
    //--------------------------------------------------

    if (!IsAssignedModeOpAvailable())
    {
        WARNING_LOG("StartAssignSlotMode] ignore, opPhase : %d\r\n", m_nOpPhase);
        return;
    }

    if (!m_pChPrimary->IsTxAvailableCh() && !m_pChSecondary->IsTxAvailableCh())
    {
        WARNING_LOG("StartAssignSlotMode] ignore, both CH not available, s : %d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        return;
    }

    BOOL bDoProcCmd = TRUE;

    float fReportIntervalSec = INT_SLOT_TO_SEC(wIncrement);

    if (!CReportRateMgr::getInst()->IsRIValueValid(fReportIntervalSec))
    {
        WARNING_LOG("StartAssignSlotMode] RI invalid : %.1f\r\n", fReportIntervalSec);
        return;
    }

    DEBUG_LOG("StartAssignSlotMode] input, CH : %d, baseSt:%09d, rcvFrame:%d, rcvSlot:%d, off:%d, inc:%d, RI:%.1f\r\n",
            pChannel ? pChannel->GetChOrdinal() : -1, uBaseStMMSI, wRcvFrameID, wRcvSlotID, wOffset, wIncrement, fReportIntervalSec);

    if (!pChannel)
        pChannel = m_pChPrimary;

    CChannelMgr *pMasterCh = m_pChPrimary;
    CChannelMgr *pSlaveCh = m_pChSecondary;//GetOppositeChPtr(pMasterCh);
    int nIncrementCh = wIncrement;
    if (pMasterCh && pMasterCh->IsTxAvailableCh() && pSlaveCh && pSlaveCh->IsTxAvailableCh())
    {
        nIncrementCh <<= 1;
    }
    else
    {
        pSlaveCh = NULL;
         if (m_pChPrimary->IsTxAvailableCh())
             pMasterCh = m_pChPrimary;
        else
            pMasterCh = m_pChSecondary;
    }

    WORD wOffsetMaster= wOffset;
    WORD wOffsetSlave = wOffsetMaster + wIncrement;

    if (m_nOpMode != OPMODE_ASSIGNED_SLOT && m_bAssignedModeBy != ASSIGNED_MODE_BY_MSG16)
    {
        // 슬롯할당모드에 처음 진입시
        if (CSetupMgr::getInst()->IsAisAClass())
        {
            // IEC-61993-2 7.3.4.2 수신받은 보고율이 현재 실행중인 전체 보고율보다 같거나 높으면 할당모드 실행
            bDoProcCmd = IsHigherOrEqualAssignedRR(pChannel, fReportIntervalSec);
            DEBUG_LOG("[ASSS-StartAssignSlotMode] Check higher than or equal to autoMode, runAssigned : %d, autoRI : %.1f, assignRI : %.1f\r\n",
                    bDoProcCmd, CReportRateMgr::getInst()->m_fReportIntervalSec, fReportIntervalSec);
        }

        if (!bDoProcCmd)
            return;

        CReportRateMgr::getInst()->RecalcReportInterval(fReportIntervalSec);                                // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.

        DEBUG_LOG("[ASSS-StartAssignSlotMode] by %9d\r\n", uBaseStMMSI);
    }
    else
    {
        DEBUG_LOG("[ASSS-StartAssignSlotMode] curOpMode : %d, AssignedBy : %d\r\n", m_nOpMode, m_bAssignedModeBy);
    }

    GetNewFrameTimeOut();

    DEBUG_LOG("[ASSS-StartAssignSlotMode] RunAssignedSlotMode, MasterCH : %d,avail:%d, SlaveCH : %d,avail:%d, tmo : %d\r\n",
            pMasterCh ? pMasterCh->GetChOrdinal() : -1, pMasterCh ? pMasterCh->IsTxAvailableCh() : 0,
            pSlaveCh ? pSlaveCh->GetChOrdinal() : -1, pSlaveCh ? pSlaveCh->IsTxAvailableCh() : 0, m_nNewFrameTimeOut);

    if (pMasterCh)
        pMasterCh->RunPhaseAssignedSlot(uBaseStMMSI, wRcvFrameID, wRcvSlotID, wOffsetMaster, wIncrement, nIncrementCh, m_nNewFrameTimeOut);
    if (pSlaveCh)
        pSlaveCh->RunPhaseAssignedSlot(uBaseStMMSI, wRcvFrameID, wRcvSlotID, wOffsetSlave, wIncrement, nIncrementCh, m_nNewFrameTimeOut);

    if (m_nOpMode != OPMODE_ASSIGNED_SLOT && m_bAssignedModeBy != ASSIGNED_MODE_BY_MSG16)
        SetOpMode(OPMODE_ASSIGNED_SLOT, ASSIGNED_MODE_BY_MSG16, TRUE);                            // 호출순서 지킬것! 맨 마지막에 호출해야한다.
}

void CLayerNetwork::RunReturnFromAssignedMode_Slot(CChannelMgr *pChannel, int nOpModeToReturn, int nAssignedModeBy, float fNewReportIntervalSec)
{
    //--------------------------------------------------------------------------------------------------------
    // refer to 1371-5 Annex2 3.3.6.2.3 Returning to autonomous and continuous mode from slot assigned mode
    // Unless a new assignment is received, the assignment should be terminated, when the slot time-out
    // reaches zero. At this stage, the station should return to autonomous and continuous mode.
    // The station should initiate the return to autonomous and continuous mode as soon as it detects
    // an assigned slot with a zero slot time-out. This slot should be used to re-enter the network.
    // The station should randomly select an available slot from candidate slots within a NI of the current
    // slot and make this the NSS. It should then substitute the assigned slot for an ITDMA slot and
    // should use this to transmit the relative offset to the new NSS. From this point on, the process should
    // be identical to the network entry phase (see § 3.3.5.2).
    // => 슬롯할당모드로부터 자동모드로의 복귀
    //--------------------------------------------------------------------------------------------------------

    WORD wLastAssignedTxSlotCh1;
    WORD wLastAssignedTxSlotCh2;

    if (m_nOpMode != OPMODE_ASSIGNED_SLOT)
        return;

    if (!pChannel)
        pChannel = GetNextPosTxCH();

    m_pChPrimary->GetInternalAllocatedSlot_SO(m_pChPrimary->m_nNextStartSI, m_pChPrimary->m_nSizeSI, &wLastAssignedTxSlotCh1, AIS_MSG_NO_ID_02);
    m_pChSecondary->GetInternalAllocatedSlot_SO(m_pChSecondary->m_nNextStartSI, m_pChSecondary->m_nSizeSI, &wLastAssignedTxSlotCh2, AIS_MSG_NO_ID_02);

    DEBUG_LOG("[ASSS-FinishSlotAssignedP] lastAssignedSlot, CH-1:%d(%d), CH-2:%d(%d), newRI : %.1f\r\n",
            wLastAssignedTxSlotCh1, m_pChPrimary->GetSlotIdFromFrameSlotID(wLastAssignedTxSlotCh1),
            wLastAssignedTxSlotCh2, m_pChSecondary->GetSlotIdFromFrameSlotID(wLastAssignedTxSlotCh2),
            fNewReportIntervalSec);

    if (nOpModeToReturn == OPMODE_CONTINUOUS && fNewReportIntervalSec < 0)
        fNewReportIntervalSec = CReportRateMgr::getInst()->CheckAutoModeReportInterval(FALSE, FALSE);            // 자동모드에서 수행될 보고주기가 주어지지않았으면 재계산

    if (!CReportRateMgr::getInst()->IsRIValueValid(fNewReportIntervalSec))
    {
        WARNING_LOG("[ASSS-FinishSlotAssignedP] Error! RI invalid : %d, opModeToSet : %d\r\n",
                fNewReportIntervalSec, nOpModeToReturn);

        SetOpMode(nOpModeToReturn, nAssignedModeBy, TRUE);
        SetOpPhase(OPPHASE_MONITOR_VDL);
        return;
    }

    CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);                                    // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.

    //-------------------------------------------------------------------------------
    // 새 모드에서 사용될 채널별 NSS 및 네트워크 진입 최초 송신슬롯을 결정한다.
    //-------------------------------------------------------------------------------
    int nLastTxSlot = (pChannel == m_pChPrimary ? wLastAssignedTxSlotCh1 : wLastAssignedTxSlotCh2);

    m_pChPrimary->TerminateSlotAssignedMode(wLastAssignedTxSlotCh1);
    m_pChSecondary->TerminateSlotAssignedMode(wLastAssignedTxSlotCh2);

    int nPrevOpMode = m_nOpMode;
    SetOpMode(nOpModeToReturn, nAssignedModeBy, TRUE);

    m_pNetworkEntryCH = pChannel;
    m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(nPrevOpMode);

    if (nPrevOpMode == m_nOpMode)
        m_nNetworkEntryPrepSlot = nLastTxSlot;
    else
        m_nNetworkEntryPrepSlot = SLOTID_NONE;
    SetOpPhase(OPPHASE_NETWORKENTRY);
}

void CLayerNetwork::RunReturnToAutoMode()
{
    switch(m_nOpMode)
    {
    case OPMODE_ASSIGNED_RR:
        RunReturnToAutoModeFromAssignedMode_RR();
        break;
    case OPMODE_ASSIGNED_SLOT:
        {
            RunReturnFromAssignedMode_Slot(GetNextPosTxCH(), OPMODE_CONTINUOUS);
        }
        break;
    }
}

void CLayerNetwork::RunChannelSwitch()
{
    //--------------------------------------
    // Switch primary and secondary channel
    //--------------------------------------
    CChannelMgr *pTmp = m_pChPrimary;
    m_pChPrimary = m_pChSecondary;
    m_pChSecondary = pTmp;
}

CChannelMgr* CLayerNetwork::GetNextTxChPtrForMsg(UINT16 uMsgID)
{
    CChannelMgr *pLastMsgCh = NULL;
    CChannelMgr *pNextMsgCh = NULL;

    switch(uMsgID)
    {
    case AIS_MSG_NO_ID_05:
    case AIS_MSG_NO_ID_24:
    case AIS_MSG_NO_ID_24_A:
        pLastMsgCh = m_pLastTxChMsg5;
        break;
    case AIS_MSG_NO_ID_27:
        pLastMsgCh = m_pLastTxChMsg27;
        break;
    default:
        break;
    }

    if (!pLastMsgCh)
    {
        if (m_pChPrimary->IsTxAvailableCh())
            pNextMsgCh = m_pChPrimary;
        else if (m_pChSecondary->IsTxAvailableCh())
            pNextMsgCh = m_pChSecondary;
    }
    else if (pLastMsgCh == m_pChPrimary)
    {
        pNextMsgCh = m_pChPrimary;
        if (m_pChSecondary->IsTxAvailableCh())
            pNextMsgCh = m_pChSecondary;
    }
    else if (pLastMsgCh == m_pChSecondary)
    {
        pNextMsgCh = m_pChSecondary;
        if (m_pChPrimary->IsTxAvailableCh())
            pNextMsgCh = m_pChPrimary;
    }

    if (!pNextMsgCh)
        pNextMsgCh = m_pChPrimary;
    return pNextMsgCh;
}

void CLayerNetwork::ProcStaticVoyageDataChanged()
{
    //-----------------------------------------------------------------------------------------------------------------
    // Static Data 변경시, 비주기적인 Msg05 전송
    // Refer to IEC-61993-2(ed2) ******** Static data reporting intervals, ********.2 Required results
    // Confirm that the EUT transmits Message 5 with a reporting interval of 6 min alternating Channel A and Channel B.
    // a) Confirm that the EUT transmits Message 5 within 1 min reverting to a reporting interval of 6 min.
    //-----------------------------------------------------------------------------------------------------------------

    if (m_nOpPhase != OPPHASE_MONITOR_VDL && !CAisLib::IsValidMMSI_MobileSt(cShip::getOwnShipInst()->xStaticData.dMMSI))
    {
        SetOpPhase(OPPHASE_MONITOR_VDL);
        return;
    }

    // within 1min, Ivan test report TT14_2.scn
    int nTimeOutSlot = CAisLib::GetNumSlotFromSec(60);
    ReserveTxStaticVoyageMsg(NULL, TRUE, nTimeOutSlot);
}

BOOL CLayerNetwork::CheckTxBuffUnscheduledTxMsg(int nMsgID)
{
    TDMA_UNSCHE_INFO *pResBlock;
    if ((pResBlock = m_pChPrimary->m_pVdlTxMgr->GetUnscheduledUsedTxBuffPtr(nMsgID)))
    {
        return TRUE;
    }
    if ((pResBlock = m_pChSecondary->m_pVdlTxMgr->GetUnscheduledUsedTxBuffPtr(nMsgID)))
    {
        return TRUE;
    }
    return FALSE;
}

BOOL CLayerNetwork::ReserveTxStaticVoyageMsg(CChannelMgr *pTxCh, BOOL bCheckExsitingTxBuff, int nTimeOutSlot)
{
    int nStaticVoyageMsgID = AIS_MSG_NO_ID_05;

    if (CSetupMgr::getInst()->IsAisBClass()) 
        nStaticVoyageMsgID = AIS_MSG_NO_ID_24_A;

    if (!OPSTATUS::bFirstFrameOnBootDone && !OPSTATUS::bSysInit2minDone)
    {
        return FALSE;
    }

    if (CVdlTxMgr::IsTxAvailable())
    {
        if (!pTxCh)
            pTxCh = GetNextTxChPtrForMsg(nStaticVoyageMsgID);

        if (bCheckExsitingTxBuff)
        {
            if (CheckTxBuffUnscheduledTxMsg(nStaticVoyageMsgID))
                return TRUE;
        }

        if (pTxCh)
        {
            DEBUG_LOG("ReserveTxMsgStatic] TxCh : %d, M:%d, enableUsed:%d, sizeSI: %d, s:%d\r\n",
                pTxCh->GetChOrdinal(), nStaticVoyageMsgID, bCheckExsitingTxBuff, nTimeOutSlot, cTimerSys::getInst()->GetCurTimerSec());

            if (CSetupMgr::getInst()->IsAisBClass())
            {
                bCheckExsitingTxBuff = FALSE;
                if (pTxCh->m_pVdlTxMgr->SetUnScheTxMsgDataBuff(nStaticVoyageMsgID, bCheckExsitingTxBuff, RATDMA_SI, nTimeOutSlot) &&    // MSG 24_partA
                    pTxCh->m_pVdlTxMgr->SetUnScheTxMsgDataBuff(AIS_MSG_NO_ID_24, bCheckExsitingTxBuff, RATDMA_SI, nTimeOutSlot))        // MSG 24_partB
                {
                    m_pLastTxChMsg5 = pTxCh;
                    m_dwLastTxSecMsg5 = cTimerSys::getInst()->GetCurTimerSec();
                    InitStaticReportSec();
                    return TRUE;
                }
            }
            else
            {
                if (pTxCh->m_pVdlTxMgr->SetUnScheTxMsgDataBuff(nStaticVoyageMsgID, bCheckExsitingTxBuff, RATDMA_SI, nTimeOutSlot))
                {
                    m_pLastTxChMsg5 = pTxCh;
                    m_dwLastTxSecMsg5 = cTimerSys::getInst()->GetCurTimerSec();
                    InitStaticReportSec();
                    return TRUE;
                }
            }
        }
        else
        {
            WARNING_LOG("ReserveTxMsgStatic] TxCh NULL!, M: %d, enableUsed:%d, s:%d\r\n",
                nStaticVoyageMsgID, bCheckExsitingTxBuff, cTimerSys::getInst()->GetCurTimerSec());
        }
    }
    return FALSE;
}

void CLayerNetwork::ProcessTxMsg24B()
{
    //----------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 ed3.0
    //----------------------------------------------------------------------------------------------
    // 14.3.2.4 Static data reporting intervals
    // c) Confirm that the EUT transmits Message 24B within 12 min after power up and thereafter
    //    once within the next 24 h. Confirm that Message 24B contains correct Vendor ID.
    //----------------------------------------------------------------------------------------------

    if (!CVdlTxMgr::IsTxAvailable())
        return;

    if (!OPSTATUS::bFirstFrameOnBootDone && !OPSTATUS::bSysInit2minDone)
    {
        return;
    }

    const int nChkTermSec = 10;
    const int nTxTermSec = 60;
    static DWORD dwChkSec = 0;

    if (cTimerSys::getInst()->GetTimeDiffSec(dwChkSec) >= nChkTermSec)
    {
        DWORD dwElapSec = cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg24);

        DEBUG_LOG("ReserveTxMsg24] cnt: %d, sec: %d, elapSec: %d, elapSinceMsg5: %d, s:%d\r\n",
            m_dwCntTxSecMsg24, m_dwLastTxSecMsg24, cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg24),
            cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg5), cTimerSys::getInst()->GetCurTimerSec());

        if (cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg5) > 10)
        {
            if ((m_dwCntTxSecMsg24 <= 0 &&  ((STATIC_REPORT_FIRST24_INTSEC-nTxTermSec) <= dwElapSec)) ||
                (m_dwCntTxSecMsg24 > 0 && ((STATIC_REPORT_NEXT24_INTSEC-nTxTermSec) <= dwElapSec)))
            {
                CChannelMgr *pTxCh = GetOppositeTxChPtr(m_pLastTxChMsg24);

                DEBUG_LOG("ReserveTxMsg24] ----------------------------- Shoot! CH: 0x%x(0x%x), cnt: %d, sec: %d, elapSec: %d, elapSinceMsg5: %d, %d\r\n",
                        (DWORD)pTxCh, (DWORD)m_pLastTxChMsg24, m_dwCntTxSecMsg24, m_dwLastTxSecMsg24, cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg24),
                        cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg5), cTimerSys::getInst()->GetCurTimerSec());

                if (CLayerNetwork::getInst()->ReserveTxMsg24B(pTxCh))
                {
                    m_pLastTxChMsg24 = pTxCh;
                    m_dwCntTxSecMsg24++;
                    m_dwLastTxSecMsg24 = cTimerSys::getInst()->GetCurTimerSec();
                }
            }
            else
            {
                WARNING_LOG("ReserveTxMsg24] ----------------------------- Waiting! cnt: %d, sec: %d, elapSec: %d, elapSinceMsg5: %d, %d\r\n",
                        m_dwCntTxSecMsg24, m_dwLastTxSecMsg24, cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg24),
                        cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg5), cTimerSys::getInst()->GetCurTimerSec());
            }
        }

        dwChkSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}

BOOL CLayerNetwork::ReserveTxMsg24B(CChannelMgr *pTxCh)
{
    if (!pTxCh)
    {
        WARNING_LOG("ReserveTxMsg24] xxxxxxxxxxxxxxxxxxxxxxxxxxxx ERROR! Channel NULL! cnt: %d, sec: %d, elapSec: %d, elapSinceMsg5: %d, %d\r\n",
                m_dwCntTxSecMsg24, m_dwLastTxSecMsg24, cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg24),
                cTimerSys::getInst()->GetTimeDiffSec(m_dwLastTxSecMsg5), cTimerSys::getInst()->GetCurTimerSec());
        return FALSE;
    }

    //-------------------------------------------------
    // Refer to IEC 61993-2 ed3.0 14.3.2.4 CCS_witness
    //-------------------------------------------------
    TDMA_UNSCHE_INFO *pResBlock;
    if ((pResBlock = m_pChPrimary->m_pVdlTxMgr->GetUnscheduledUsedTxBuffPtr(AIS_MSG_NO_ID_24)))
    {
        return TRUE;
    }
    else if ((pResBlock = m_pChSecondary->m_pVdlTxMgr->GetUnscheduledUsedTxBuffPtr(AIS_MSG_NO_ID_24)))
    {
        return TRUE;
    }

    if (!pResBlock)
    {
        BOOL bCheckExistingMsg24 = FALSE;
        const int nTimeOutSlotMsg24 = RATDMA_SI;
        int nMsg24SizeSI = RATDMA_SI;

        DEBUG_LOG("ReserveTxMsg24] newAlloc, TxCh : %d, sizeSI: %d, s:%d\r\n",
            pTxCh->GetChOrdinal(), nTimeOutSlotMsg24, cTimerSys::getInst()->GetCurTimerSec());

        return pTxCh->m_pVdlTxMgr->SetUnScheTxMsgDataBuff(AIS_MSG_NO_ID_24, bCheckExistingMsg24, nMsg24SizeSI, nTimeOutSlotMsg24);
    }
    return FALSE;
}

void CLayerNetwork::InitChSetupNetLayer(CH_SETUP *psChSetup)
{
    CReportRateMgr::getInst()->SetReportRateDoubleMode(CROSMgr::getInst()->IsTrModeAvailable(CROSMgr::getInst()->m_sRosData.nRosMode, psChSetup));

    CROSMgr::getInst()->SetTxPowerModeROS(psChSetup->uTxPower);

    m_pChPrimary->SetTxChannelNumber(psChSetup->uChannelIdA);
    m_pChPrimary->SetRxChannelNumber(psChSetup->uChannelIdA);
    m_pChPrimary->ClearFrameMap();

    m_pChSecondary->SetTxChannelNumber(psChSetup->uChannelIdB);
    m_pChSecondary->SetRxChannelNumber(psChSetup->uChannelIdB);
    m_pChSecondary->ClearFrameMap();
}

BOOL CLayerNetwork::GetNewChToChg(INT8 nNewTxRxMode, INT8 nNewTxRxModeBy, UINT16 uInputChIdA, UINT16 uInputChIdB, UINT16 *puNewChATx, UINT16 *puNewChARx, UINT16 *puNewChBTx, UINT16 *puNewChBRx)
{
    BOOL bChSetupChged = FALSE;

    // nNewTxRxModeBy도 변경된 경우 채널 setup 변경된 것으로 판단함.
    if (nNewTxRxMode != CROSMgr::getInst()->m_sChSetup.nTxRxMode || nNewTxRxModeBy != CROSMgr::getInst()->m_sChSetup.nTxRxModeBy)
        bChSetupChged = TRUE;

    *puNewChATx = uInputChIdA;
    *puNewChARx = uInputChIdA;
    *puNewChBTx = uInputChIdB;
    *puNewChBRx = uInputChIdB;

    switch(nNewTxRxMode)
    {
    case TRXMODE_TXARXA_TXBRXB:
        *puNewChATx = uInputChIdA;
        *puNewChARx = uInputChIdA;
        *puNewChBTx = uInputChIdB;
        *puNewChBRx = uInputChIdB;
        break;
    case TRXMODE_TXARXA____RXB:
        *puNewChATx = uInputChIdA;
        *puNewChARx = uInputChIdA;
        *puNewChBTx = AIS_CH_NUM_NONE;
        *puNewChBRx = uInputChIdB;
        break;
    case TRXMODE____RXA_TXBRXB:
        *puNewChATx = AIS_CH_NUM_NONE;
        *puNewChARx = uInputChIdA;
        *puNewChBTx = uInputChIdB;
        *puNewChBRx = uInputChIdB;
        break;
    case TRXMODE____RXA____RXB:
        *puNewChATx = AIS_CH_NUM_NONE;
        *puNewChARx = uInputChIdA;
        *puNewChBTx = AIS_CH_NUM_NONE;
        *puNewChBRx = uInputChIdB;
        break;
    case TRXMODE____RXA_______:
        *puNewChATx = AIS_CH_NUM_NONE;
        *puNewChARx = uInputChIdA;
        *puNewChBTx = AIS_CH_NUM_NONE;
        *puNewChBRx = AIS_CH_NUM_NONE;
        break;
    case TRXMODE___________RXB:
        *puNewChATx = AIS_CH_NUM_NONE;
        *puNewChARx = AIS_CH_NUM_NONE;
        *puNewChBTx = AIS_CH_NUM_NONE;
        *puNewChBRx = uInputChIdB;
        break;
    case TRXMODE_TXARXA_______:
        *puNewChATx = uInputChIdA;
        *puNewChARx = uInputChIdA;
        *puNewChBTx = AIS_CH_NUM_NONE;
        *puNewChBRx = AIS_CH_NUM_NONE;
        break;
    }

    if (*puNewChATx != m_pChPrimary->m_uChNumTx || *puNewChBTx != m_pChSecondary->m_uChNumTx || *puNewChARx != m_pChPrimary->m_uChNumRx || *puNewChBRx != m_pChSecondary->m_uChNumRx)
        bChSetupChged = TRUE;

    DEBUG_LOG("GetNewChToChg] chg: %d, TRXmode: %d(%d) -> %d(%d) inputA: %d, inputB: %d, CH-A: %d,%d -> %d,%d, CH-B: %d,%d -> %d,%d\r\n",
            bChSetupChged, CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, nNewTxRxMode, nNewTxRxModeBy, uInputChIdA, uInputChIdB,
            m_pChPrimary->m_uChNumTx, m_pChPrimary->m_uChNumRx, *puNewChATx, *puNewChARx,
            m_pChSecondary->m_uChNumTx, m_pChSecondary->m_uChNumRx, *puNewChBTx, *puNewChBRx);

    return bChSetupChged;
}

BOOL CLayerNetwork::RunChannelSetup(int nRosMode, INT8 nTxRxMode, INT8 nTxRxModeBy, UINT16 uInputChIdA, UINT16 uInputChIdB, float fNewReportIntervalSec, int nPrevOpMode, int nNewOpMode, int nNewAssignedBy)
{
    //---------------------------------------------------------------------
    // nRosMode    : ROS_MODE_HIGH_SEA_ZONE ~ AIS_ROS_MODE_INNER_ZONE
    // nTxRxMode: TRXMODE_TXARXA_TXBRXB,...,
    // nTxRxModeBy : TRXMODE_BY_TRZONE..
    //---------------------------------------------------------------------

    const int nCurChA = m_pChPrimary->m_uChNumRx;
    const int nCurChB = m_pChSecondary->m_uChNumRx;

    BOOL bProcOK = FALSE;
    BOOL bGoToChChgPhase = FALSE;

    UINT16 uNewChATx, uNewChARx;
    UINT16 uNewChBTx, uNewChBRx;
    BOOL bChSetupChged = GetNewChToChg(nTxRxMode, nTxRxModeBy, uInputChIdA, uInputChIdB, &uNewChATx, &uNewChARx, &uNewChBTx, &uNewChBRx);
    BOOL bTrxModeChged = (nTxRxMode != CROSMgr::getInst()->m_sChSetup.nTxRxMode || nTxRxModeBy != CROSMgr::getInst()->m_sChSetup.nTxRxModeBy);

    BOOL bRunTrMode = CROSMgr::getInst()->IsTrMode(nRosMode);
    BOOL bChgRR = (fNewReportIntervalSec != CReportRateMgr::getInst()->m_fReportIntervalSec);

    if (bChSetupChged)
    {
        DEBUG_LOG("ProcRosModeJmp] RunPhaseChChg] SetupChged, chgTRmode: %d, chgRI: %d(%.1f), CH-1 : %d(%d),%d(%d) -> %d,%d, CH-2: %d(%d),%d(%d) -> %d,%d TRX: %d,%d -> %d,%d, curOp:%d,%d, prevOpMode:%d, newOpMode:%d(%d), s:%d\r\n",
            bRunTrMode, bChgRR, fNewReportIntervalSec,
            m_pChPrimary->m_uChNumTx, m_pChPrimary->IsTxAvailableCh(), m_pChPrimary->m_uChNumRx, m_pChPrimary->IsRxAvailableCh(), uNewChATx, uNewChARx,
            m_pChSecondary->m_uChNumTx, m_pChSecondary->IsTxAvailableCh(), m_pChSecondary->m_uChNumRx, m_pChSecondary->IsRxAvailableCh(), uNewChBTx, uNewChBRx,
            CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, nTxRxMode, nTxRxModeBy, m_nOpMode, m_nOpPhase, nPrevOpMode, nNewOpMode, nNewAssignedBy, cTimerSys::getInst()->GetCurTimerSec());

        if (nNewOpMode != OPMODE_ASSIGNED_SLOT && nNewAssignedBy != ASSIGNED_MODE_BY_MSG16 && CheckAssignedModeRunningByMsg16())                            // refer to ITU-R M.1371-5 4.1.5
        {
            WARNING_LOG("ProcRosModeJmp] ignore CH Setup, Assigned mode by MSG-16, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
            return FALSE;
        }

        if ((m_nOpMode == OPMODE_ASSIGNED_RR && m_bAssignedModeBy == ASSIGNED_MODE_BY_MSG23) && (nTxRxModeBy == TRXMODE_BY_MSG22_ADDRSD || nTxRxModeBy == TRXMODE_BY_MSG22_BRDCST))
        {
            //--------------------------------------------------------------------------------------------------------
            // Ivan Test report TT_16_6_7_1_Ed2.scn 에서 07:00 항목
            // 메시지-23에 의한 RR 할당모드 실행 중 ROS 에 의한 채널 설정 변경 요구시에는 자동모드로 돌아가도록 한다!
            //--------------------------------------------------------------------------------------------------------
            nNewOpMode = OPMODE_CONTINUOUS;
            nNewAssignedBy = ASSIGNED_MODE_NONE;

            INFO_LOG("ProcRosModeJmp] Reverts to Auto mode from Assigned, curOpMode:%d(%d), trxBy:%d, s:%d\r\n",
                    m_nOpMode, m_bAssignedModeBy, nTxRxModeBy, cTimerSys::getInst()->GetCurTimerSec());
        }

        if (m_nOpPhase <= OPPHASE_NETWORKENTRY)
        {
            m_pChPrimary->SetTxChannelNumber(uNewChATx);
            m_pChPrimary->SetRxChannelNumber(uNewChARx);
            m_pChSecondary->SetTxChannelNumber(uNewChBTx);
            m_pChSecondary->SetRxChannelNumber(uNewChBRx);
            CROSMgr::getInst()->SetTxRxMode(nTxRxMode, nTxRxModeBy);

            if (bChgRR)
            {
                CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);                                // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.
            }

            m_pNetworkEntryCH = NULL;
            m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(m_nOpMode);
            m_nNetworkEntryPrepSlot = SLOTID_NONE;

            SetOpMode(nNewOpMode, nNewAssignedBy, TRUE);
            SetOpPhase(OPPHASE_NETWORKENTRY);
            bProcOK = TRUE;
        }
        // Primary, Secondary 채널이 스위칭 변경된 경우
        else if (m_pChPrimary->IsTxAvailableCh() && m_pChSecondary->IsTxAvailableCh() && uNewChATx != AIS_CH_NUM_NONE && uNewChBTx != AIS_CH_NUM_NONE &&
                uNewChATx == m_pChSecondary->m_uChNumTx && uNewChBTx == m_pChPrimary->m_uChNumTx)
        {
            INFO_LOG("ProcRosModeJmp] RunPhaseChChg] ChSwitch, CH-1: %d -> %d, CH-2: %d -> %d, s:%d\r\n",
                    m_pChPrimary->m_uChNumTx, uNewChATx, m_pChSecondary->m_uChNumTx, uNewChBTx, cTimerSys::getInst()->GetCurTimerSec());

            CROSMgr::getInst()->SetTxRxMode(nTxRxMode, nTxRxModeBy);
            RunChannelSwitch();                    // Switch primary and secondary channel
            bProcOK = !bChgRR;
        }
        else
        {
            if ((m_pChPrimary->m_uChNumTx != AIS_CH_NUM_NONE && m_pChSecondary->m_uChNumTx != AIS_CH_NUM_NONE &&
                 ((uNewChATx != AIS_CH_NUM_NONE && uNewChBTx == AIS_CH_NUM_NONE) || (uNewChATx == AIS_CH_NUM_NONE && uNewChBTx != AIS_CH_NUM_NONE))) ||
                (uNewChATx != AIS_CH_NUM_NONE && m_pChPrimary->m_uChNumTx != AIS_CH_NUM_NONE && uNewChATx != m_pChPrimary->m_uChNumTx) ||
                (uNewChBTx != AIS_CH_NUM_NONE && m_pChSecondary->m_uChNumTx != AIS_CH_NUM_NONE && uNewChBTx != m_pChSecondary->m_uChNumTx) ||
                (m_pChPrimary->m_uChNumTx != AIS_CH_NUM_NONE && m_pChSecondary->m_uChNumTx == AIS_CH_NUM_NONE && uNewChATx == AIS_CH_NUM_NONE && uNewChBTx != AIS_CH_NUM_NONE) ||
                (m_pChPrimary->m_uChNumTx == AIS_CH_NUM_NONE && m_pChSecondary->m_uChNumTx != AIS_CH_NUM_NONE && uNewChATx != AIS_CH_NUM_NONE && uNewChBTx == AIS_CH_NUM_NONE))
                bGoToChChgPhase = TRUE;

             if (bGoToChChgPhase)
             {
                m_nChChgPhaseStartSlotID = CAisLib::FrameMapSlotIdAdd(OPSTATUS::nCurFrameMapSlotID, TX_CHECK_SLOT_SPACE);
                m_dwChChgPhaseStartTick  = SysGetSystemTimer();
                PrepFrameMapToChChgNet(TRUE, uNewChATx, uNewChBTx, m_nChChgPhaseStartSlotID);

                m_pChPrimary->SetTxChannelNumber(uNewChATx);
                m_pChPrimary->SetRxChannelNumber(uNewChARx);
                m_pChSecondary->SetTxChannelNumber(uNewChBTx);
                m_pChSecondary->SetRxChannelNumber(uNewChBRx);
                CROSMgr::getInst()->SetTxRxMode(nTxRxMode, nTxRxModeBy);

                // Assigned mode 진입시 채널과 보고율 모두 변경되는 경우 AutoMode 보고율과 Assigned mode 보고율 비교하여 적용.
                if (bChgRR)
                {
                    CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSec);
                }

                SetOpMode(nNewOpMode, nNewAssignedBy, TRUE);
                SetOpPhase(OPPHASE_CH_CHG_PHASE);
                bProcOK = TRUE;
             }
        }
    }

    if (bTrxModeChged && nTxRxModeBy == TRXMODE_BY_MSG22_ADDRSD)
        CROSMgr::getInst()->m_dwTRxModeStartSecByAddrMsg22 = cTimerSys::getInst()->GetCurTimerSec();

    if (bProcOK)
    {
        CROSMgr::getInst()->SetTrxModeChData(nTxRxMode, nTxRxModeBy, uInputChIdA, uInputChIdB);
        InitStaticReportSec();
    }
    else
    {
        //----------------------------------------------------------------------------------------------
        // 그외의 경우에는 모두 Change RR phase 실행한다!
        //----------------------------------------------------------------------------------------------

        DEBUG_LOG("ProcRosModeJmp] RunPhaseChChg] goto ChgRR, TRX: %d,%d -> %d,%d, CH-1: %d -> %d, CH-2: %d -> %d, RI:%.1f, prevOpMode:%d, newOpMode:%d,%d\r\n",
                CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, nTxRxMode, nTxRxModeBy,
                nCurChA, uInputChIdA, nCurChB, uInputChIdB, fNewReportIntervalSec, nPrevOpMode, nNewOpMode, nNewAssignedBy);
        bProcOK = RunPhaseChangeReportRate(nTxRxMode, nTxRxModeBy, uInputChIdA, uInputChIdB, fNewReportIntervalSec, nPrevOpMode, nNewOpMode, nNewAssignedBy);
    }

    if (!bProcOK)
    {
        WARNING_LOG("xxxxxxxxxxxxxxxxxxxxxxx ProcRosModeJmp] RunPhaseChChg] ignored, skip to setup, CH-1: %d -> %d, CH-2: %d -> %d, TRX: %d,%d -> %d,%d\r\n",
                nCurChA, uInputChIdA, nCurChB, uInputChIdB, CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, nTxRxMode, nTxRxModeBy);
    }

    return bChSetupChged;
}

void CLayerNetwork::ProcessPhaseChannelChange()
{
    DWORD dwChChgPhaseElapMs = SysGetDiffTimeMili(m_dwChChgPhaseStartTick);

    // 1분 동안 기존 송신 슬롯에서 마지막송신(타임아웃 0으로) 하고 1분 경과시 network entry phase 를 실행한다.
    if (dwChChgPhaseElapMs >= 50000 && CAisLib::FrameMapGetDiffSlotID(m_nChChgPhaseStartSlotID, OPSTATUS::nCurFrameMapSlotID) >= NUM_SLOT_PER_FRAME)
    {
        float fNewReportIntervalSecSO = CReportRateMgr::getInst()->CheckAutoModeReportInterval(FALSE, FALSE);    // VDL 네트워크 진입 후 수행될 보고주기를 여기서 결정
        
        // Assigned mode 진입시 채널과 보고율 모두 변경되는 경우 AutoMode 보고율과 Assigned mode 보고율 비교하여 적용.
        if (CheckAssignedModeRunning() && fNewReportIntervalSecSO < CReportRateMgr::getInst()->m_fReportIntervalSec)
            CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSecSO);                            // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.
        else
            CReportRateMgr::getInst()->RecalcReportInterval(CReportRateMgr::getInst()->m_fReportIntervalSec);            // SOTDMA 예약이 안되어있기때문에 바로 바꾸면 된다.

        DEBUG_LOG("ProcRosModeJmp] ProcPhaseChChg] end-end, goto NetEntry, TRX:%d, TRXby:%d, TRmode: %d(%d,%d), RI: %.1f\r\n",
                CROSMgr::getInst()->m_sChSetup.nTxRxMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, CROSMgr::getInst()->IsTrModeRunning(),
                CROSMgr::getInst()->m_sRosData.nRosMode, CROSMgr::getInst()->m_sChSetup.nTxRxModeBy, fNewReportIntervalSecSO);

        m_pNetworkEntryCH = NULL;
        m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(m_nOpMode);
        m_nNetworkEntryPrepSlot = SLOTID_NONE;
        SetOpPhase(OPPHASE_NETWORKENTRY);
    }
}

BOOL CLayerNetwork::ProcessSlotResync(INT16 nModemUtcSyncSec, int nModemSlotShift)
{
    static INT16 nOldModemSlotShift = 0;
    static INT16 nOldUtcSyncSec = 0;
    static DWORD dwOldSysTick = 0;

    BOOL bResynced = FALSE;

    m_bSlotTimeResynced = FALSE;
    if (nModemSlotShift != nOldModemSlotShift)
    {
        if (RunResync(nModemSlotShift-nOldModemSlotShift))
        {
            m_bSlotTimeResynced = TRUE;
        }

        nOldUtcSyncSec        = nModemUtcSyncSec;
        nOldModemSlotShift    = nModemSlotShift;
        bResynced = TRUE;
    }

    dwOldSysTick = SysGetSystemTimer();
    return bResynced;
}

void CLayerNetwork::CheckAndProcessSlotChg(INT16 nFrameID, INT16 nSlotID)
{
    static INT16 nOldFrameID = 0;
    static INT16 nOldSlotID = 0;

    if (nFrameID != nOldFrameID || nSlotID != nOldSlotID)
    {
        m_pChPrimary->ProcessSlotChangeTime_Ch(nFrameID != nOldFrameID);
        m_pChSecondary->ProcessSlotChangeTime_Ch(nFrameID != nOldFrameID);

        if (!CTestModeMgr::getInst()->IsTestModeRunning())
        {
            ProcessSlotChangeTime();
        }

        nOldFrameID = nFrameID;
        nOldSlotID  = nSlotID;
    }
}

void CLayerNetwork::ProcessRxMgr()
{
    if (m_bEnableRx)
    {
        m_pChPrimary->m_pVdlRxMgr->RunProcessRxMgr();
        m_pChSecondary->m_pVdlRxMgr->RunProcessRxMgr();
    }
}

void CLayerNetwork::ProcessSlotChangeTime()
{
    switch(m_nOpPhase)
    {
     case OPPHASE_ONBOOT_WAITSYNC:
        {
            // Wait for 55 seconds or until UTC direct sync is done
            if (   (SysGetDiffTimeScnd(m_dwOpPhaseStartTick) > 55)
                || (CSyncMgr::getInst()->IsUtcDirectSyncRunning() && m_bSlotTimeResynced))
            {
                SetOpPhase(OPPHASE_MONITOR_VDL);
                OPSTATUS::dwRcvClearSec = cTimerSys::getInst()->GetCurTimerSec();
            }
        }
        break;
    case OPPHASE_MONITOR_VDL:
        {
            // Only receive vdl message for 1 minute
            if (SysGetDiffTimeScnd(m_dwOpPhaseStartTick) > 60)
            {
                if (CVdlTxMgr::IsTxAvailable())
                {
                    float fNewReportIntervalSecSO = CReportRateMgr::getInst()->CheckAutoModeReportInterval(FALSE, FALSE);
                    CReportRateMgr::getInst()->RecalcReportInterval(fNewReportIntervalSecSO);

                    if (CReportRateMgr::getInst()->IsRIValueValid(fNewReportIntervalSecSO))
                    {
                        m_pNetworkEntryCH = NULL;
                        m_nNetworkEntryPrevPosMsg = CAisLib::GetScheduledPosReportMsgID(m_nOpMode);
                        m_nNetworkEntryPrepSlot = SLOTID_NONE;
                        SetOpPhase(OPPHASE_NETWORKENTRY);
                    }
                }
            }

            if (!OPSTATUS::bRunSyncProcess && SysGetDiffTimeScnd(m_dwOpPhaseStartTick) > 50)
            	OPSTATUS::bRunSyncProcess = TRUE;
        }
        break;
    case OPPHASE_NETWORKENTRY:
        {
            // IEC-61993-2 14.4 Initialization period "Confirm that the EUT starts transmissions within 2 min after switch on"
            // 초기화 시간 : "EUT 는 전원인가 후 2분 내에 송신을 시작해야한다."
            if (RunPhaseNetworkEntry())                        // 송신을 위한 최초 슬롯선택, 최초 송신 특별위치보고를 위한 준비
            {
                if (CReportRateMgr::getInst()->IsReportRateForSOTDMA())
                    SetOpPhase(OPPHASE_FIRST_FRAME);
                else
                {
                    SetOpPhase(OPPHASE_ROUTINE);
                }
            }
        }
        break;
    case OPPHASE_FIRST_FRAME:
        CheckBothChSetOpPhase(OPPHASE_ROUTINE);
        break;
    case OPPHASE_ROUTINE:
        break;

    case OPPHASE_CHG_RR_PREP_FIRST:
        if (CheckBothChSetOpPhase(OPPHASE_CHG_RR_PREP_SECOND))
        {
            if (CReportRateMgr::getInst()->m_bTempUseReportByITDMA)
            {
                //-----------------------------------------------------------------------
                // 보고주기 관련 변수값들이 모두 바뀌었으므로
                // 현재 "일시적인 ITDMA 에 의한 속도 증가" 동작중이면 다시 계산해야 한다!
                //-----------------------------------------------------------------------
                CReportRateMgr::getInst()->SetTempUseReportByITDMA(FALSE);
                CReportRateMgr::getInst()->SetTempUseReportByITDMA(TRUE);
            }
        }
        break;
    case OPPHASE_CHG_RR_PREP_SECOND:
        if (CheckBothChSetOpPhase(OPPHASE_ROUTINE))
        {
        }
        break;
    case OPPHASE_CH_CHG_PHASE:
        ProcessPhaseChannelChange();
        break;
    case OPPHASE_DIAGNOSTIC:
        break;
    default:
        break;
    }
}

void CLayerNetwork::RunPeriodicallyLayerNetwork()
{
    static DWORD dwCheckSec = 0;

    if (cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) > 0)
    {
        ProcessTxMsg24B();

        m_pChPrimary->RunPeriodicallyCh();
        m_pChSecondary->RunPeriodicallyCh();

        CSyncMgr::getInst()->RunPeriodically();
        CReportRateMgr::getInst()->RunPeriodicallyReportRateMgr();
        CROSMgr::getInst()->RunPeriodically();

        if (!OPSTATUS::bEnableCalcNumRcvSt && cTimerSys::getInst()->GetTimeDiffSec(OPSTATUS::dwRcvClearSec) > 60)
        {
        	OPSTATUS::bEnableCalcNumRcvSt = TRUE;
        }

        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
}
