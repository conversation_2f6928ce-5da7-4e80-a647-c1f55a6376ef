#include <string.h>
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "ComLib.h"
#include "AisLib.h"
#include "Ship.h"
#include "SensorMgr.h"
#include "SetupMgr.h"
#include "SetupMgr.h"
#include "Timer.h"
#include "GnssInternal.h"
#include "VdlTxMgr.h"
#include "GPIOExt.h"
#include "SyncMgr.h"
#include "LayerNetwork.h"
#include "LayerPhysical.h"
#include "SysOpStatus.h"
#include "EventLogMgr.h"

BOOL CEventLogMgr::m_bCheckEventLogDone = FALSE;

CEventLogMgr::CEventLogMgr()
{
    m_pLogData = (EVENTLOG_DATA*)SysAllocMemory(sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA);

    memset(m_pLogData, 0, sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA);

    CAisLib::SetDefaultSysDateTime(&m_sPowerOffTime);
    m_bPowerOffTimeUTC = FALSE;

    CAisLib::SetDefaultSysDateTime(&m_sMMSINullTime);
    m_bMMSINullTimeUTC = FALSE;

    CAisLib::SetDefaultSysDateTime(&m_sRxOnlyStartTime);
    m_bRxOnlyStartTimeUTC = FALSE;

    CAisLib::SetDefaultSysDateTime(&m_sTxMulFuncStartTime);
    m_bTxMulFuncStartTimeUTC = FALSE;

    m_bCheckEventLogDone = FALSE;
}

CEventLogMgr::~CEventLogMgr()
{
}

void CEventLogMgr::ResetEventLogData(EVENTLOG_DATA *pLogData)
{
    CAisLib::SetDefaultSysDateTime(&(pLogData->sEventTime));
    CAisLib::SetDefaultSysDateTime(&(pLogData->sEventEndTime));

    pLogData->sEventTime.nValid = FALSE;
    pLogData->bEventTimeUTC    = FALSE;
    pLogData->nEventLogID    = 0;
    pLogData->nDurationMin    = 0;
    pLogData->nLogFlag        = FALSE;
}

EVENTLOG_DATA *CEventLogMgr::AddEventLogData(int nEventLogID, EVENTLOG_DATA *pLogData, int nIndexToSave)
{
    if(nIndexToSave >= 0)
    {
        m_pLogData[nIndexToSave] = *pLogData;
        return &m_pLogData[nIndexToSave];
    }

    memmove(&m_pLogData[1], &m_pLogData[0], sizeof(EVENTLOG_DATA)*(EVENTLOG_NUM_LOGDATA-1));

    m_pLogData[0]                = *pLogData;
    m_pLogData[0].nEventLogID    = nEventLogID;
    m_pLogData[0].nLogFlag        = TRUE;

    if(!pLogData->sEventTime.nValid)
    {
        if(cShip::getOwnShipInst()->xSysTime.nValid)
        {
            m_pLogData[0].sEventTime = cShip::getOwnShipInst()->xSysTime;
            m_pLogData[0].bEventTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_pLogData[0].sEventTime);
            m_pLogData[0].bEventTimeUTC = FALSE;
        }
    }

    if(!pLogData->sEventEndTime.nValid)
    {
        if(cShip::getOwnShipInst()->xSysTime.nValid)
        {
            m_pLogData[0].sEventEndTime = cShip::getOwnShipInst()->xSysTime;
            m_pLogData[0].bEventTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_pLogData[0].sEventEndTime);
            m_pLogData[0].bEventTimeUTC = FALSE;
        }
    }

    return &(m_pLogData[0]);
}

int CEventLogMgr::FindEventLog(int nEventLogID, SYS_DATE_TIME *pEventStartTime)
{
    for(int i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
    {
        if(m_pLogData[i].nEventLogID == nEventLogID && !memcmp(&m_pLogData[i].sEventTime, pEventStartTime, sizeof(SYS_DATE_TIME)))
            return i;
    }
    return -1;
}

BOOL CEventLogMgr::CheckAvailableLogData(EVENTLOG_DATA *pLogData)
{
    return (ALRID_SECURITYLOG_MIN <= pLogData->nEventLogID && pLogData->nEventLogID <= ALRID_SECURITYLOG_MAX) &&
            (pLogData->bEventTimeUTC == TRUE || pLogData->bEventTimeUTC == FALSE) &&
            (pLogData->nLogFlag == 0 || pLogData->nLogFlag == 1);
}

int CEventLogMgr::SerializeEventLog(UCHAR *pBackData)
{
    int    nSize;
    UCHAR  *pTemp;

    pTemp = pBackData;

    // Data header
    memcpy(pTemp, SETUP_HEADER, SETUP_HEADER_LEN);
    pTemp += SETUP_HEADER_LEN;

    memmove(pTemp, &m_sPowerOffTime,    sizeof(m_sPowerOffTime));        pTemp += sizeof(m_sPowerOffTime);
    memmove(pTemp, &m_bPowerOffTimeUTC,    sizeof(m_bPowerOffTimeUTC));    pTemp += sizeof(m_bPowerOffTimeUTC);
    memmove(pTemp, m_pLogData,            sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA ); pTemp += sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA;

    // Checksum
    DWORD *pdwChecksum = (DWORD*)pTemp;
    nSize = pTemp - pBackData;
    *pdwChecksum = GetCrc32(pBackData, nSize);
    nSize += SETUP_CHECKSUM_LEN;

    return nSize;
}

bool CEventLogMgr::LoadEventLogData(UCHAR *pBackData)
{
    UCHAR  *pTemp;

    pTemp = pBackData;
    pTemp += SETUP_HEADER_LEN;

    memmove(&m_sPowerOffTime,    pTemp,    sizeof(m_sPowerOffTime));                        pTemp += sizeof(m_sPowerOffTime);
    memmove(&m_bPowerOffTimeUTC,pTemp,     sizeof(m_bPowerOffTimeUTC));                    pTemp += sizeof(m_bPowerOffTimeUTC);
    memmove(m_pLogData,            pTemp, sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA  ); pTemp += sizeof(EVENTLOG_DATA) * EVENTLOG_NUM_LOGDATA;

    DEBUG_LOG("LoadSetupData-EventLog] PwrOffTm: utc:%d, %d, %d-%d-%d %02d:%02d:%02d\r\n",
            m_bPowerOffTimeUTC, m_sPowerOffTime.nValid, m_sPowerOffTime.xDate.nYear, m_sPowerOffTime.xDate.nMon, m_sPowerOffTime.xDate.nDay,
            m_sPowerOffTime.xTime.nHour, m_sPowerOffTime.xTime.nMin, m_sPowerOffTime.xTime.nSec);

    VerifyEventLogData();

    return true;
}

void  CEventLogMgr::VerifyEventLogData(void)
{
    if(!CAisLib::IsValidAisSysDateTime(&m_sPowerOffTime))
    {
        CAisLib::SetDefaultSysDateTime(&m_sPowerOffTime);
        m_bPowerOffTimeUTC = FALSE;
    }

    if(m_bPowerOffTimeUTC != TRUE && m_bPowerOffTimeUTC != FALSE)
        m_bPowerOffTimeUTC = FALSE;

    for(int i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
    {
        if(!CheckAvailableLogData(&m_pLogData[i]))
        {
            ResetEventLogData(&m_pLogData[i]);
        }
        if(!CAisLib::IsValidAisSysDateTime(&(m_pLogData[i].sEventTime)))
        {
            CAisLib::SetDefaultSysDateTime(&(m_pLogData[i].sEventTime));
            m_pLogData[i].bEventTimeUTC = FALSE;
        }
    }
}

void  CEventLogMgr::ClearEventLogData(void)
{
    for(int i = 0 ; i < EVENTLOG_NUM_LOGDATA ; i++)
        ResetEventLogData(&m_pLogData[i]);

    CAisLib::SetDefaultSysDateTime(&m_sPowerOffTime);
    m_bPowerOffTimeUTC = FALSE;
}

BOOL CEventLogMgr::CheckEventLogValidMMSI()
{
#ifdef __DEBUG_TEST_EVENTLOG__
    const int MMSI_INVALID_CHECKTIME_SEC = 10;
#else
    const int MMSI_INVALID_CHECKTIME_SEC = EVENTLOG_CHECKTIMESEC;
#endif

    static BOOL bOldStat = FALSE;
    static DWORD dwCheckSec = 0;

    static DWORD dwEventOccurSec = 0;
    static DWORD dwEventSaveSec = 0;

    BOOL bUpdateFlash = FALSE;

    if(!CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)    // GPS fix 기다리기위해 초기 시간 대기 (Event log 발생 시간 기록을 위해)
        return FALSE;

    if(!cShip::getOwnShipInst()->xSysTime.nValid)
        return FALSE;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) < 1)
        return FALSE;
    dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();

    BOOL bEventOccur = !CAisLib::IsValidMMSI_MobileSt(cShip::getOwnShipInst()->xStaticData.dMMSI);

    if(bEventOccur == bOldStat)
    {
        if(bEventOccur && cTimerSys::getInst()->GetTimeDiffSec(dwEventOccurSec) > MMSI_INVALID_CHECKTIME_SEC)
        {
            if(cTimerSys::getInst()->GetTimeDiffSec(dwEventSaveSec) > 30)
            {
                bUpdateFlash = TRUE;
                EVENTLOG_DATA sEventLog = {0};
                int nEventIndex = FindEventLog(ALRID_SECURITYLOG_MMSI_INVALID, &m_sMMSINullTime);
                if(nEventIndex >= 0)
                    sEventLog = m_pLogData[nEventIndex];

                int nDiffSec = CAisLib::GetDiffTimeSeconds(&(cShip::getOwnShipInst()->xSysTime), &m_sMMSINullTime);
                sEventLog.nEventLogID    = ALRID_SECURITYLOG_MMSI_INVALID;
                sEventLog.sEventTime    = m_sMMSINullTime;
                sEventLog.sEventEndTime = cShip::getOwnShipInst()->xSysTime;
                sEventLog.bEventTimeUTC = m_bMMSINullTimeUTC;
                sEventLog.nDurationMin  = nDiffSec / 60;
                sEventLog.nMMSI         = cShip::getOwnShipInst()->xStaticData.dMMSI;

                dwEventSaveSec = cTimerSys::getInst()->GetCurTimerSec();
                EVENTLOG_DATA *pLogData = AddEventLogData(ALRID_SECURITYLOG_MMSI_INVALID, &sEventLog, nEventIndex);

                INFO_LOG("EventLog] Invalid MMSI,Add, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                    nEventIndex, cShip::getOwnShipInst()->xStaticData.dMMSI, dwEventOccurSec,
                    pLogData->nLogFlag, pLogData->nEventLogID,
                    pLogData->sEventTime.nValid, pLogData->sEventTime.xDate.nYear, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nDay,
                    pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec, pLogData->bEventTimeUTC,
                    cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                    cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                    nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
            }
        }
    }
    else
    {
        if(bEventOccur)
        {
            m_sMMSINullTime        = cShip::getOwnShipInst()->xSysTime;
            m_bMMSINullTimeUTC    = cShip::getOwnShipInst()->bSysTimeUTC;

            dwEventOccurSec = cTimerSys::getInst()->GetCurTimerSec();

            DEBUG_LOG("EventLog] Invalid MMSI,StartEvent, %09d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, s:%d\r\n",
                cShip::getOwnShipInst()->xStaticData.dMMSI,
                m_sMMSINullTime.nValid, m_sMMSINullTime.xDate.nYear, m_sMMSINullTime.xDate.nMon, m_sMMSINullTime.xDate.nDay,
                m_sMMSINullTime.xTime.nHour, m_sMMSINullTime.xTime.nMin, m_sMMSINullTime.xTime.nSec,
                m_bMMSINullTimeUTC, dwEventOccurSec);
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_sMMSINullTime);
            m_bMMSINullTimeUTC = FALSE;

            INFO_LOG("EventLog] Invalid MMSI,FinishEvent, %09d, s:%d\r\n",
                    cShip::getOwnShipInst()->xStaticData.dMMSI, cTimerSys::getInst()->GetCurTimerSec());
        }
    }

    bOldStat = bEventOccur;
    return bUpdateFlash;
}

BOOL CEventLogMgr::CheckEventLogRxOnlyMode()
{
    const int RXONLYMODE_CHECKTIME_SEC = EVENTLOG_CHECKTIMESEC;
    static BOOL bOldStat = FALSE;
    static DWORD dwCheckSec = 0;
    static DWORD dwEventOccurSec = 0;
    static DWORD dwCheckEventChgSec = 0;

    BOOL bUpdateFlash = FALSE;

    // GPS fix 기다리기위해 초기 시간 대기 (Evnet log 발생 시간 기록을 위해)
    if(!CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)
        return FALSE;

    if(cShip::getOwnShipInst()->xStaticData.dMMSI == AIS_AB_MMSI_NULL)
        return FALSE;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) < 1)
        return FALSE;
    dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();

    DWORD dwRxOnlyTimeSec = cTimerSys::getInst()->GetTimeDiffSec(CLayerNetwork::m_dwPosReportLastTxSec);
    BOOL bEventOccur = dwRxOnlyTimeSec > RXONLYMODE_CHECKTIME_SEC;

    if(bEventOccur == bOldStat)
    {
        if(bEventOccur)
        {
            if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckEventChgSec) > 10)
            {
                bUpdateFlash = TRUE;
                dwCheckEventChgSec = cTimerSys::getInst()->GetCurTimerSec();
            }
        }
    }
    else
    {
        if(bEventOccur)
        {
            m_sRxOnlyStartTime        = cShip::getOwnShipInst()->xSysTime;
            m_bRxOnlyStartTimeUTC    = cShip::getOwnShipInst()->bSysTimeUTC;

            dwEventOccurSec    = cTimerSys::getInst()->GetCurTimerSec();
            bUpdateFlash    = TRUE;

            DEBUG_LOG("EventLog] RX only,StartEvent, %09d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, s:%d\r\n",
                cShip::getOwnShipInst()->xStaticData.dMMSI,
                m_sRxOnlyStartTime.nValid, m_sRxOnlyStartTime.xDate.nYear, m_sRxOnlyStartTime.xDate.nMon, m_sRxOnlyStartTime.xDate.nDay,
                m_sRxOnlyStartTime.xTime.nHour, m_sRxOnlyStartTime.xTime.nMin, m_sRxOnlyStartTime.xTime.nSec,
                m_bRxOnlyStartTimeUTC, dwEventOccurSec);
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_sRxOnlyStartTime);
            m_bRxOnlyStartTimeUTC = FALSE;

            DEBUG_LOG("EventLog] RX only,FinishEvent, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        }
    }

    if(bUpdateFlash)
    {
        EVENTLOG_DATA sEventLog = {0};
        EVENTLOG_DATA sOldEventLog;
        int nEventIndex = FindEventLog(ALRID_SECURITYLOG_RXONLYMODE, &m_sRxOnlyStartTime);
        if(nEventIndex >= 0)
        {
            sEventLog = m_pLogData[nEventIndex];
            sOldEventLog = m_pLogData[nEventIndex];
        }

        int nDiffSec = CAisLib::GetDiffTimeSeconds(&(cShip::getOwnShipInst()->xSysTime), &m_sRxOnlyStartTime) + RXONLYMODE_CHECKTIME_SEC;
        sEventLog.nEventLogID   = ALRID_SECURITYLOG_RXONLYMODE;
        sEventLog.sEventTime    = m_sRxOnlyStartTime;
        sEventLog.sEventEndTime = cShip::getOwnShipInst()->xSysTime;
        sEventLog.bEventTimeUTC = m_bRxOnlyStartTimeUTC;
        sEventLog.nDurationMin  = nDiffSec / 60;

        if(nEventIndex >= 0 && sEventLog.nDurationMin == sOldEventLog.nDurationMin)
        {
            bUpdateFlash = FALSE;

            DEBUG_LOG("EventLog] RX only,Skip Update, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                nEventIndex, cShip::getOwnShipInst()->xStaticData.dMMSI, dwEventOccurSec,
                sEventLog.nLogFlag, sEventLog.nEventLogID,
                sEventLog.sEventTime.nValid, sEventLog.sEventTime.xDate.nYear, sEventLog.sEventTime.xDate.nMon, sEventLog.sEventTime.xDate.nDay,
                sEventLog.sEventTime.xTime.nHour, sEventLog.sEventTime.xTime.nMin, sEventLog.sEventTime.xTime.nSec, sEventLog.bEventTimeUTC,
                cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
        }

        if(bUpdateFlash)
        {
            EVENTLOG_DATA *pLogData = AddEventLogData(ALRID_SECURITYLOG_RXONLYMODE, &sEventLog, nEventIndex);

            DEBUG_LOG("EventLog] RX only,Add,Update, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                nEventIndex, cShip::getOwnShipInst()->xStaticData.dMMSI, dwEventOccurSec,
                pLogData->nLogFlag, pLogData->nEventLogID,
                pLogData->sEventTime.nValid, pLogData->sEventTime.xDate.nYear, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nDay,
                pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec, pLogData->bEventTimeUTC,
                cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
        }
    }

    bOldStat = bEventOccur;
    return bUpdateFlash;
}

BOOL CEventLogMgr::CheckEventLogTxMulFunc()
{
    const int TXMULFUNC_CHECKTIME_SEC = EVENTLOG_CHECKTIMESEC;
    static BOOL bOldStat = FALSE;
    static DWORD dwCheckSec = 0;
    static DWORD dwEventOccurSec = 0;
    static DWORD dwEventSaveSec = 0;

    BOOL bUpdateFlash = FALSE;

    // GPS fix 기다리기위해 초기 시간 대기 (Evnet log 발생 시간 기록을 위해)
    if(!CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) && cTimerSys::getInst()->GetTimeDiffSec(0) < WAIT_TIMESEC_INT_GNSS)
        return FALSE;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) < 1)
        return FALSE;
    dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();

    BOOL bEventOccur = CLayerPhysical::getInst()->IsTxHwShutdownOccurred();

    if(bEventOccur == bOldStat)
    {
        if(bEventOccur && cTimerSys::getInst()->GetTimeDiffSec(dwEventOccurSec) > TXMULFUNC_CHECKTIME_SEC)
        {
            if(cTimerSys::getInst()->GetTimeDiffSec(dwEventSaveSec) > 30)
            {
                bUpdateFlash = TRUE;
                EVENTLOG_DATA sEventLog = {0};
                int nEventIndex = FindEventLog(ALRID_SECURITYLOG_TX_SHUTDOWN, &m_sTxMulFuncStartTime);
                if(nEventIndex >= 0)
                    sEventLog = m_pLogData[nEventIndex];

                int nDiffSec = CAisLib::GetDiffTimeSeconds(&(cShip::getOwnShipInst()->xSysTime), &m_sTxMulFuncStartTime);
                sEventLog.nEventLogID    = ALRID_SECURITYLOG_TX_SHUTDOWN;
                sEventLog.sEventTime    = m_sTxMulFuncStartTime;
                sEventLog.sEventEndTime = cShip::getOwnShipInst()->xSysTime;
                sEventLog.bEventTimeUTC = m_bTxMulFuncStartTimeUTC;
                sEventLog.nDurationMin  = nDiffSec / 60;

                EVENTLOG_DATA *pLogData = AddEventLogData(ALRID_SECURITYLOG_TX_SHUTDOWN, &sEventLog, nEventIndex);
                dwEventSaveSec = cTimerSys::getInst()->GetCurTimerSec();

                DEBUG_LOG("EventLog] TX mulfunc,Add, index: %d, %09d, startSec: %d, Valid:%d, eventID:%d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, to %d,%d-%d-%d %02d:%02d:%02d, duSec: %d,min:%d, s:%d\r\n",
                    nEventIndex, cShip::getOwnShipInst()->xStaticData.dMMSI, dwEventOccurSec,
                    pLogData->nLogFlag, pLogData->nEventLogID,
                    pLogData->sEventTime.nValid, pLogData->sEventTime.xDate.nYear, pLogData->sEventTime.xDate.nMon, pLogData->sEventTime.xDate.nDay,
                    pLogData->sEventTime.xTime.nHour, pLogData->sEventTime.xTime.nMin, pLogData->sEventTime.xTime.nSec, pLogData->bEventTimeUTC,
                    cShip::getOwnShipInst()->xSysTime.nValid, cShip::getOwnShipInst()->xSysTime.xDate.nYear, cShip::getOwnShipInst()->xSysTime.xDate.nMon, cShip::getOwnShipInst()->xSysTime.xDate.nDay,
                    cShip::getOwnShipInst()->xSysTime.xTime.nHour, cShip::getOwnShipInst()->xSysTime.xTime.nMin, cShip::getOwnShipInst()->xSysTime.xTime.nSec,
                    nDiffSec, sEventLog.nDurationMin, cTimerSys::getInst()->GetCurTimerSec());
            }
        }
    }
    else
    {
        if(bEventOccur)
        {
            m_sTxMulFuncStartTime        = cShip::getOwnShipInst()->xSysTime;
            m_bTxMulFuncStartTimeUTC    = cShip::getOwnShipInst()->bSysTimeUTC;

            dwEventOccurSec = cTimerSys::getInst()->GetCurTimerSec();
            DEBUG_LOG("EventLog] TX mulfunc,StartEvent, %09d, %d, %d-%d-%d %02d:%02d:%02d,U:%d, s:%d\r\n",
                cShip::getOwnShipInst()->xStaticData.dMMSI,
                m_sTxMulFuncStartTime.nValid, m_sTxMulFuncStartTime.xDate.nYear, m_sTxMulFuncStartTime.xDate.nMon, m_sTxMulFuncStartTime.xDate.nDay,
                m_sTxMulFuncStartTime.xTime.nHour, m_sTxMulFuncStartTime.xTime.nMin, m_sTxMulFuncStartTime.xTime.nSec,
                m_bTxMulFuncStartTimeUTC, dwEventOccurSec);
        }
        else
        {
            CAisLib::SetDefaultSysDateTime(&m_sTxMulFuncStartTime);
            m_bTxMulFuncStartTimeUTC = FALSE;

            DEBUG_LOG("EventLog] TX mulfunc,FinishEvent, s:%d\r\n", cTimerSys::getInst()->GetCurTimerSec());
        }
    }

    bOldStat = bEventOccur;
    return bUpdateFlash;
}

BOOL CEventLogMgr::CheckPowerOffTime(void)
{
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
    // refer to IEC-61993-2 6.6 Event log
    // A security mechanism shall be provided to detect disabling of the AIS and to prevent unauthorised alteration of input or transmitted data.
    // To protect the unauthorised dissemination of data, the IMO guidelines (IMO Resolution MSC.43(64), Guidelines for Ship Reporting Systems) shall be followed.
    // Means shall be provided to automatically record all periods when the AIS installation is nonfunctioning,
    // for instance when the power is switched off, when the AIS is in receive only mode or not transmitting for other reasons, as follows.
    // The last 10 times when the equipment was non-functioning for more than 15 min shall be recorded, in UTC time and nDurationMin,
    // in a non-volatile memory. Means shall be provided to recover this data. It shall not be possible for the user to alter any information recorded in this memory.
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------------

    BOOL bUpdateSetupData = FALSE;

    if(!m_bCheckEventLogDone)
    {
        if(!CAisLib::IsValidAisSysDateTime(&(m_sPowerOffTime)) ||
            (!CSensorMgr::getInst()->IsUtcTimeValid(SENSORID_0) && OPSTATUS::bFirstFrameOnBootDone && cTimerSys::getInst()->GetTimeDiffSec(0) > WAIT_TIMESEC_INT_GNSS))        // GPS fix 기다리기위해 초기 시간 대기 (Evnet log 발생 시간 기록을 위해)
        {
            m_bCheckEventLogDone = TRUE;

            DEBUG_LOG("EventLog] CheckPwrOffTime, old PwrOffTime invalid, utc:%d, %d, %d-%d-%d %02d:%02d:%02d, s:%d\r\n",
                m_bPowerOffTimeUTC, m_sPowerOffTime.nValid,
                m_sPowerOffTime.xDate.nYear, m_sPowerOffTime.xDate.nMon, m_sPowerOffTime.xDate.nDay,
                m_sPowerOffTime.xTime.nHour, m_sPowerOffTime.xTime.nMin, m_sPowerOffTime.xTime.nSec,
                cTimerSys::getInst()->GetCurTimerSec());
        }
        else if(cShip::getOwnShipInst()->xSysTime.nValid)
        {
            EVENTLOG_DATA *pLogData;
            int nDiffSec;

            SYS_DATE_TIME sSysTime = cShip::getOwnShipInst()->xSysTime;
            if((nDiffSec = CAisLib::GetDiffTimeSeconds(&sSysTime, &(m_sPowerOffTime))) >= EVENTLOG_CHECKTIMESEC)        // 15 min
            {
                EVENTLOG_DATA sEventLog = {0};
                sEventLog.sEventTime    = m_sPowerOffTime;
                sEventLog.sEventEndTime = sSysTime;
                sEventLog.bEventTimeUTC = m_bPowerOffTimeUTC;
                sEventLog.nDurationMin  = nDiffSec / 60;
                pLogData = AddEventLogData(ALRID_SECURITYLOG_POWEROFF, &sEventLog);
                bUpdateSetupData = TRUE;
            }

            m_bCheckEventLogDone = TRUE;
        }
    }
    return bUpdateSetupData;
}

BOOL CEventLogMgr::SavePowerOffTime()
{
    static DWORD dwCheckSec = 0;
    static DWORD dwSaveSec = 0;

    BOOL bUpdateSetupData = FALSE;

    if(cTimerSys::getInst()->GetTimeDiffSec(dwCheckSec) >= 10)    // 10 sec
    {
        if(cTimerSys::getInst()->GetTimeDiffSec(dwSaveSec) >= 60) // 1 min
        {
            if(m_bCheckEventLogDone)
            {
                if(cShip::getOwnShipInst()->xSysTime.nValid)
                {
                    m_sPowerOffTime = cShip::getOwnShipInst()->xSysTime;
                    m_bPowerOffTimeUTC = cShip::getOwnShipInst()->bSysTimeUTC;
                    bUpdateSetupData = TRUE;
                }
                else
                {
                    if(m_sPowerOffTime.nValid)
                    {
                        CAisLib::SetDefaultSysDateTime(&(m_sPowerOffTime));
                        m_bPowerOffTimeUTC = FALSE;
                        bUpdateSetupData = TRUE;
                    }
                }

                dwSaveSec = cTimerSys::getInst()->GetCurTimerSec();
            }
        }
        dwCheckSec = cTimerSys::getInst()->GetCurTimerSec();
    }
    return bUpdateSetupData;
}

void CEventLogMgr::RunPeriodicallyEventLog(void)
{
    BOOL bUpdateSetup = FALSE;

    bUpdateSetup |= CheckPowerOffTime();
    bUpdateSetup |= SavePowerOffTime();

    bUpdateSetup |= CheckEventLogValidMMSI();
    bUpdateSetup |= CheckEventLogRxOnlyMode();
    bUpdateSetup |= CheckEventLogTxMulFunc();

    if(bUpdateSetup) {
        CSetupMgr::getInst()->ReserveToSaveLogConfigData();
    }
}
