#ifndef __BUILTINTESTMGR_H__
#define __BUILTINTESTMGR_H__

#include "DataType.h"
#include "AllConst.h"

// IEC61993-2 Built-in test equipment
// The AIS shall be equipped with BIIT to implement BITE. These tests shall run continuously or
// at appropriate intervals simultaneously with the standard functions of the equipment. These
// tests shall be run whenever software has been updated (see 5.3).
// If any failure or malfunction is detected that will significantly reduce integrity or stop operation
// of the AIS, an alert is initiated.
class CBuiltInTestMgr
{
public:
    CBuiltInTestMgr();
    ~CBuiltInTestMgr();

    static std::shared_ptr<CBuiltInTestMgr> getInst() {
        static std::shared_ptr<CBuiltInTestMgr> pInst = std::make_shared<CBuiltInTestMgr>();
        return pInst;
    }

public:
    void    SetROMError(BOOL bError);
    void    SetEEPROMError(BOOL bError);

    BOOL    IsErrorROM();
    BOOL    IsErrorEEPROM();
    BOOL    IsVswrFail();
    BOOL    IsTxMalFunction();
    BOOL    IsRxMalFunctionCH1();
    BOOL    IsRxMalFunctionCH2();
    BOOL    IsRxMalFunctionChDSC();
    WORD    GetDcVoltageAdcData();
    BOOL    IsDcVoltageFail();

protected:
    BOOL    m_bErrorROM;
    BOOL    m_bErrorEEPROM;
};

#endif//__BUILTINTESTMGR_H__
