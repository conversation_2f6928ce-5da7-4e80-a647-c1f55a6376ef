#ifndef __LAYERNETWORK_H__
#define __LAYERNETWORK_H__

#include "DataType.h"
#include "AllConst.h"
#include "ChannelMgr.h"

class CLayerNetwork
{
public:
    CLayerNetwork();
    ~CLayerNetwork();

    static std::shared_ptr<CLayerNetwork> getInst() {
        static std::shared_ptr<CLayerNetwork> pInst = std::make_shared<CLayerNetwork>();
        return pInst;
    }

public:
    void    Initialize();
    void    EnableRx(BOOL bEnable);
    BOOL    IsRandomTimeoutRequired();
    BOOL    IsPhaseTxAvailable();
    BOOL    IsAssignedModeOpAvailable();
    BOOL    IsRosChgOpAvailable();
    BOOL    IsChangingReportRateAvailable();
    BOOL    IsHigherOrEqualAssignedRR(CChannelMgr *pCh, float fAssignedRIfor2Ch);
    BOOL    IsSilentMode();

    void    SetOpMode(BYTE bOpMode, BYTE bAssignedModeBy, BOOL bSetAllCh);
    void    SetOpPhase(BYTE bOpPhase, BOOL bSetAllCh=TRUE);
    BOOL    CheckBothChSetOpPhase(BYTE bOpPhase);
    BOOL    CheckAssignedModeRunning();
    BOOL    CheckAssignedModeRunningByMsg16();
    BOOL    CheckAssignedModeRunningByMsg23();
    INT8    GetNewFrameTimeOut();

    CChannelMgr*    GetChPtr(UINT8 uChID);
    CChannelMgr*    GetChPrimary();
    CChannelMgr*    GetChSecondary();
    CChannelMgr*    GetOppositeChPtr(CChannelMgr *pCh);
    CChannelMgr*    GetOppositeTxChPtr(CChannelMgr *pCh);
    CChannelMgr*    GetNextTxChPtrForMsg(UINT16 uMsgID);
    CChannelMgr*    GetNextPosTxCH();
    CChannelMgr*    GetNextPosTxCH(int nNewTxCh1, int nNewTxCh2);

    void    SetLastScheduledPosReportTime(CChannelMgr *pChannel);

    BOOL    IsMasterChChgRR(CChannelMgr *pChannel);
    void    InitStaticReportSec();
    void    InitLongRangeReportSec();
    void    ChangeMMSI(UINT uNewMMSI);
    BOOL    RunSyncronize(int nShiftSlot);
    BOOL    RunPhaseNetworkEntry();
    int     GetNewReportRateStartNS();
    void    PrepFrameMapToChChgNet(BOOL bPrepToChChg, UINT uNewChIdA, UINT uNewChIdB, int nChChgPhaseStartSlotID=SLOTID_NONE);
    BOOL    RunPhaseChangeReportRate(int nTxRxMode, int nTxRxModeBy, UINT uChannelIdA, UINT uChannelIdB, float fNewReportIntervalSec, int nPrevOpMode, int nNewOpMode, int NewAssignedBy);
    void    RunReturnToAutoModeFromAssignedMode_RR();
    void    RunReturnFromAssignedMode_Slot(CChannelMgr *pChannel, int nOpModeToReturn, int nAssignedModeBy=ASSIGNED_MODE_NONE, float fNewReportIntervalSec=-1);
    void    RunReturnToAutoMode();
    void    RunChannelSwitch();

    void    ProcStaticVoyageDataChanged();

    BOOL    CheckTxBuffUnscheduledTxMsg(int nMsgID);
    BOOL    ReserveTxStaticVoyageMsg(CChannelMgr *pTxCh, BOOL bCheckExsitingTxBuff, int nTimeOutSlot);

    void    ProcessTxMsg24B();
    BOOL    ReserveTxMsg24B(CChannelMgr *pTxCh);

    void    InitChSetupNetLayer(CH_SETUP *psChSetup);
    BOOL    GetNewChToChg(INT8 nTxRxMode, INT8 nTxRxModeBy, UINT16 uInputChIdA, UINT16 uInputChIdB, UINT16 *puNewChATx, UINT16 *puNewChARx, UINT16 *puNewChBTx, UINT16 *puNewChBRx);
    BOOL    RunChannelSetup(int nRosMode, INT8 nTxRxMode, INT8 nTxRxModeBy, UINT16 uChannelIdA, UINT16 uChannelIdB, float fNewReportIntervalSec, int nPrevOpMode, int nNewOpMode, int nNewAssignedBy);
    void    ProcessPhaseChannelChange();

    void    StartAssignedMode_ReportRate(UINT uBaseStMMSI, BYTE bAssignedModeBy, int nTxRxMode, int nTxRxModeBy, float fReportIntervalSec);
    void    StartAssignedMode_Slot(CChannelMgr *pChannel, int nMsgID, UINT uBaseStMMSI, WORD wRcvFrameID, WORD wRcvSlotID, WORD wOffset, WORD wIncrement);

    BOOL    ProcessSlotSync(INT16 nModemUtcSyncSec, int nModemSlotShift);
    void    CheckAndProcessSlotChg(INT16 nFrameID, INT16 nSlotID);
    void    ProcessSlotChangeTime();
    void    ProcessRxMgr();

    void    RunPeriodicallyLayerNetwork();

public:
    BOOL    m_bEnableRx;
    BOOL    m_bSlotTimeSynced;

    INT8    m_nOpMode;                              // OPMODE_CONTINUOUS ~ OPMODE_ASSIGNED_SLOT
    INT8    m_nOpPhase;                             // OPPHASE_MONITOR_VDL ~ OPPHASE_CHG_RR_PREP_FIRST

    int     m_nNetworkEntryPrepSlot;
    int     m_nNetworkEntryPrevPosMsg;
    CChannelMgr *m_pNetworkEntryCH;

    BOOL    m_bNetEntryFromExisting;

    BYTE    m_bAssignedModeBy;

    int     m_nAssignedRRStartSlot;

    INT16   m_nChChgPhaseStartSlotID;
    DWORD   m_dwChChgPhaseStartTick;

    DWORD   m_dwOpPhaseStartTick;

    INT8    m_nNewFrameTimeOut;

    int     m_nChgRRphasePrevOpMode;
    int     m_nChgRRphasePrevPosMsg;

    UINT    m_uSlotAssignedModeBaseStMMSI;          // 할당모드 수행 시 할당명령 송신한 기지국 MMSI
    UINT    m_uChannelManageModeBaseStMMSI;         // 채널 관리모드 수행 시 송신한 기지국 MMSI

    CChannelMgr *m_pChPrimary;                      // AIS primary channel
    CChannelMgr *m_pChSecondary;                    // AIS secondary channel

    static  CChannelMgr  *m_pLastTxCH;
    static  DWORD         m_dwPosReportLastTxSec;
    static  CChannelMgr  *m_pPosReportLastTxCH;
    static  SYS_DATE_TIME m_sPorReportLastTxDateTime;

    static  CChannelMgr  *m_pLastRoutineCH;
    static  CChannelMgr  *m_pLastTxChMsg5;
    static  DWORD         m_dwLastTxSecMsg5;
    static  CChannelMgr  *m_pLastTxChMsg27;
    static  DWORD         m_dwLastTxSecMsg27;
    static  CChannelMgr  *m_pChgRRMasterCH;

    static  DWORD         m_dwCntTxSecMsg24;
    static  DWORD         m_dwLastTxSecMsg24;
    static  CChannelMgr  *m_pLastTxChMsg24;

    static  DWORD         m_dwRcvSecMsg23;
};

#endif//__LAYERNETWORK_H__
