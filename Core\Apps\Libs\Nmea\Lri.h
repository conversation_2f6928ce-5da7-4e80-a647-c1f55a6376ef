/**
 * @file    Lri.h
 * @brief   Lri header file
 * <AUTHOR>
 * @version 0.1
 * @date    2025-06-04
 * 
 * @copyright Copyright (c) 2025
 */

#include "DataType_AIS.h"
#include "Sentence.h"

#ifndef __LRI_H__
#define __LRI_H__

/******************************************************************************
*
* LRI - Long Range Interrogation
*
* $--LRI,x,a,xxxxxxxxx,xxxxxxxxx,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a*hh<CR><LF>
*        | | |         |         |       | |        | |       | |        |
*        1 2 3         4         5       6 7        8 9      10 11       12
*
* 1.     Sequence Number , 0 to 9
* 2.     Control Flag
* 3.     MMSI of "requester"
* 4.     MMSI of "destination"
* 5.6.   Latitude  - N/S (north-east co-ordinate)
* 7.8.   Longitude - E/W (north-east co-ordinate)
* 9.10.  Latitude  - N/S (south-west co-ordinate)
* 11.12. Longitude - E/W (south-west co-ordinate)
*
******************************************************************************/
class CLri : public CSentence
{
public:
    CLri();

    /**
     * @brief Parse the sentence
     * @param pszSentence The sentence to be parsed
     * @return True if the sentence is parsed successfully, false otherwise
     */
    static bool Parse(const char *pszSentence);

    /**
     * @brief Get the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t GetSequentialId(void) { return m_nSequentialId; }

    /**
     * @brief Increment the sequential message identifier
     * @return The sequential message identifier
     */
    static int8_t IncSequentialId(void)
    {
        if (m_nSequentialId >= 9)   m_nSequentialId = 0;
        else                        m_nSequentialId++;

        return m_nSequentialId;
    };

    /**
     * @brief Make the sentence
     * @param pszSentence The sentence to be made
     * @return The length of the sentence
     */
    static int32_t MakeSentence(char *pszSentence);

public:
    static int8_t m_nSequentialId;
    char     m_chControlFlag;
    uint32_t m_dwMMSIReq;
    uint32_t m_dwMMSIDest;
    int32_t  m_nLatNE;
    int32_t  m_nLonNE;
    int32_t  m_nLatSW;
    int32_t  m_nLonSW;
};

#endif /* __LRI_H__ */

