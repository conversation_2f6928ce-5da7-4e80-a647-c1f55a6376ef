/**
 * @file    Pll.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#include <memory>
#include "SysConst.h"
#include "GPIO.h"
#include "SPI.h"

#ifndef  __PLL_H__
#define  __PLL_H__

//=================================================================
#define  PLL_NO_0                   (0)
#define  PLL_NO_1                   (1)
#define  PLL_NO_2                   (2)
//-----------------------------------------------------------------
#define  PLL_AIS_RX1_OFFSET_FREQ    (-21700000)
#define  PLL_AIS_RX2_OFFSET_FREQ    ( 10700000)
#define  PLL_AIS_TXx_OFFSET_FREQ    (-72000000)
#define  PLL_DSC_RX3_OFFSET_FREQ    (-21700000)
//-----------------------------------------------------------------
#define  PLL_OSC1_IN_FREQ           (26000000)
#define  PLL_OSC2_IN_FREQ           (26000000)
#define  PLL_OSC3_IN_FREQ           (26000000)
//-----------------------------------------------------------------
#define  PLL_AIS_CH1_OUT_FREQ       (161975000) // CH1 (87)
#define  PLL_AIS_CH2_OUT_FREQ       (162025000) // CH2 (88)
#define  PLL_DSC_CH3_OUT_FREQ       (156525000) // CH3 (70)
//-----------------------------------------------------------------
#define  PLL_INT_VCO_DEFT           (4800000)   // 4.8GHz / 1000
//-----------------------------------------------------------------
#define LMX2571_WORD_SIZE           2           // 2Byte (WORD)
#define LMX2571_MAX_VCO_DIV         28
#define LMX2571_MAX_DEN             16777215    // Maximum denominator value (2^24 - 1)

#define LMX2571_DEFT_RX_PWR		    (31)
#define LMX2571_DEFT_TX_PWR		    (31)

//-----------------------------------------------------------------
#define REG_R60 0b00111100
#define REG_R58 0b00111010
#define REG_R53 0b00110101
#define REG_R47 0b00101111
#define REG_R46 0b00101110
#define REG_R42 0b00101010
#define REG_R41 0b00101001
#define REG_R40 0b00101000
#define REG_R39 0b00100111
#define REG_R35 0b00100011
#define REG_R34 0b00100010
#define REG_R33 0b00100001
#define REG_R32 0b00100000
#define REG_R31 0b00011111
#define REG_R30 0b00011110
#define REG_R29 0b00011101
#define REG_R28 0b00011100
#define REG_R27 0b00011011
#define REG_R26 0b00011010
#define REG_R25 0b00011001
#define REG_R24 0b00011000
#define REG_R23 0b00010111
#define REG_R22 0b00010110
#define REG_R21 0b00010101
#define REG_R20 0b00010100
#define REG_R19 0b00010011
#define REG_R18 0b00010010
#define REG_R17 0b00010001
#define REG_R16 0b00010000
#define REG_R15 0b00001111
#define REG_R14 0b00001110
#define REG_R13 0b00001101
#define REG_R12 0b00001100
#define REG_R11 0b00001011
#define REG_R10 0b00001010
#define REG_R09 0b00001001
#define REG_R08 0b00001000
#define REG_R07 0b00000111
#define REG_R06 0b00000110
#define REG_R05 0b00000101
#define REG_R04 0b00000100
#define REG_R03 0b00000011
#define REG_R02 0b00000010
#define REG_R01 0b00000001
#define REG_R00 0b00000000

//-----------------------------------------------------------------------------
// LMX2571 Synthesizer registers
//-----------------------------------------------------------------------------
typedef union
{
    uint16_t Data;
    struct {
        uint16_t LD_EN      :1; // Enables lock detect function. 0 = Disabled 1 = Enabled
        uint16_t RSV_1      :2;
        uint16_t SDO_LD_SEL :1; // the MUXout pin. 0 = Register readback serial data output 1 = Lock detect output
        uint16_t RSV_2      :12;
    };
} tREG39;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t OUTBUF_RX_TYPE  :1;  // 0 = Open drain 1 = Push pull
        uint16_t OUTBUF_TX_TYPE  :1;  // 0 = Open drain 1 = Push pull
        uint16_t OUTBUF_AUTOMUTE :1;  // 0 = Disabled   1 = Enabled
        uint16_t MULT_WAIT       :11; // 0 = Do not use this setting 1 = 1 OSCin clock cycle ...2047 = 2047 OSCin clock cycles
        uint16_t RSV             :2;
    };
} tREG35;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t FSK_MODE_SEL1     :1;
        uint16_t FSK_MODE_SEL0     :1;
        uint16_t FSK_DEV_SEL       :3;
        uint16_t FSK_LEVEL         :2;
        uint16_t FSK_I2S_CLK_POL   :1;
        uint16_t FSK_I2S_FS_POL    :1;
        uint16_t RSV               :1;
        uint16_t XTAL_EN           :1;
        uint16_t XTAL_PWRCTRL      :3;
        uint16_t IPBUF_SE_DIFF_SEL :1;
        uint16_t IPBUFDIFF_TERM    :1;
    };
} tREG34;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t OUTBUF_TX_PWR_F2  :5;
        uint16_t EXTVCO_SEL_F2     :1;
        uint16_t EXTVCO_CHDIV_F2   :4;
        uint16_t FSK_EN_F2         :1;
        uint16_t RSV               :5;
    };
} tREG24;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t LF_R4_F2          :3;
        uint16_t RSV_1             :3;
        uint16_t OUTBUF_RX_EN_F2   :1;
        uint16_t OUTBUF_TX_EN_F2   :1;
        uint16_t OUTBUF_RX_PWR_F2  :5;
        uint16_t RSV_2             :3;
    };
} tREG23;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t MULT_F2           :5;
        uint16_t PFD_DELAY_F2      :3;
        uint16_t CHDIV1_F2         :2;
        uint16_t CHDIV2_F2         :3;
        uint16_t LF_R3_F2          :3;
    };
} t_REG22;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_R_PRE_F2      :8;
        uint16_t PLL_R_F2          :8;
    };
} tREG21;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_N_F2          :12;
        uint16_t FRAC_ORDER_F2     :3;
        uint16_t PLL_N_PRE_F2      :1;
    };
} tREG20;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_DEN_F2        :16;
    };
} tREG19;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_NUM_F2        :16;
    };
} tREG18;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_NUM_F2        :8;
        uint16_t PLL_DEN_F2        :8;
    };
} tREG17;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t OUTBUF_TX_PWR_F1  :5;
        uint16_t EXTVCO_SEL_F1     :1;
        uint16_t EXTVCO_CHDIV_F1   :4;
        uint16_t FSK_EN_F1         :1;
        uint16_t RSV               :5;
    };
} tREG8;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t LF_R4_F1          :3;
        uint16_t RSV_1             :3;
        uint16_t OUTBUF_RX_EN_F1   :1;
        uint16_t OUTBUF_TX_EN_F1   :1;
        uint16_t OUTBUF_RX_PWR_F1  :5;
        uint16_t RSV_2             :3;
    };
} tREG7;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t MULT_F1           :5;
        uint16_t PFD_DELAY_F1      :3;
        uint16_t CHDIV1_F1         :2;
        uint16_t CHDIV2_F1         :3;
        uint16_t LF_R3_F1          :3;
    };
} tREG6;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_R_PRE_F1      :8;
        uint16_t PLL_R_F1          :8;
    };
} tREG5;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_N_F1          :12;
        uint16_t FRAC_ORDER_F1     :3;
        uint16_t PLL_N_PRE_F1      :1;
    };
} tREG4;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_DEN_F1        :16;
    };
} tREG3;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_NUM_F1        :16;
    };
} tREG2;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t PLL_NUM_F1        :8;
        uint16_t PLL_DEN_F1        :8;
    };
} tREG1;

typedef union
{
    uint16_t Data;
    struct {
        uint16_t FCAL_EN           :1;
        uint16_t RSV_1             :5;
        uint16_t F1F2_SEL          :1;
        uint16_t F1F2_MODE         :1;
        uint16_t F1F2_CTRL         :1;
        uint16_t F1F2_INIT         :1;
        uint16_t RXTX_POL          :1;
        uint16_t RXTX_CTRL         :1;
        uint16_t POWERDOWN         :1;
        uint16_t RESET             :1;
        uint16_t RSV_2             :2;
    };
} tREG0;


typedef struct
{
    uint16_t    IntVcoDiv;
    uint8_t     SEG1;
    uint8_t     SEG2;
} tVCODIV;

static tVCODIV xVcoDiv[LMX2571_MAX_VCO_DIV] = {
    {   4, 0, 0 },
    {   5, 1, 0 },
    {   6, 2, 0 },
    {   7, 3, 0 },
    {   8, 0, 1 },
    {  10, 1, 1 },
    {  12, 2, 1 },
    {  14, 3, 1 },
    {  16, 0, 2 },
    {  20, 1, 2 },
    {  24, 2, 2 },
    {  28, 3, 2 },
    {  32, 0, 3 },
    {  40, 1, 3 },
    {  48, 2, 3 },
    {  56, 3, 3 },
    {  64, 0, 4 },
    {  80, 1, 4 },
    {  96, 2, 4 },
    { 112, 3, 4 },
    { 128, 0, 5 },
    { 160, 1, 5 },
    { 192, 2, 5 },
    { 224, 3, 5 },
    { 256, 0, 6 },
    { 320, 1, 6 },
    { 384, 2, 6 },
    { 448, 3, 6 },
};

//=============================================================================
class cPll
{
public:
    cPll(void);
    virtual ~cPll(void);

public:
    virtual void    SetRxFrequency(int nFrequency) = 0;  // Rx
    virtual void    SetTxFrequency(int nFrequency) = 0;  // Tx

    virtual void    SetRxOutPower(int nPower) = 0;
    virtual void    SetTxOutPower(int nPower) = 0;

    virtual void    SetRxOutEnable(BOOL nEnable) = 0;
    virtual void    SetTxOutEnable(BOOL nEnable) = 0;

protected:
    int             m_nPllIdx;
    DWORD           m_dChipSelP;
    GPIO_TypeDef*   m_pChipGPIO;
    cSpiSYS*        m_pSPI;

    int             m_nCurrSetRFFreq;

    BOOL            m_bRxEnable;
    BOOL            m_bTxEnable;

    int             m_nRxPower;         // 0 ~ 31
    int             m_nTxPower;         // 0 ~ 31

    int             m_nRFxOutFreq[2];
    int             m_nRFxOffFreq;
};

class CPllMgr : public cPll
{
public:
    CPllMgr(int nPllIdx, GPIO_TypeDef *pChipGPIO, DWORD dChipSelect, int nRxOutFreq, int nTxOutFreq, int nChOffFreq);
    virtual ~CPllMgr(void);

    static std::shared_ptr<CPllMgr> getInstPLL1() {
        static std::shared_ptr<CPllMgr> pInstPLL1 = std::make_shared<CPllMgr>(PLL_NO_0,
                                                                            GPIOJ,
                                                                            GPIO_DDS1_LE,
                                                                            PLL_AIS_CH1_OUT_FREQ,
                                                                            PLL_AIS_CH1_OUT_FREQ,
                                                                            PLL_AIS_RX1_OFFSET_FREQ
                                                                            );
        return pInstPLL1;
    }

    static std::shared_ptr<CPllMgr> getInstPLL2() {
        static std::shared_ptr<CPllMgr> pInstPLL2 = std::make_shared<CPllMgr>(PLL_NO_1,
                                                                            GPIOJ,
                                                                            GPIO_DDS2_LE,
                                                                            PLL_AIS_CH2_OUT_FREQ,
                                                                            PLL_AIS_CH2_OUT_FREQ,
                                                                            PLL_AIS_RX2_OFFSET_FREQ
                                                                            );
        return pInstPLL2;
    }

    static std::shared_ptr<CPllMgr> getInstPLL3() {
        static std::shared_ptr<CPllMgr> pInstPLL3 = std::make_shared<CPllMgr>(PLL_NO_2,
                                                                            GPIOJ,
                                                                            GPIO_DDS3_LE,
                                                                            PLL_DSC_CH3_OUT_FREQ,
                                                                            PLL_DSC_CH3_OUT_FREQ,
                                                                            PLL_DSC_RX3_OFFSET_FREQ
                                                                            );
        return pInstPLL3;
    }

public:
    enum
    {
        PLL_RX = 0,
        PLL_TX = 1
    };

    void  InitLMX2571(void);
    void  SetResetPLL(void);
    void  SetXtalEnable(void);
    void  SetLockDetect(void);
    void  WriteRegData(uint8_t nAddr, uint16_t *pTxData, uint16_t nLen);

    int   GetRFOutFrequency();
    int   GetRxFrequency();
    int   GetTxFrequency();
    void  SetChFrequency(int nChNo, int nFrequency);

    void  SetRxFrequency(int nFrequency);
    void  SetTxFrequency(int nFrequency);

    void  SetRxOutPower(int nPower);
    void  SetTxOutPower(int nPower);

    void  SetRxOutEnable(BOOL nEnable);
    void  SetTxOutEnable(BOOL nEnable);
};

//=============================================================================
#endif /*__PLL_H__*/
