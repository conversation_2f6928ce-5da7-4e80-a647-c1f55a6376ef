#ifndef __CHVDLTXMGR_H__
#define __CHVDLTXMGR_H__

#include "DataType.h"
#include "AllConst.h"
#include "AisLib.h"
#include "SyncMgr.h"
#include "VdlRxMgr.h"
#include "ChannelMgr.h"

class CChannelMgr;

class CVdlTxMgr : public CAisLib
{
public:
    CVdlTxMgr(CChannelMgr *pChannel);
    ~CVdlTxMgr();

public:
    enum _tagConsts
    {
        MAX_NUM_TX_PER_FRAME_MSG15 = 5,
        MAX_NUM_TX_PER_FRAME_BINMSG = 20,        // 1분간 전송할 수 있는 RATDMA Slot 수.
    };

public:
                    void    InitAllTxBuff(void);
                    void    ClearUnScheTxMsgInfo(TDMA_UNSCHE_INFO *pTxBuff);
                    void    ResetAllBinUnscheBroadBuff(void);
                    bool    SetUnScheTxMsgDataBuff(UINT16 nTxMsgID, bool bCheckExsitingTxBuff, int nSizeSI, int nTimeOutSlot=RATDMA_SLOT_TIMEOUT);
        TDMA_UNSCHE_INFO*   GetUnscheduledTxBuffList(UINT16 nTxMsgID);
        TDMA_UNSCHE_INFO*   GetUnscheduledEmptyTxBuffPtr(UINT16 nTxMsgID);
        TDMA_UNSCHE_INFO*   GetUnscheduledUsedTxBuffPtr(UINT16 nTxMsgID);

                    void    ClearMsg15Buff(TDMA_UNSCHE15_INFO *pTxBuff);
                    void    ResetAllMsg15Buff(void);
                    void    SetUnScheTxMsg15Info(TDMA_UNSCHE15_INFO *pTxBuff, UINT8 bAirType, UINT uMMSI1, int bMsgID11, int bMsgID12, UINT uMMSI2, int bMsgID21);
      TDMA_UNSCHE15_INFO*   GetEmptyMsg15BuffPtr(void);

                    void    ClearBinMsgBuff_Broadcast(TDMA_UNSCHE_BROADMSG *pTxBuff);
                    void    ResetAllBinBroadBuff(void);
                    void    SetUnScheBroadTxMsgInfo(TDMA_UNSCHE_BROADMSG *broad_info, UINT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot);
    TDMA_UNSCHE_BROADMSG*   GetBinMsgBuffListBroadcast(int nTxMsgID);
    TDMA_UNSCHE_BROADMSG*   GetBinMsgEmptyBuffBroadcast(int nTxMsgID, int nTxSeqNum);

                    void    ClearBinMsgBuff_Addressed(TDMA_UNSCHE_ADDRMSG *pTxBuff);
                    void    ResetAllAddrMsgBuff(void);
                    void    SetUnScheAddrTxMsgInfo(TDMA_UNSCHE_ADDRMSG *addr_info, UINT32 uDestMMSI, UINT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot);
    TDMA_UNSCHE_ADDRMSG*    GetAddrMsgBuffPtr(UINT16 uMsgID);
    TDMA_UNSCHE_ADDRMSG*    GetBinMsgBuffPtr_Addressed(UINT uDestMMSI, int nTxMsgID, int nTxSeqNum);
    TDMA_UNSCHE_ADDRMSG*    GetAddrMsgTxInfoByUniqueID(UINT16 nTxMsgID, UINT uDestMMSI, UINT8 nTxSeqNum);

                    void    ClearAckTxMsgInfo(TDMA_UNSCHE_ACKMSG *ack_msg);
                    void    ResetAllAckMsgBuff();
                    void    SetAckMsgBuffPtr(TDMA_UNSCHE_ACKMSG *ack_msg, WORD wRespStartSI, UINT uDestMMSI, UINT16 nTxMsgID, UINT8 nTxSeqNum);
    TDMA_UNSCHE_ACKMSG*     GetAckMsgBuffList(UINT16 nTxMsgID);
    TDMA_UNSCHE_ACKMSG*     GetAckMsgEmptyBuff(UINT16 nTxMsgID);

                    void    ClearMsg25Buff(TDMA_UNSCHE_MSG25 *pTxBuff);    
                    void    ResetAllMsg25Buff();
                    void    SetTxMsg25Info(TDMA_UNSCHE_MSG25 *pTxBuff, UINT32 uDestMMSI, INT16 nABMMsgID, INT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot, BYTE bBinDataFlag);
    TDMA_UNSCHE_MSG25*      GetMsg25BuffPtr(UINT uDestMMSI, int nTxMsgID, int nTxSeqNum);

                    void    ClearMsg26Buff(TDMA_UNSCHE_MSG26 *pTxBuff);
                    void    ResetAllMsg26Buff();
                    void    SetTxMsg26Info(TDMA_UNSCHE_MSG26 *pTxBuff, UINT32 uDestMMSI, INT16 nBBMMsgID, INT16 nTxMsgID, BYTE nTxSeqNum, BYTE uNumTxSlot, BYTE bBinDataFlag);
    TDMA_UNSCHE_MSG26*      GetMsg26BuffPtr(UINT uDestMMSI, int nTxMsgID, int nTxSeqNum);

                    void    ClearOneSlotAddrTxMsgInfo(TDMA_ONESLOT_ADDRMSG *ack_msg);
                    void    ResetAllOneSlotAddrMsgBuff();
                    void    SetOneSlotAddrTxMsgInfo(TDMA_ONESLOT_ADDRMSG *pTxBuff, UINT uDestMMSI, UINT16 nAirMsgID, UINT16 nTxMsgID);
    TDMA_ONESLOT_ADDRMSG*   GetOneSlotAddrTxMsgBuffList(UINT16 nTxMsgID);
    TDMA_ONESLOT_ADDRMSG*   GetOneSlotAddrTxMsgEmptyBuff(UINT16 nTxMsgID);
    TDMA_ONESLOT_ADDRMSG*   GetOneSlotAddrTxMsgByUniqueID(UINT16 nTxMsgID, UINT uDestMMSI);

                    void    SetTxSlotIdBinMsg(BYTE nSentenceType, int uNumTxSlot);
                    bool    CheckTxCntOkForBinMsg(BYTE nSentenceType, int uNumTxSlot);

    bool    ProcessUnscheduledOneSlotBroadTxMsg(TDMA_UNSCHE_INFO *pBuff, BYTE nBuffIdx);
    void    PeriodicUnscheduledOneSlotBroadTxMsg(TDMA_UNSCHE_INFO *pBuff);
    bool    ProcessUnscheduledOneSlotAddrTxMsg(TDMA_ONESLOT_ADDRMSG *pBuff);
    void    PeriodicUnscheduledOneSlotAddrTxMsg(TDMA_ONESLOT_ADDRMSG *pBuffList);
    bool    ProcessUnscheduledBroadcastTxMsg(TDMA_UNSCHE_BROADMSG *pBuff);
    void    PeriodicUnscheduledBroadcastTxMsg(TDMA_UNSCHE_BROADMSG *pBuff);
    bool    ProcessUnscheduledAddressedTxMsg(TDMA_UNSCHE_ADDRMSG *pBuff);
    void    PeriodicUnscheduledAddressedTxMsg(TDMA_UNSCHE_ADDRMSG *pBuff);
    bool    ProcessUnscheduledAckTxMsg(TDMA_UNSCHE_ACKMSG *pBuff);
    void    PeriodicUnscheduledAckTxMsg(TDMA_UNSCHE_ACKMSG *pBuff);

    bool    ProcessUnscheduledTxBuffMsg03(void);
    void    PeriodicUnscheduledTxBuffMsg03(void);
    bool    ProcessUnscheduledTxBuffMsg05(void);
    void    PeriodicUnscheduledTxBuffMsg05(void);
    bool    ProcessUnscheduledTxBuffMsg06(void);
    void    PeriodicUnscheduledTxBuffMsg06(void);
    bool    ProcessUnscheduledTxBuffMsg07(void);
    void    PeriodicUnscheduledTxBuffMsg07(void);
    bool    ProcessUnscheduledTxBuffMsg08(void);
    void    PeriodicUnscheduledTxBuffMsg08(void);
    bool    ProcessUnscheduledTxBuffMsg10(void);
    void    PeriodicUnscheduledTxBuffMsg10(void);
    bool    ProcessUnscheduledTxBuffMsg11(void);
    void    PeriodicUnscheduledTxBuffMsg11(void);
    bool    ProcessUnscheduledTxBuffMsg12(void);
    void    PeriodicUnscheduledTxBuffMsg12(void);
    bool    ProcessUnscheduledTxBuffMsg13(void);
    void    PeriodicUnscheduledTxBuffMsg13(void);
    bool    ProcessUnscheduledTxBuffMsg14(void);
    void    PeriodicUnscheduledTxBuffMsg14(void);
    bool    ProcessUnscheduledTxBuffMsg15(void);
    void    ProcessRespForMsg15(int nMMSI, int nAckMsgID);
    void    PeriodicUnscheduledTxBuffMsg15(void);
    bool    ProcessUnscheduledTxBuffMsg24(void);
    void    PeriodicUnscheduledTxBuffMsg24(void);
    bool    ProcessUnscheduledTxBuffMsg25(void);
    void    PeriodicUnscheduledTxBuffMsg25(void);
    bool    ProcessUnscheduledTxBuffMsg26(void);
    void    PeriodicUnscheduledTxBuffMsg26(void);

    void    ProcessUnscheduledTxBuffer(void);
    void    ProcessFrameChg_TxMgr(void);

    static  void    ClearReserveTx(CVdlTxMgr *pVdlTxMgr, UINT16 nTxMsgID, BYTE nDataIdx);

    static  BYTE    GetSyncStateData(void);
    static  DWORD   GetPosDimensionData(void);
    static  DWORD   GetCommStateData_SOTDMA(CChannelMgr *pChannel, WORD wMapSlotID);
    static  DWORD   GetCommStateData_ITDMA(CChannelMgr *pChannel, WORD wMapSlotID);

    static  bool    IsTxMsgWaitingAck(CVdlTxMgr *pVdlTxMgr, FRAMEMAP_SLOTDATA *pSlot);

    static  int     MakeAisTxSlotMsg_01_02_03(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxMsg_01_02_03(CChannelMgr *pChannel, WORD wMapSlotID, UINT16 uMsgID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg05(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg06(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg_07_13(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg08(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg_10(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg11(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg12(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg14(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg15(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg18(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg19(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg24_PartA(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg24_PartB(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg25(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg26(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisTxSlotMsg27(CChannelMgr *pChannel, WORD wMapSlotID, BYTE *pTxSlotData);
    static  int     MakeAisMsgRandomPRBS(BYTE *pTxSlotData);
    static  int     MakeAisMsgFixedPRBS(BYTE *pTxSlotData);
    static  bool    MakeVdlTxMsg(CChannelMgr *pChannel, WORD wSlotID);

    static  bool    CheckTxCntOkForMsg15();

#ifdef __ENABLE_CHECK_MSG15_CNT__
    static  int     IncMsg15TxCnt();
    static  int     ResetMsg15TxCnt();
#else
    static  void    SetTxSlotIdMsg15(int nMapSlotID);
    static  void    CheckLogNumTxSlotMsg15();
#endif

    static  void    CheckLogNumTxSlotBinMsg();

    static  bool    IsTxStatMalfunction();
    static  bool    IsTxAvailable();

public:
    CChannelMgr          *m_pChannel;

    TDMA_UNSCHE_INFO     *m_pMsgList_03;        // 비주기적인 Msg 03 - RATDMA (Interrogation)
    TDMA_UNSCHE_INFO     *m_pMsgList_05;        // 비주기적인 Msg 05
    TDMA_UNSCHE_INFO     *m_pMsgList_11;        // 비주기적인 Msg 11 - RATDMA (Inquire)
    TDMA_UNSCHE_INFO     *m_pMsgList_24;        // 비주기적인 Msg 24 - RATDMA (Inquire)

    TDMA_UNSCHE15_INFO   *m_pMsgList_15;

    TDMA_UNSCHE_BROADMSG *m_pMsgList_08;
    TDMA_UNSCHE_BROADMSG *m_pMsgList_14;

    TDMA_UNSCHE_ADDRMSG  *m_pMsgList_06;        // Msg 6  (동시에 Max 4개까지 처리, Retry)
    TDMA_UNSCHE_ADDRMSG  *m_pMsgList_12;        // Msg 12 (동시에 Max 4개까지 처리, Retry)

    TDMA_UNSCHE_ACKMSG   *m_pMsgList_07;        // Msg 07(0~3) - 1 Slot으로 4개까지 응답
    TDMA_UNSCHE_ACKMSG   *m_pMsgList_13;        // Msg 13(0~3) - Addr Msg 한개당, 7 or 13으로 한개씩 응답 처리하도록 추천(Ivan)

    TDMA_ONESLOT_ADDRMSG *m_pMsgList_10;        // Msg 07(0~3) - 1 Slot으로 4개까지 응답

    TDMA_UNSCHE_MSG25    *m_pMsgList_25;        // Msg 25
    TDMA_UNSCHE_MSG26    *m_pMsgList_26;        // Msg 26

    BYTE                 *m_pTxSlotBuff;

#ifdef __ENABLE_CHECK_MSG15_CNT__
    static    int        m_nTxMsg15CntFrame;
#else
    static    int        *m_pTxSlotLogMsg15;
    static    int        m_nTxSlotLogHeadMsg15;
    static    int        m_nTxSlotLogTailMsg15;
#endif

    static    int        *m_pTxSlotLogBinMsg;
    static    int        m_nTxSlotLogHeadBinMsg;
    static    int        m_nTxSlotLogTailBinMsg;

    static    DWORD      m_dwSendSecVDO;
};
#endif//__CHVDLTXMGR_H__
